{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-line-progress/u-line-progress.vue?1054", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-line-progress/u-line-progress.vue?37ee", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-line-progress/u-line-progress.vue?674f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-line-progress/u-line-progress.vue?213c", "uni-app:///components/uview-ui/components/u-line-progress/u-line-progress.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-line-progress/u-line-progress.vue?8523", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-line-progress/u-line-progress.vue?6843"], "names": ["name", "mixins", "data", "lineWidth", "watch", "percentage", "computed", "progressStyle", "style", "innserPercentage", "mounted", "methods", "init", "uni", "getProgressWidth", "resizeProgressWidth", "width", "size"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACyL;AACzL,gBAAgB,iLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0B9uB;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACA;MACA;IACA;IACAC;MAEA;IAWA;IACAC;MAAA;MACA;QACA,IACAC,QACAC,KADAD;QAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAq3C,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAz4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-line-progress/u-line-progress.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-line-progress.vue?vue&type=template&id=2f487391&scoped=true&\"\nvar renderjs\nimport script from \"./u-line-progress.vue?vue&type=script&lang=js&\"\nexport * from \"./u-line-progress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-line-progress.vue?vue&type=style&index=0&id=2f487391&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f487391\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-line-progress/u-line-progress.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line-progress.vue?vue&type=template&id=2f487391&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  var g0 = _vm.$u.addUnit(_vm.height)\n  var s1 = _vm.__get_style([_vm.progressStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line-progress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line-progress.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t    class=\"u-line-progress\"\n\t    :style=\"[$u.addStyle(customStyle)]\"\n\t>\n\t\t<view\n\t\t    class=\"u-line-progress__background\"\n\t\t    ref=\"u-line-progress__background\"\n\t\t    :style=\"[{\n\t\t\t\tbackgroundColor: inactiveColor,\n\t\t\t\theight: $u.addUnit(height),\n\t\t\t}]\"\n\t\t>\n\t\t</view>\n\t\t<view\n\t\t    class=\"u-line-progress__line\"\n\t\t    :style=\"[progressStyle]\"\n\t\t> \n\t\t\t<slot>\n\t\t\t\t<text v-if=\"showText && percentage >= 10\" class=\"u-line-progress__text\">{{innserPercentage + '%'}}</text>\n\t\t\t</slot> \n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t// #ifdef APP-NVUE\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * lineProgress 线型进度条\n\t * @description 展示操作或任务的当前进度，比如上传文件，是一个线形的进度条。\n\t * @tutorial https://www.uviewui.com/components/lineProgress.html\n\t * @property {String}\t\t\tactiveColor\t\t激活部分的颜色 ( 默认 '#19be6b' )\n\t * @property {String}\t\t\tinactiveColor\t背景色 ( 默认 '#ececec' )\n\t * @property {String | Number}\tpercentage\t\t进度百分比，数值 ( 默认 0 )\n\t * @property {Boolean}\t\t\tshowText\t\t是否在进度条内部显示百分比的值 ( 默认 true )\n\t * @property {String | Number}\theight\t\t\t进度条的高度，单位px ( 默认 12 )\n\t * \n\t * @example <u-line-progress :percent=\"70\" :show-percent=\"true\"></u-line-progress>\n\t */\n\texport default {\n\t\tname: \"u-line-progress\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tlineWidth: 0,\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tpercentage(n) {\n\t\t\t\tthis.resizeProgressWidth()\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tprogressStyle() { \n\t\t\t\tlet style = {}\n\t\t\t\tstyle.width = this.lineWidth\n\t\t\t\tstyle.backgroundColor = this.activeColor\n\t\t\t\tstyle.height = uni.$u.addUnit(this.height)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tinnserPercentage() {\n\t\t\t\t// 控制范围在0-100之间\n\t\t\t\treturn uni.$u.range(0, 100, this.percentage)\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tuni.$u.sleep(20).then(() => {\n\t\t\t\t\tthis.resizeProgressWidth()\n\t\t\t\t})\n\t\t\t},\n\t\t\tgetProgressWidth() {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\treturn this.$uGetRect('.u-line-progress__background')\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 返回一个promise\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(this.$refs['u-line-progress__background'], (res) => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tresizeProgressWidth() {\n\t\t\t\tthis.getProgressWidth().then(size => {\n\t\t\t\t\tconst {\n\t\t\t\t\t\twidth\n\t\t\t\t\t} = size\n\t\t\t\t\t// 通过设置的percentage值，计算其所占总长度的百分比\n\t\t\t\t\tthis.lineWidth = width * this.innserPercentage / 100 + 'px'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-line-progress {\n\t\talign-items: stretch;\n\t\tposition: relative;\n\t\t@include flex(row);\n\t\tflex: 1;\n\t\toverflow: hidden;\n\t\tborder-radius: 100px;\n\n\t\t&__background {\n\t\t\tbackground-color: #ececec;\n\t\t\tborder-radius: 100px;\n\t\t\tflex: 1;\n\t\t}\n\n\t\t&__line {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tbottom: 0;\n\t\t\talign-items: center;\n\t\t\t@include flex(row);\n\t\t\tcolor: #ffffff;\n\t\t\tborder-radius: 100px;\n\t\t\ttransition: width 0.5s ease;\n\t\t\tjustify-content: flex-end;\n\t\t}\n\n\t\t&__text {\n\t\t\tfont-size: 10px;\n\t\t\talign-items: center;\n\t\t\ttext-align: right;\n\t\t\tcolor: #FFFFFF;\n\t\t\tmargin-right: 5px;\n\t\t\ttransform: scale(0.9);\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line-progress.vue?vue&type=style&index=0&id=2f487391&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-line-progress.vue?vue&type=style&index=0&id=2f487391&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360672641\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}