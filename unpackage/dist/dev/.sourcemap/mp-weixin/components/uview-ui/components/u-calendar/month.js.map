{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-calendar/month.vue?f666", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-calendar/month.vue?749e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-calendar/month.vue?95ae", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-calendar/month.vue?8475", "uni-app:///components/uview-ui/components/u-calendar/month.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-calendar/month.vue?9d2f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-calendar/month.vue?25f0"], "names": ["name", "mixins", "props", "showMark", "type", "default", "color", "months", "mode", "rowHeight", "maxCount", "startText", "endText", "defaultDate", "minDate", "maxDate", "max<PERSON><PERSON><PERSON>", "readonly", "max<PERSON><PERSON><PERSON>", "rangePrompt", "showRangePrompt", "allowSameDay", "data", "width", "item", "selected", "watch", "<PERSON><PERSON><PERSON><PERSON>", "immediate", "handler", "computed", "dayStyle", "style", "week", "daySelectStyle", "textStyle", "getBottomInfo", "len", "mounted", "methods", "init", "uni", "dateSame", "getWrapperWidth", "getMonthRect", "index", "Promise", "topArr", "height", "getMonthRectByPromise", "resolve", "clickHandler", "arr", "i", "setDefaultDate", "setSelected", "event"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyL;AACzL,gBAAgB,iLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAAgtB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiCpuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;EACA;EACAiB;IACA;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAH;MACA;IACA;IACAI;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;;QAIAC;QACA;UACA;UACAC;UACAD;QACA;QACA;UACA;UACAA;UACAA;UACAA;UACAA;QACA;QACA;MACA;IACA;IACAE;MAAA;MACA;QACA;UACAF;QACA;QACA;UAAA;QAAA;UACAA;QACA;QACA;UACA;YACA;YACAA;YACAA;YACAA;YACAA;UACA;QACA;UACA;YACA;YACA;YACA;cACAA;cACAA;YACA;YACA;YACA;cACAA;cACAA;YACA;YACA;YACA,gJACAP;cACAO;cACA;cACAA;YACA;UACA;YACA;YACA;YACAA;YACAA;UACA;QACA;UACA;YAAA;UAAA;YACAA;YACAA;YACAA;YACAA;UACA;QACA;QACA;MACA;IACA;IACA;IACAG;MAAA;MACA;QACA;UACAH;QACA;QACA;UAAA;QAAA;UACAA;QACA;QACA;UACA;UACA;UACA,gJACAP;YACAO;UACA;QACA;QACA;MACA;IACA;IACA;IACAI;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA,4EACA;UACA;YACA;YACA;YACA,8FACAC;cACA;cACA;YACA;cACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;QACAC;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAOA;QACA;MACA;IAEA;IACAC;MAAA;MACA;MACA;QAAA,+DACAC;MAAA;MACA;MACAC,gCACA;QACA;QACA;QACA;UACA;UACAC;UACAC;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAEA;MACA;MACA;QACA;UACAC;QACA;MACA;IAYA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA1B;MACA;QACA;UAAA;QAAA;UACA;UACA;YAAA;UAAA;UACAA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;UACA;UACAA;QACA;UACA;UACA;UACA;UACA;YACAA;UACA;YACA;YACA;cACA;gBACAgB;cACA;gBACAA;cACA;cACA;YACA;YACA;YACAhB;YACA;YACA;YACA;YACA;YACA;cACA;cACA2B;cACAC;cACA;YACA;YACA;YACAD;YACA3B;UACA;YACA;YACA;YACAA;UACA;QACA;MACA;MACA;IACA;IACA;IACA6B;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACAzC;QACA;UACAA;QACA;MACA;QACA;QACA;QACAA;MACA;MACA;MACAA;QACA,oJACAE;MACA;MACA;IACA;IACAwC;MAAA;MACA;MACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACncA;AAAA;AAAA;AAAA;AAA22C,CAAgB,0pCAAG,EAAC,C;;;;;;;;;;;ACA/3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-calendar/month.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./month.vue?vue&type=template&id=5636def3&scoped=true&\"\nvar renderjs\nimport script from \"./month.vue?vue&type=script&lang=js&\"\nexport * from \"./month.vue?vue&type=script&lang=js&\"\nimport style0 from \"./month.vue?vue&type=style&index=0&id=5636def3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5636def3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-calendar/month.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./month.vue?vue&type=template&id=5636def3&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.months, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 = _vm.__map(item.date, function (item1, index1) {\n      var $orig = _vm.__get_orig(item1)\n      var s0 = _vm.__get_style([_vm.dayStyle(index, index1, item1)])\n      var s1 = _vm.__get_style([_vm.daySelectStyle(index, index1, item1)])\n      var s2 = _vm.__get_style([_vm.textStyle(item1)])\n      var m0 = _vm.getBottomInfo(index, index1, item1)\n      var s3 = m0 ? _vm.__get_style([_vm.textStyle(item1)]) : null\n      var m1 = m0 ? _vm.getBottomInfo(index, index1, item1) : null\n      return {\n        $orig: $orig,\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        m0: m0,\n        s3: s3,\n        m1: m1,\n      }\n    })\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./month.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./month.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-calendar-month-wrapper\" ref=\"u-calendar-month-wrapper\">\n\t\t<view v-for=\"(item, index) in months\" :key=\"index\" :class=\"[`u-calendar-month-${index}`]\"\n\t\t\t:ref=\"`u-calendar-month-${index}`\" :id=\"`month-${index}`\">\n\t\t\t<text v-if=\"index !== 0\" class=\"u-calendar-month__title\">{{ item.year }}年{{ item.month }}月</text>\n\t\t\t<view class=\"u-calendar-month__days\">\n\t\t\t\t<view v-if=\"showMark\" class=\"u-calendar-month__days__month-mark-wrapper\">\n\t\t\t\t\t<text class=\"u-calendar-month__days__month-mark-wrapper__text\">{{ item.month }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-calendar-month__days__day\" v-for=\"(item1, index1) in item.date\" :key=\"index1\"\n\t\t\t\t\t:style=\"[dayStyle(index, index1, item1)]\" @tap=\"clickHandler(index, index1, item1)\"\n\t\t\t\t\t:class=\"[item1.selected && 'u-calendar-month__days__day__select--selected']\">\n\t\t\t\t\t<view class=\"u-calendar-month__days__day__select\" :style=\"[daySelectStyle(index, index1, item1)]\">\n\t\t\t\t\t\t<text class=\"u-calendar-month__days__day__select__info\"\n\t\t\t\t\t\t\t:class=\"[item1.disabled && 'u-calendar-month__days__day__select__info--disabled']\"\n\t\t\t\t\t\t\t:style=\"[textStyle(item1)]\">{{ item1.day }}</text>\n\t\t\t\t\t\t<text v-if=\"getBottomInfo(index, index1, item1)\"\n\t\t\t\t\t\t\tclass=\"u-calendar-month__days__day__select__buttom-info\"\n\t\t\t\t\t\t\t:class=\"[item1.disabled && 'u-calendar-month__days__day__select__buttom-info--disabled']\"\n\t\t\t\t\t\t\t:style=\"[textStyle(item1)]\">{{ getBottomInfo(index, index1, item1) }}</text>\n\t\t\t\t\t\t<text v-if=\"item1.dot\" class=\"u-calendar-month__days__day__select__dot\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\t// 由于nvue不支持百分比单位，需要查询宽度来计算每个日期的宽度\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\timport dayjs from '../../libs/util/dayjs.js';\n\texport default {\n\t\tname: 'u-calendar-month',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin],\n\t\tprops: {\n\t\t\t// 是否显示月份背景色\n\t\t\tshowMark: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 主题色，对底部按钮和选中日期有效\n\t\t\tcolor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '#3c9cff'\n\t\t\t},\n\t\t\t// 月份数据\n\t\t\tmonths: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: () => []\n\t\t\t},\n\t\t\t// 日期选择类型\n\t\t\tmode: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'single'\n\t\t\t},\n\t\t\t// 日期行高\n\t\t\trowHeight: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 58\n\t\t\t},\n\t\t\t// mode=multiple时，最多可选多少个日期\n\t\t\tmaxCount: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: Infinity\n\t\t\t},\n\t\t\t// mode=range时，第一个日期底部的提示文字\n\t\t\tstartText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '开始'\n\t\t\t},\n\t\t\t// mode=range时，最后一个日期底部的提示文字\n\t\t\tendText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '结束'\n\t\t\t},\n\t\t\t// 默认选中的日期，mode为multiple或range是必须为数组格式\n\t\t\tdefaultDate: {\n\t\t\t\ttype: [Array, String, Date],\n\t\t\t\tdefault: null\n\t\t\t},\n\t\t\t// 最小的可选日期\n\t\t\tminDate: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\t// 最大可选日期\n\t\t\tmaxDate: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\t// 如果没有设置maxDate，则往后推多少个月\n\t\t\tmaxMonth: {\n\t\t\t\ttype: [String, Number],\n\t\t\t\tdefault: 2\n\t\t\t},\n\t\t\t// 是否为只读状态，只读状态下禁止选择日期\n\t\t\treadonly: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: uni.$u.props.calendar.readonly\n\t\t\t},\n\t\t\t// 日期区间最多可选天数，默认无限制，mode = range时有效\n\t\t\tmaxRange: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: Infinity\n\t\t\t},\n\t\t\t// 范围选择超过最多可选天数时的提示文案，mode = range时有效\n\t\t\trangePrompt: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 范围选择超过最多可选天数时，是否展示提示文案，mode = range时有效\n\t\t\tshowRangePrompt: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 是否允许日期范围的起止时间为同一天，mode = range时有效\n\t\t\tallowSameDay: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 每个日期的宽度\n\t\t\t\twidth: 0,\n\t\t\t\t// 当前选中的日期item\n\t\t\t\titem: {},\n\t\t\t\tselected: []\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tselectedChange: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tthis.setDefaultDate()\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 多个条件的变化，会引起选中日期的变化，这里统一管理监听\n\t\t\tselectedChange() {\n\t\t\t\treturn [this.minDate, this.maxDate, this.defaultDate]\n\t\t\t},\n\t\t\tdayStyle(index1, index2, item) {\n\t\t\t\treturn (index1, index2, item) => {\n\t\t\t\t\tconst style = {}\n\t\t\t\t\tlet week = item.week\n\t\t\t\t\t// 不进行四舍五入的形式保留2位小数\n\t\t\t\t\tconst dayWidth = Number(parseFloat(this.width / 7).toFixed(3).slice(0, -1))\n\t\t\t\t\t// 得出每个日期的宽度\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tstyle.width = uni.$u.addUnit(dayWidth)\n\t\t\t\t\t// #endif\n\t\t\t\t\tstyle.height = uni.$u.addUnit(this.rowHeight)\n\t\t\t\t\tif (index2 === 0) {\n\t\t\t\t\t\t// 获取当前为星期几，如果为0，则为星期天，减一为每月第一天时，需要向左偏移的item个数\n\t\t\t\t\t\tweek = (week === 0 ? 7 : week) - 1\n\t\t\t\t\t\tstyle.marginLeft = uni.$u.addUnit(week * dayWidth)\n\t\t\t\t\t}\n\t\t\t\t\tif (this.mode === 'range') {\n\t\t\t\t\t\t// 之所以需要这么写，是因为DCloud公司的iOS客户端的开发者能力有限导致的bug\n\t\t\t\t\t\tstyle.paddingLeft = 0\n\t\t\t\t\t\tstyle.paddingRight = 0\n\t\t\t\t\t\tstyle.paddingBottom = 0\n\t\t\t\t\t\tstyle.paddingTop = 0\n\t\t\t\t\t}\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t},\n\t\t\tdaySelectStyle() {\n\t\t\t\treturn (index1, index2, item) => {\n\t\t\t\t\tlet date = dayjs(item.date).format(\"YYYY-MM-DD\"),\n\t\t\t\t\t\tstyle = {}\n\t\t\t\t\t// 判断date是否在selected数组中，因为月份可能会需要补0，所以使用dateSame判断，而不用数组的includes判断\n\t\t\t\t\tif (this.selected.some(item => this.dateSame(item, date))) {\n\t\t\t\t\t\tstyle.backgroundColor = this.color\n\t\t\t\t\t}\n\t\t\t\t\tif (this.mode === 'single') {\n\t\t\t\t\t\tif (date === this.selected[0]) {\n\t\t\t\t\t\t\t// 因为需要对nvue的兼容，只能这么写，无法缩写，也无法通过类名控制等等\n\t\t\t\t\t\t\tstyle.borderTopLeftRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderBottomLeftRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderTopRightRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderBottomRightRadius = '3px'\n\t\t\t\t\t\t}\n\t\t\t\t\t} else if (this.mode === 'range') {\n\t\t\t\t\t\tif (this.selected.length >= 2) {\n\t\t\t\t\t\t\tconst len = this.selected.length - 1\n\t\t\t\t\t\t\t// 第一个日期设置左上角和左下角的圆角\n\t\t\t\t\t\t\tif (this.dateSame(date, this.selected[0])) {\n\t\t\t\t\t\t\t\tstyle.borderTopLeftRadius = '3px'\n\t\t\t\t\t\t\t\tstyle.borderBottomLeftRadius = '3px'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 最后一个日期设置右上角和右下角的圆角\n\t\t\t\t\t\t\tif (this.dateSame(date, this.selected[len])) {\n\t\t\t\t\t\t\t\tstyle.borderTopRightRadius = '3px'\n\t\t\t\t\t\t\t\tstyle.borderBottomRightRadius = '3px'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 处于第一和最后一个之间的日期，背景色设置为浅色，通过将对应颜色进行等分，再取其尾部的颜色值\n\t\t\t\t\t\t\tif (dayjs(date).isAfter(dayjs(this.selected[0])) && dayjs(date).isBefore(dayjs(this\n\t\t\t\t\t\t\t\t\t.selected[len]))) {\n\t\t\t\t\t\t\t\tstyle.backgroundColor = uni.$u.colorGradient(this.color, '#ffffff', 100)[90]\n\t\t\t\t\t\t\t\t// 增加一个透明度，让范围区间的背景色也能看到底部的mark水印字符\n\t\t\t\t\t\t\t\tstyle.opacity = 0.7\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.selected.length === 1) {\n\t\t\t\t\t\t\t// 之所以需要这么写，是因为DCloud公司的iOS客户端的开发者能力有限导致的bug\n\t\t\t\t\t\t\t// 进行还原操作，否则在nvue的iOS，uni-app有bug，会导致诡异的表现\n\t\t\t\t\t\t\tstyle.borderTopLeftRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderBottomLeftRadius = '3px'\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (this.selected.some(item => this.dateSame(item, date))) {\n\t\t\t\t\t\t\tstyle.borderTopLeftRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderBottomLeftRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderTopRightRadius = '3px'\n\t\t\t\t\t\t\tstyle.borderBottomRightRadius = '3px'\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 某个日期是否被选中\n\t\t\ttextStyle() {\n\t\t\t\treturn (item) => {\n\t\t\t\t\tconst date = dayjs(item.date).format(\"YYYY-MM-DD\"),\n\t\t\t\t\t\tstyle = {}\n\t\t\t\t\t// 选中的日期，提示文字设置白色\n\t\t\t\t\tif (this.selected.some(item => this.dateSame(item, date))) {\n\t\t\t\t\t\tstyle.color = '#ffffff'\n\t\t\t\t\t}\n\t\t\t\t\tif (this.mode === 'range') {\n\t\t\t\t\t\tconst len = this.selected.length - 1\n\t\t\t\t\t\t// 如果是范围选择模式，第一个和最后一个之间的日期，文字颜色设置为高亮的主题色\n\t\t\t\t\t\tif (dayjs(date).isAfter(dayjs(this.selected[0])) && dayjs(date).isBefore(dayjs(this\n\t\t\t\t\t\t\t\t.selected[len]))) {\n\t\t\t\t\t\t\tstyle.color = this.color\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn style\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 获取底部的提示文字\n\t\t\tgetBottomInfo() {\n\t\t\t\treturn (index1, index2, item) => {\n\t\t\t\t\tconst date = dayjs(item.date).format(\"YYYY-MM-DD\")\n\t\t\t\t\tconst bottomInfo = item.bottomInfo\n\t\t\t\t\t// 当为日期范围模式时，且选择的日期个数大于0时\n\t\t\t\t\tif (this.mode === 'range' && this.selected.length > 0) {\n\t\t\t\t\t\tif (this.selected.length === 1) {\n\t\t\t\t\t\t\t// 选择了一个日期时，如果当前日期为数组中的第一个日期，则显示底部文字为“开始”\n\t\t\t\t\t\t\tif (this.dateSame(date, this.selected[0])) return this.startText\n\t\t\t\t\t\t\telse return bottomInfo\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst len = this.selected.length - 1\n\t\t\t\t\t\t\t// 如果数组中的日期大于2个时，第一个和最后一个显示为开始和结束日期\n\t\t\t\t\t\t\tif (this.dateSame(date, this.selected[0]) && this.dateSame(date, this.selected[1]) &&\n\t\t\t\t\t\t\t\tlen === 1) {\n\t\t\t\t\t\t\t\t// 如果长度为2，且第一个等于第二个日期，则提示语放在同一个item中\n\t\t\t\t\t\t\t\treturn `${this.startText}/${this.endText}`\n\t\t\t\t\t\t\t} else if (this.dateSame(date, this.selected[0])) {\n\t\t\t\t\t\t\t\treturn this.startText\n\t\t\t\t\t\t\t} else if (this.dateSame(date, this.selected[len])) {\n\t\t\t\t\t\t\t\treturn this.endText\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treturn bottomInfo\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn bottomInfo\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 初始化默认选中\n\t\t\t\tthis.$emit('monthSelected', this.selected)\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t// 这里需要另一个延时，因为获取宽度后，会进行月份数据渲染，只有渲染完成之后，才有真正的高度\n\t\t\t\t\t// 因为nvue下，$nextTick并不是100%可靠的\n\t\t\t\t\tuni.$u.sleep(10).then(() => {\n\t\t\t\t\t\tthis.getWrapperWidth()\n\t\t\t\t\t\tthis.getMonthRect()\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 判断两个日期是否相等\n\t\t\tdateSame(date1, date2) {\n\t\t\t\treturn dayjs(date1).isSame(dayjs(date2))\n\t\t\t},\n\t\t\t// 获取月份数据区域的宽度，因为nvue不支持百分比，所以无法通过css设置每个日期item的宽度\n\t\t\tgetWrapperWidth() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(this.$refs['u-calendar-month-wrapper'], res => {\n\t\t\t\t\tthis.width = res.size.width\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$uGetRect('.u-calendar-month-wrapper').then(size => {\n\t\t\t\t\tthis.width = size.width\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tgetMonthRect() {\n\t\t\t\t// 获取每个月份数据的尺寸，用于父组件在scroll-view滚动事件中，监听当前滚动到了第几个月份\n\t\t\t\tconst promiseAllArr = this.months.map((item, index) => this.getMonthRectByPromise(\n\t\t\t\t\t`u-calendar-month-${index}`))\n\t\t\t\t// 一次性返回\n\t\t\t\tPromise.all(promiseAllArr).then(\n\t\t\t\t\tsizes => {\n\t\t\t\t\t\tlet height = 1\n\t\t\t\t\t\tconst topArr = []\n\t\t\t\t\t\tfor (let i = 0; i < this.months.length; i++) {\n\t\t\t\t\t\t\t// 添加到months数组中，供scroll-view滚动事件中，判断当前滚动到哪个月份\n\t\t\t\t\t\t\ttopArr[i] = height\n\t\t\t\t\t\t\theight += sizes[i].height\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 由于微信下，无法通过this.months[i].top的形式(引用类型)去修改父组件的month的top值，所以使用事件形式对外发出\n\t\t\t\t\t\tthis.$emit('updateMonthTop', topArr)\n\t\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取每个月份区域的尺寸\n\t\t\tgetMonthRectByPromise(el) {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t// $uGetRect为uView自带的节点查询简化方法，详见文档介绍：https://www.uviewui.com/js/getRect.html\n\t\t\t\t// 组件内部一般用this.$uGetRect，对外的为uni.$u.getRect，二者功能一致，名称不同\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tthis.$uGetRect(`.${el}`).then(size => {\n\t\t\t\t\t\tresolve(size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// nvue下，使用dom模块查询元素高度\n\t\t\t\t// 返回一个promise，让调用此方法的主体能使用then回调\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(this.$refs[el][0], res => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 点击某一个日期\n\t\t\tclickHandler(index1, index2, item) {\n\t\t\t\tif (this.readonly) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tthis.item = item\n\t\t\t\tconst date = dayjs(item.date).format(\"YYYY-MM-DD\")\n\t\t\t\tif (item.disabled) return\n\t\t\t\t// 对上一次选择的日期数组进行深度克隆\n\t\t\t\tlet selected = uni.$u.deepClone(this.selected)\n\t\t\t\tif (this.mode === 'single') {\n\t\t\t\t\t// 单选情况下，让数组中的元素为当前点击的日期\n\t\t\t\t\tselected = [date]\n\t\t\t\t} else if (this.mode === 'multiple') {\n\t\t\t\t\tif (selected.some(item => this.dateSame(item, date))) {\n\t\t\t\t\t\t// 如果点击的日期已在数组中，则进行移除操作，也就是达到反选的效果\n\t\t\t\t\t\tconst itemIndex = selected.findIndex(item => item === date)\n\t\t\t\t\t\tselected.splice(itemIndex, 1)\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果点击的日期不在数组中，且已有的长度小于总可选长度时，则添加到数组中去\n\t\t\t\t\t\tif (selected.length < this.maxCount) selected.push(date)\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 选择区间形式\n\t\t\t\t\tif (selected.length === 0 || selected.length >= 2) {\n\t\t\t\t\t\t// 如果原来就为0或者大于2的长度，则当前点击的日期，就是开始日期\n\t\t\t\t\t\tselected = [date]\n\t\t\t\t\t} else if (selected.length === 1) {\n\t\t\t\t\t\t// 如果已经选择了开始日期\n\t\t\t\t\t\tconst existsDate = selected[0]\n\t\t\t\t\t\t// 如果当前选择的日期小于上一次选择的日期，则当前的日期定为开始日期\n\t\t\t\t\t\tif (dayjs(date).isBefore(existsDate)) {\n\t\t\t\t\t\t\tselected = [date]\n\t\t\t\t\t\t} else if (dayjs(date).isAfter(existsDate)) {\n\t\t\t\t\t\t\t// 当前日期减去最大可选的日期天数，如果大于起始时间，则进行提示\n\t\t\t\t\t\t\tif(dayjs(dayjs(date).subtract(this.maxRange, 'day')).isAfter(dayjs(selected[0])) && this.showRangePrompt) {\n\t\t\t\t\t\t\t\tif(this.rangePrompt) {\n\t\t\t\t\t\t\t\t\tuni.$u.toast(this.rangePrompt)\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tuni.$u.toast(`选择天数不能超过 ${this.maxRange} 天`)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 如果当前日期大于已有日期，将当前的添加到数组尾部\n\t\t\t\t\t\t\tselected.push(date)\n\t\t\t\t\t\t\tconst startDate = selected[0]\n\t\t\t\t\t\t\tconst endDate = selected[1]\n\t\t\t\t\t\t\tconst arr = []\n\t\t\t\t\t\t\tlet i = 0\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\t// 将开始和结束日期之间的日期添加到数组中\n\t\t\t\t\t\t\t\tarr.push(dayjs(startDate).add(i, 'day').format(\"YYYY-MM-DD\"))\n\t\t\t\t\t\t\t\ti++\n\t\t\t\t\t\t\t\t// 累加的日期小于结束日期时，继续下一次的循环\n\t\t\t\t\t\t\t} while (dayjs(startDate).add(i, 'day').isBefore(dayjs(endDate)))\n\t\t\t\t\t\t\t// 为了一次性修改数组，避免computed中多次触发，这里才用arr变量一次性赋值的方式，同时将最后一个日期添加近来\n\t\t\t\t\t\t\tarr.push(endDate)\n\t\t\t\t\t\t\tselected = arr\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 选择区间时，只有一个日期的情况下，且不允许选择起止为同一天的话，不允许选择自己\n\t\t\t\t\t\t\tif (selected[0] === date && !this.allowSameDay) return\n\t\t\t\t\t\t\tselected.push(date)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.setSelected(selected)\n\t\t\t},\n\t\t\t// 设置默认日期\n\t\t\tsetDefaultDate() {\n\t\t\t\tif (!this.defaultDate) {\n\t\t\t\t\t// 如果没有设置默认日期，则将当天日期设置为默认选中的日期\n\t\t\t\t\tconst selected = [dayjs().format(\"YYYY-MM-DD\")]\n\t\t\t\t\treturn this.setSelected(selected, false)\n\t\t\t\t}\n\t\t\t\tlet defaultDate = []\n\t\t\t\tconst minDate = this.minDate || dayjs().format(\"YYYY-MM-DD\")\n\t\t\t\tconst maxDate = this.maxDate || dayjs(minDate).add(this.maxMonth - 1, 'month').format(\"YYYY-MM-DD\")\n\t\t\t\tif (this.mode === 'single') {\n\t\t\t\t\t// 单选模式，可以是字符串或数组，Date对象等\n\t\t\t\t\tif (!uni.$u.test.array(this.defaultDate)) {\n\t\t\t\t\t\tdefaultDate = [dayjs(this.defaultDate).format(\"YYYY-MM-DD\")]\n\t\t\t\t\t} else {\n\t\t\t\t\t\tdefaultDate = [this.defaultDate[0]]\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 如果为非数组，则不执行\n\t\t\t\t\tif (!uni.$u.test.array(this.defaultDate)) return\n\t\t\t\t\tdefaultDate = this.defaultDate\n\t\t\t\t}\n\t\t\t\t// 过滤用户传递的默认数组，取出只在可允许最大值与最小值之间的元素\n\t\t\t\tdefaultDate = defaultDate.filter(item => {\n\t\t\t\t\treturn dayjs(item).isAfter(dayjs(minDate).subtract(1, 'day')) && dayjs(item).isBefore(dayjs(\n\t\t\t\t\t\tmaxDate).add(1, 'day'))\n\t\t\t\t})\n\t\t\t\tthis.setSelected(defaultDate, false)\n\t\t\t},\n\t\t\tsetSelected(selected, event = true) {\n\t\t\t\tthis.selected = selected\n\t\t\t\tevent && this.$emit('monthSelected', this.selected)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-calendar-month-wrapper {\n\t\tmargin-top: 4px;\n\t}\n\n\t.u-calendar-month {\n\n\t\t&__title {\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 42px;\n\t\t\theight: 42px;\n\t\t\tcolor: $u-main-color;\n\t\t\ttext-align: center;\n\t\t\tfont-weight: bold;\n\t\t}\n\n\t\t&__days {\n\t\t\tposition: relative;\n\t\t\t@include flex;\n\t\t\tflex-wrap: wrap;\n\n\t\t\t&__month-mark-wrapper {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\t@include flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\n\t\t\t\t&__text {\n\t\t\t\t\tfont-size: 155px;\n\t\t\t\t\tcolor: rgba(231, 232, 234, 0.83);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&__day {\n\t\t\t\t@include flex;\n\t\t\t\tpadding: 2px;\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t// vue下使用css进行宽度计算，因为某些安卓机会无法进行js获取父元素宽度进行计算得出，会有偏移\n\t\t\t\twidth: calc(100% / 7);\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\t/* #endif */\n\n\t\t\t\t&__select {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\t@include flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t&__dot {\n\t\t\t\t\t\twidth: 7px;\n\t\t\t\t\t\theight: 7px;\n\t\t\t\t\t\tborder-radius: 100px;\n\t\t\t\t\t\tbackground-color: $u-error;\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: 12px;\n\t\t\t\t\t\tright: 7px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&__buttom-info {\n\t\t\t\t\t\tcolor: $u-content-color;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tbottom: 5px;\n\t\t\t\t\t\tfont-size: 10px;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\tright: 0;\n\n\t\t\t\t\t\t&--selected {\n\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t\tcolor: #cacbcd;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&__info {\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tfont-size: 16px;\n\n\t\t\t\t\t\t&--selected {\n\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t&--disabled {\n\t\t\t\t\t\t\tcolor: #cacbcd;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t&--selected {\n\t\t\t\t\t\tbackground-color: $u-primary;\n\t\t\t\t\t\t@include flex;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\tborder-radius: 3px;\n\t\t\t\t\t}\n\n\t\t\t\t\t&--range-selected {\n\t\t\t\t\t\topacity: 0.3;\n\t\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t&--range-start-selected {\n\t\t\t\t\t\tborder-top-right-radius: 0;\n\t\t\t\t\t\tborder-bottom-right-radius: 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t&--range-end-selected {\n\t\t\t\t\t\tborder-top-left-radius: 0;\n\t\t\t\t\t\tborder-bottom-left-radius: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./month.vue?vue&type=style&index=0&id=5636def3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./month.vue?vue&type=style&index=0&id=5636def3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673193\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}