{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-subsection/u-subsection.vue?815b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-subsection/u-subsection.vue?7e1e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-subsection/u-subsection.vue?6f05", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-subsection/u-subsection.vue?383f", "uni-app:///components/uview-ui/components/u-subsection/u-subsection.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-subsection/u-subsection.vue?4821", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/uview-ui/components/u-subsection/u-subsection.vue?aa85"], "names": ["name", "mixins", "data", "itemRect", "width", "height", "watch", "list", "current", "immediate", "handler", "computed", "wrapperStyle", "style", "barStyle", "itemStyle", "textStyle", "mounted", "methods", "init", "uni", "getText", "getRect", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACyL;AACzL,gBAAgB,iLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAutB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACsD3uB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAkBA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACAC;MACAC,8BAkBA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAD;MACAA;MACA;;MAEAA,uCACA,0CACA;MAEA;QACA;QACAA;MACA;MACA;IACA;IACA;IACAE;MAAA;MACA;QACA;QACA;UACA;UACAF;UACAA;UACAA;QACA;QACA;MACA;IACA;IACA;IACAG;MAAA;MACA;QACA;QACAH,mBACA;QACAA;QACA;QACA;UACAA,cACA;QACA;UACA;UACAA,cACA,2BACA,qBACA;QACA;QACA;MACA;IACA;EACA;EACAI;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAEA;QACA;MACA;IAUA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5MA;AAAA;AAAA;AAAA;AAAk3C,CAAgB,iqCAAG,EAAC,C;;;;;;;;;;;ACAt4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uview-ui/components/u-subsection/u-subsection.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-subsection.vue?vue&type=template&id=12830753&scoped=true&\"\nvar renderjs\nimport script from \"./u-subsection.vue?vue&type=script&lang=js&\"\nexport * from \"./u-subsection.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-subsection.vue?vue&type=style&index=0&id=12830753&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"12830753\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uview-ui/components/u-subsection/u-subsection.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=template&id=12830753&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle), _vm.wrapperStyle])\n  var s1 = _vm.__get_style([_vm.barStyle])\n  var g0 =\n    _vm.current > 0 &&\n    _vm.current < _vm.list.length - 1 &&\n    _vm.mode === \"subsection\" &&\n    \"u-subsection__bar--center\"\n  var g1 =\n    _vm.current === _vm.list.length - 1 &&\n    _vm.mode === \"subsection\" &&\n    \"u-subsection__bar--last\"\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s2 = _vm.__get_style([_vm.itemStyle(index)])\n    var g2 =\n      index < _vm.list.length - 1 && \"u-subsection__item--no-border-right\"\n    var g3 = index === _vm.list.length - 1 && \"u-subsection__item--last\"\n    var s3 = _vm.__get_style([_vm.textStyle(index)])\n    var m0 = _vm.getText(item)\n    return {\n      $orig: $orig,\n      s2: s2,\n      g2: g2,\n      g3: g3,\n      s3: s3,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=script&lang=js&\"", "<template>\n    <view\n        class=\"u-subsection\"\n        ref=\"u-subsection\"\n        :class=\"[`u-subsection--${mode}`]\"\n        :style=\"[$u.addStyle(customStyle), wrapperStyle]\"\n    >\n        <view\n            class=\"u-subsection__bar\"\n            ref=\"u-subsection__bar\"\n            :style=\"[barStyle]\"\n            :class=\"[\n                mode === 'button' && 'u-subsection--button__bar',\n                current === 0 &&\n                    mode === 'subsection' &&\n                    'u-subsection__bar--first',\n                current > 0 &&\n                    current < list.length - 1 &&\n                    mode === 'subsection' &&\n                    'u-subsection__bar--center',\n                current === list.length - 1 &&\n                    mode === 'subsection' &&\n                    'u-subsection__bar--last',\n            ]\"\n        ></view>\n        <view\n            class=\"u-subsection__item\"\n            :class=\"[\n                `u-subsection__item--${index}`,\n                index < list.length - 1 &&\n                    'u-subsection__item--no-border-right',\n                index === 0 && 'u-subsection__item--first',\n                index === list.length - 1 && 'u-subsection__item--last',\n            ]\"\n            :ref=\"`u-subsection__item--${index}`\"\n            :style=\"[itemStyle(index)]\"\n            @tap=\"clickHandler(index)\"\n            v-for=\"(item, index) in list\"\n            :key=\"index\"\n        >\n            <text\n                class=\"u-subsection__item__text\"\n                :style=\"[textStyle(index)]\"\n                >{{ getText(item) }}</text\n            >\n        </view>\n    </view>\n</template>\n\n<script>\n// #ifdef APP-NVUE\nconst dom = uni.requireNativePlugin(\"dom\");\nconst animation = uni.requireNativePlugin(\"animation\");\n// #endif\nimport props from \"./props.js\";\n/**\n * Subsection 分段器\n * @description 该分段器一般用于用户从几个选项中选择某一个的场景\n * @tutorial https://www.uviewui.com/components/subsection.html\n * @property {Array}\t\t\tlist\t\t\ttab的数据\n * @property {String ｜ Number}\tcurrent\t\t\t当前活动的tab的index（默认 0 ）\n * @property {String}\t\t\tactiveColor\t\t激活时的颜色（默认 '#3c9cff' ）\n * @property {String}\t\t\tinactiveColor\t未激活时的颜色（默认 '#303133' ）\n * @property {String}\t\t\tmode\t\t\t模式选择，mode=button为按钮形式，mode=subsection时为分段模式（默认 'button' ）\n * @property {String ｜ Number}\tfontSize\t\t字体大小，单位px（默认 12 ）\n * @property {Boolean}\t\t\tbold\t\t\t激活选项的字体是否加粗（默认 true ）\n * @property {String}\t\t\tbgColor\t\t\t组件背景颜色，mode为button时有效（默认 '#eeeeef' ）\n * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n * @property {String}\t        keyName\t        从`list`元素对象中读取的键名（默认 'name' ）\n *\n * @event {Function} change\t\t分段器选项发生改变时触发  回调 index：选项的index索引值，从0开始\n * @example <u-subsection :list=\"list\" :current=\"curNow\" @change=\"sectionChange\"></u-subsection>\n */\nexport default {\n    name: \"u-subsection\",\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n    data() {\n        return {\n            // 组件尺寸\n            itemRect: {\n                width: 0,\n                height: 0,\n            },\n        };\n    },\n    watch: {\n        list(newValue, oldValue) {\n            this.init();\n        },\n        current: {\n            immediate: true,\n            handler(n) {\n                // #ifdef APP-NVUE\n                // 在安卓nvue上，如果通过translateX进行位移，到最后一个时，会导致右侧无法绘制圆角\n                // 故用animation模块进行位移\n                const ref = this.$refs?.[\"u-subsection__bar\"]?.ref;\n                // 不存在ref的时候(理解为第一次初始化时，需要渲染dom，进行一定延时再获取ref)，这里的100ms是经过测试得出的结果(某些安卓需要延时久一点)，勿随意修改\n                uni.$u.sleep(ref ? 0 : 100).then(() => {\n                    animation.transition(this.$refs[\"u-subsection__bar\"].ref, {\n                        styles: {\n                            transform: `translateX(${\n                                n * this.itemRect.width\n                            }px)`,\n                            transformOrigin: \"center center\",\n                        },\n                        duration: 300,\n                    });\n                });\n                // #endif\n            },\n        },\n    },\n    computed: {\n        wrapperStyle() {\n            const style = {};\n            // button模式时，设置背景色\n            if (this.mode === \"button\") {\n                style.backgroundColor = this.bgColor;\n            }\n            return style;\n        },\n        // 滑块的样式\n        barStyle() {\n            const style = {};\n            style.width = `${this.itemRect.width}px`;\n            style.height = `${this.itemRect.height}px`;\n            // 通过translateX移动滑块，其移动的距离为索引*item的宽度\n            // #ifndef APP-NVUE\n            style.transform = `translateX(${\n                this.current * this.itemRect.width\n            }px)`;\n            // #endif\n            if (this.mode === \"subsection\") {\n                // 在subsection模式下，需要动态设置滑块的圆角，因为移动滑块使用的是translateX，无法通过父元素设置overflow: hidden隐藏滑块的直角\n                style.backgroundColor = this.activeColor;\n            }\n            return style;\n        },\n        // 分段器item的样式\n        itemStyle(index) {\n            return (index) => {\n                const style = {};\n                if (this.mode === \"subsection\") {\n                    // 设置border的样式\n                    style.borderColor = this.activeColor;\n                    style.borderWidth = \"1px\";\n                    style.borderStyle = \"solid\";\n                }\n                return style;\n            };\n        },\n        // 分段器文字颜色\n        textStyle(index) {\n            return (index) => {\n                const style = {};\n                style.fontWeight =\n                    this.bold && this.current === index ? \"bold\" : \"normal\";\n                style.fontSize = uni.$u.addUnit(this.fontSize);\n                // subsection模式下，激活时默认为白色的文字\n                if (this.mode === \"subsection\") {\n                    style.color =\n                        this.current === index ? \"#fff\" : this.inactiveColor;\n                } else {\n                    // button模式下，激活时文字颜色默认为activeColor\n                    style.color =\n                        this.current === index\n                            ? this.activeColor\n                            : this.inactiveColor;\n                }\n                return style;\n            };\n        },\n    },\n    mounted() {\n        this.init();\n    },\n    methods: {\n        init() {\n            uni.$u.sleep().then(() => this.getRect());\n        },\n\t\t// 判断展示文本\n\t\tgetText(item) {\n\t\t\treturn typeof item === 'object' ? item[this.keyName] : item\n\t\t},\n        // 获取组件的尺寸\n        getRect() {\n            // #ifndef APP-NVUE\n            this.$uGetRect(\".u-subsection__item--0\").then((size) => {\n                this.itemRect = size;\n            });\n            // #endif\n\n            // #ifdef APP-NVUE\n            const ref = this.$refs[\"u-subsection__item--0\"][0];\n            ref &&\n                dom.getComponentRect(ref, (res) => {\n                    this.itemRect = res.size;\n                });\n            // #endif\n        },\n        clickHandler(index) {\n            this.$emit(\"change\", index);\n        },\n    },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/components.scss\";\n\n.u-subsection {\n    @include flex;\n    position: relative;\n    overflow: hidden;\n\t/* #ifndef APP-NVUE */\n\twidth: 100%;\n\tbox-sizing: border-box;\n\t/* #endif */\n\n    &--button {\n        height: 32px;\n        background-color: rgb(238, 238, 239);\n        padding: 3px;\n        border-radius: 3px;\n        align-items: stretch;\n\n        &__bar {\n            background-color: #ffffff;\n            border-radius: 3px !important;\n        }\n    }\n\n    &--subsection {\n        height: 30px;\n    }\n\n    &__bar {\n        position: absolute;\n        /* #ifndef APP-NVUE */\n        transition-property: transform, color;\n        transition-duration: 0.3s;\n        transition-timing-function: ease-in-out;\n        /* #endif */\n\n        &--first {\n            border-top-left-radius: 3px;\n            border-bottom-left-radius: 3px;\n            border-top-right-radius: 0px;\n            border-bottom-right-radius: 0px;\n        }\n\n        &--center {\n            border-top-left-radius: 0px;\n            border-bottom-left-radius: 0px;\n            border-top-right-radius: 0px;\n            border-bottom-right-radius: 0px;\n        }\n\n        &--last {\n            border-top-left-radius: 0px;\n            border-bottom-left-radius: 0px;\n            border-top-right-radius: 3px;\n            border-bottom-right-radius: 3px;\n        }\n    }\n\n    &__item {\n        @include flex;\n        flex: 1;\n        justify-content: center;\n        align-items: center;\n        // vue环境下，需要设置相对定位，因为滑块为绝对定位，item需要在滑块的上面\n        position: relative;\n\n        &--no-border-right {\n            border-right-width: 0 !important;\n        }\n\n        &--first {\n            border-top-left-radius: 3px;\n            border-bottom-left-radius: 3px;\n        }\n\n        &--last {\n            border-top-right-radius: 3px;\n            border-bottom-right-radius: 3px;\n        }\n\n        &__text {\n            font-size: 12px;\n            line-height: 12px;\n            @include flex;\n            align-items: center;\n            transition-property: color;\n            transition-duration: 0.3s;\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=style&index=0&id=12830753&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=style&index=0&id=12830753&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673176\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}