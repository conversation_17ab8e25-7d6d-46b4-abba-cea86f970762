{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/mescroll-uni/mescroll-uni/components/mescroll-top.vue?9714", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/mescroll-uni/mescroll-uni/components/mescroll-top.vue?0ad1", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/mescroll-uni/mescroll-uni/components/mescroll-top.vue?7734", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/mescroll-uni/mescroll-uni/components/mescroll-top.vue?a130", "uni-app:///components/mescroll-uni/mescroll-uni/components/mescroll-top.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/mescroll-uni/mescroll-uni/components/mescroll-top.vue?f248", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/mescroll-uni/mescroll-uni/components/mescroll-top.vue?8888"], "names": ["props", "option", "type", "default", "value", "modelValue", "computed", "left", "right", "isShow", "methods", "addUnit", "toTopClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AACyL;AACzL,gBAAgB,iLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAutB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCc3uB;EACAA;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;IAAA;IACAC;EACA;;EACAC;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAKA;IAEA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MAKA;;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAshC,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACA1iC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/mescroll-uni/mescroll-uni/components/mescroll-top.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-top.vue?vue&type=template&id=18360e3b&\"\nvar renderjs\nimport script from \"./mescroll-top.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-top.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-top.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/mescroll-uni/mescroll-uni/components/mescroll-top.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=template&id=18360e3b&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.option && _vm.option.src ? _vm.addUnit(_vm.option.bottom) : null\n  var m1 = _vm.option && _vm.option.src ? _vm.addUnit(_vm.option.width) : null\n  var m2 = _vm.option && _vm.option.src ? _vm.addUnit(_vm.option.radius) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=script&lang=js&\"", "<!-- 回到顶部的按钮 -->\r\n<template>\r\n\t<image\r\n\t\tv-if=\"option && option.src\"\r\n\t\tclass=\"mescroll-totop\"\r\n\t\t:class=\"[isShow ? 'mescroll-totop-in' : 'mescroll-totop-out', {'mescroll-totop-safearea': option.safearea}]\"\r\n\t\t:style=\"{'z-index':option.zIndex, 'left': left, 'right': right, 'bottom':addUnit(option.bottom), 'width':addUnit(option.width), 'border-radius':addUnit(option.radius)}\"\r\n\t\t:src=\"option.src\"\r\n\t\tmode=\"widthFix\"\r\n\t\t@click=\"toTopClick\"\r\n\t/>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\t// up.toTop的配置项\r\n\t\toption: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault(){\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 是否显示\r\n\t\tvalue: false, // vue2\r\n\t\tmodelValue: false // vue3\r\n\t},\r\n\tcomputed: {\r\n\t\t// 优先显示左边\r\n\t\tleft(){\r\n\t\t\tif(!this.option) return 'auto' \r\n\t\t\treturn this.option.left ? this.addUnit(this.option.left) : 'auto';\r\n\t\t},\r\n\t\t// 右边距离 (优先显示左边)\r\n\t\tright() {\r\n\t\t\tif(!this.option) return 'auto' \r\n\t\t\treturn this.option.left ? 'auto' : this.addUnit(this.option.right);\r\n\t\t},\r\n\t\t// 是否显示\r\n\t\tisShow(){\r\n\t\t\t// #ifdef VUE3\r\n\t\t\treturn this.modelValue\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE2\r\n\t\t\treturn this.value\r\n\t\t\t// #endif\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\taddUnit(num){\r\n\t\t\tif(!num) return 0;\r\n\t\t\tif(typeof num === 'number') return num + 'rpx';\r\n\t\t\treturn num\r\n\t\t},\r\n\t\ttoTopClick() {\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tthis.$emit(\"update:modelValue\", false); // 使v-model生效 vue3\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE2\r\n\t\t\tthis.$emit('input', false); // 使v-model生效 vue2\r\n\t\t\t// #endif\r\n\t\t\tthis.$emit('click'); // 派发点击事件\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style>\r\n/* 回到顶部的按钮 */\r\n.mescroll-totop {\r\n\tz-index: 9990;\r\n\tposition: fixed !important; /* 加上important避免编译到H5,在多mescroll中定位失效 */\r\n\tright: 20rpx;\r\n\tbottom: 120rpx;\r\n\twidth: 72rpx;\r\n\theight: auto;\r\n\tborder-radius: 50%;\r\n\topacity: 0;\r\n\ttransition: opacity 0.5s; /* 过渡 */\r\n\tmargin-bottom: var(--window-bottom); /* css变量 */\r\n}\r\n\r\n/* 适配 iPhoneX */\r\n@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {\r\n\t.mescroll-totop-safearea {\r\n\t\tmargin-bottom: calc(var(--window-bottom) + constant(safe-area-inset-bottom)); /* window-bottom + 适配 iPhoneX */\r\n\t\tmargin-bottom: calc(var(--window-bottom) + env(safe-area-inset-bottom));\r\n\t}\r\n}\r\n\r\n/* 显示 -- 淡入 */\r\n.mescroll-totop-in {\r\n\topacity: 1;\r\n}\r\n\r\n/* 隐藏 -- 淡出且不接收事件*/\r\n.mescroll-totop-out {\r\n\topacity: 0;\r\n\tpointer-events: none;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360664379\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}