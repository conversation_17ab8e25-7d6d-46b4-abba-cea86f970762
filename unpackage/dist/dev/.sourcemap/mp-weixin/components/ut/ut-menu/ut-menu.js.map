{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-menu/ut-menu.vue?b1e6", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-menu/ut-menu.vue?f5e5", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-menu/ut-menu.vue?3d17", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-menu/ut-menu.vue?6824", "uni-app:///components/ut/ut-menu/ut-menu.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-menu/ut-menu.vue?0b7a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-menu/ut-menu.vue?506f"], "names": ["components", "data", "categoryCurrent", "props", "list", "type", "default", "rowCount", "imageSize", "imageRadius", "shape", "computed", "carousel", "viewWidht", "boxHeight", "comm<PERSON>ey", "token", "city", "community", "locationAddress", "userInfo", "created", "methods", "sortData", "oArr", "minArr", "arr", "onSwiper", "routerTo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2CvtB;AAIA;AAAA;AAAA,gBACA;EACAA;EACAC;IACA;MACAC;IACA;EACA;;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;EAAA,GACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;EACAC;IACA;IACAC;MACA;MACA;MACAC;QACA;UACAC;QACA;QACA;UACAC;QACA;QACAD;MACA;MAEA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;MACA;MACA;QACA;QACA;QACA;MACA;MACA;QACA;QACA;QACAF;UACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACtKA;AAAA;AAAA;AAAA;AAA0zC,CAAgB,ooCAAG,EAAC,C;;;;;;;;;;;ACA90C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-menu/ut-menu.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-menu.vue?vue&type=template&id=584a99d4&\"\nvar renderjs\nimport script from \"./ut-menu.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-menu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-menu.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-menu/ut-menu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-menu.vue?vue&type=template&id=584a99d4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.carousel.length\n  var l1 = g0\n    ? _vm.__map(_vm.carousel, function (itemList, indexList) {\n        var $orig = _vm.__get_orig(itemList)\n        var g1 = _vm.list.length\n        var l0 = _vm.__map(itemList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g2 = itemList.length > _vm.rowCount && index < _vm.rowCount\n          return {\n            $orig: $orig,\n            g2: g2,\n          }\n        })\n        return {\n          $orig: $orig,\n          g1: g1,\n          l0: l0,\n        }\n      })\n    : null\n  var g3 = g0 ? _vm.carousel.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-menu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-menu.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 产品分类导航 -->\r\n\t<view class=\"menu-category-box\" v-if=\"carousel.length\">\r\n\t\t<swiper\r\n\t\t\tclass=\"menu-swiper-box\"\r\n\t\t\t:style=\"{height:boxHeight+'rpx'}\"\r\n\t\t\t@change=\"onSwiper\"\r\n\t\t\tcircular\r\n\t\t\t:autoplay=\"false\"\r\n\t\t\t:interval=\"3000\"\r\n\t\t\t:duration=\"1000\"\r\n\t\t>\r\n\t\t\t<swiper-item class=\"menu-swiper-item\" v-for=\"(itemList, indexList) in carousel\" :key=\"indexList\">\r\n\r\n\t\t\t\t<view class=\"menu-tab-box\" :style=\"{justifyContent: list.length<=rowCount?'space-around':''}\">\r\n\t\t\t\t\t<view class=\"item-box\" \r\n\t\t\t\t\t\t:style=\"{ width: viewWidht + '%',paddingBottom: itemList.length>rowCount && index<rowCount?'20rpx':''}\" \r\n\t\t\t\t\t\tv-for=\"(item,index) in itemList\" \r\n\t\t\t\t\t\t:key=\"index\" \r\n\t\t\t\t\t\t@tap=\"routerTo(item)\">\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view v-if=\"item.cmd=='contact'\" class=\"contact-box\">\r\n\t\t\t\t\t\t\t<button open-type=\"contact\" class=\"btn-contact\" bindcontact=\"test\" session-from=\"sessionFrom\">\r\n\t\t\t\t\t\t\t\t<image class=\"item-image\" :style=\"{ width: imageSize + 'rpx', height: imageSize + 'rpx',borderRadius:shape=='circle'?'500rpx':imageRadius+'rpx' }\" :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t\t<text class=\"item-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t\t<image class=\"item-image\" :style=\"{ width: imageSize + 'rpx', height: imageSize + 'rpx',borderRadius:shape=='circle'?'500rpx':imageRadius+'rpx' }\" :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t<text class=\"item-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view class=\"menu-category-dots\" v-if=\"carousel.length > 1\">\r\n\t\t\t<text class=\"category-dot\" :class=\"{'category-dot-active':categoryCurrent === index}\" v-for=\"(dot, index) in carousel.length\" :key=\"index\"></text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapMutations,\r\n\t\tmapActions,\r\n\t\tmapState\r\n\t} from 'vuex'\r\nexport default {\r\n\tcomponents: {},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcategoryCurrent: 0 //分类轮播下标\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\tlist: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: ()=>[]\r\n\t\t},\r\n\t\trowCount: {\r\n\t\t\ttype:Number,\r\n\t\t\tdefault: 5\r\n\t\t},\r\n\t\timageSize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 72\r\n\t\t},\r\n\t\timageRadius: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 6\r\n\t\t},\r\n\t\tshape:{\r\n\t\t\ttype:String,\r\n\t\t\tdefault:''\r\n\t\t},\r\n\t},\r\n\tcomputed: {\r\n\t\tcarousel() {\r\n\t\t\tif (this.list) {\r\n\t\t\t\tlet data = this.sortData(this.list, this.rowCount * 2);\r\n\t\t\t\treturn data;\r\n\t\t\t}\r\n\t\t},\r\n\t\tviewWidht(){\r\n\t\t\tswitch(this.rowCount){\r\n\t\t\t\tcase 1: return 100 \r\n\t\t\t\tcase 2: return 50\r\n\t\t\t\tcase 3: return 33.33\r\n\t\t\t\tcase 4: return 25\r\n\t\t\t\tcase 5: return 20\r\n\t\t\t\tcase 6: return 16.66\r\n\t\t\t\tcase 7: return 14.28\r\n\t\t\t\tcase 8: return 12.5\r\n\t\t\t\tcase 9: return 11.11\r\n\t\t\t\tcase 10: return 10\r\n\t\t\t\tdefault: return 25\r\n\t\t\t}\r\n\t\t},\r\n\t\tboxHeight(){\r\n\t\t\tif(this.list.length <= this.rowCount){ \r\n\t\t\t\treturn this.imageSize+10+30\r\n\t\t\t}else{\r\n\t\t\t\treturn (this.imageSize+10+30)*2+20\r\n\t\t\t}\r\n\t\t},\r\n\t\t...mapState({\r\n\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t\ttoken: state => state.user.token,\r\n\t\t\tcity: state => state.init.city,\r\n\t\t\tcommunity: state => state.init.community,\r\n\t\t\tlocationAddress: state => state.init.locationAddress,\r\n\t\t\tuserInfo: state => state.user.info,\r\n\t\t}),\r\n\t},\r\n\tcreated() {},\r\n\tmethods: {\r\n\t\t// 数据分层\r\n\t\tsortData(oArr, length) {\r\n\t\t\tlet arr = [];\r\n\t\t\tlet minArr = [];\r\n\t\t\toArr.forEach(c => {\r\n\t\t\t\tif (minArr.length === length) {\r\n\t\t\t\t\tminArr = [];\r\n\t\t\t\t}\r\n\t\t\t\tif (minArr.length === 0) {\r\n\t\t\t\t\tarr.push(minArr);\r\n\t\t\t\t}\r\n\t\t\t\tminArr.push(c);\r\n\t\t\t});\r\n\r\n\t\t\treturn arr;\r\n\t\t},\r\n\t\t// 轮播\r\n\t\tonSwiper(e) {\r\n\t\t\tthis.categoryCurrent = e.detail.current;\r\n\t\t},\r\n\t\t// 路由跳转\r\n\t\trouterTo(item) {\r\n\t\t\tif(item.checkLogin && !this.token){\r\n\t\t\t\tthis.$store.commit('LOGIN_TIP', true)\r\n\t\t\t\tthis.$emit('check',['login'])\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif(item.checkPhone && !this.userInfo.phone){\r\n\t\t\t\tthis.$store.commit('PHONE_TIP', true)\r\n\t\t\t\tthis.$emit('check',['phone'])\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif(item.checkCode){\r\n\t\t\t\tlet arr=item.checkCode.split(',')\r\n\t\t\t\tvar isGo=true\r\n\t\t\t\tarr.forEach(o=>{\r\n\t\t\t\t\tlet obj=this.$store.state.init[o]\r\n\t\t\t\t\tif(!obj) isGo=false\r\n\t\t\t\t})\r\n\t\t\t\tif(!isGo){\t\r\n\t\t\t\t\tthis.$emit('check',arr)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif(item.cmd) return\r\n\t\t\tif(!item.path) return\r\n\t\t\tthis.$tools.routerTo(item.path);\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.menu-category-box,\r\n.menu-swiper-box {\r\n\tposition: relative;\r\n\t.menu-tab-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\talign-items: center;\r\n\t\t.item-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-family: PingFang SC;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: rgba(51, 51, 51, 1);\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.item-image {\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.item-title{\r\n\t\t\t\r\n\t\t\t\toverflow: hidden;//溢出隐藏\r\n\t\t\t\twhite-space: nowrap;//禁止换行\r\n\t\t\t\ttext-overflow: ellipsis;//...\r\n\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.menu-category-dots {\r\n\t\tdisplay: flex;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tbottom: 20rpx;\r\n\r\n\t\t.category-dot {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 3rpx;\r\n\t\t\tbackground: #eeeeee;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\r\n\t\t.category-dot-active {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 3rpx;\r\n\t\t\tbackground: #a8700d;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.contact-box{\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.btn-contact{\r\n\t\tborder: none;\r\n\t\tpadding: 0;\r\n\t\tbackground: none;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tfont-size: 12px;\r\n\t\tfont-weight: 500;\r\n\t\tline-height: 32rpx !important;\r\n\t\tborder-radius: 0;\r\n\t\tcolor: #333;\r\n\t\tfont-family: PingFang SC;\r\n\t\t\r\n\t\t&::after{\r\n\t\t\tcontent: none;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-menu.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-menu.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673571\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}