{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-tabs/ut-tabs.vue?28f6", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-tabs/ut-tabs.vue?1039", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-tabs/ut-tabs.vue?f45f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-tabs/ut-tabs.vue?510c", "uni-app:///components/ut/ut-tabs/ut-tabs.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-tabs/ut-tabs.vue?7493", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-tabs/ut-tabs.vue?a2b6"], "names": ["name", "props", "bjColor", "type", "default", "tabHeight", "tabsData", "lineColor", "fontColor", "data", "tabIndex", "methods", "onTabIndex"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCavtB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA0zC,CAAgB,ooCAAG,EAAC,C;;;;;;;;;;;ACA90C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-tabs/ut-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-tabs.vue?vue&type=template&id=12f79154&\"\nvar renderjs\nimport script from \"./ut-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-tabs.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-tabs/ut-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-tabs.vue?vue&type=template&id=12f79154&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-tabs.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"ut-tabs-container\" :style=\"{'background-color':bjColor}\">\n\t\t<scroll-view scroll-x scroll-with-animation class=\"ut-tabs-scroll\">\r\n\t\t\t<view class=\"ut-tabs-scroll-box\" :style=\"{'height': tabHeight,'line-height': tabHeight}\">\r\n\t\t\t\t<view class=\"ut-tabs-scroll-box-item\" :class=\"tabIndex==index?'active':''\"   v-for=\"(item,index) in tabsData\" :key=\"index\" @click=\"onTabIndex(index,item)\">\r\n\t\t\t\t\t<view class=\"name\"  :style=\"{'color':fontColor}\">{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname:\"ut-tabs\",\r\n\t\tprops:{\r\n\t\t\tbjColor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:\"#fff\"\r\n\t\t\t},\r\n\t\t\ttabHeight:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '90rpx'\r\n\t\t\t},\r\n\t\t\ttabsData:{\r\n\t\t\t\ttype:Array,\r\n\t\t\t\tdefault:function(){\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlineColor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:\"#fa436a\"\r\n\t\t\t},\r\n\t\t\tfontColor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:\"#fff\"\r\n\t\t\t},\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttabIndex:0\n\t\t\t};\n\t\t},\r\n\t\tmethods:{\r\n\t\t\tonTabIndex(index,item){\r\n\t\t\t\tif(this.tabIndex!=index){\r\n\t\t\t\t\tthis.tabIndex = index;\r\n\t\t\t\t\t// this.$emit(\"update:tabIndex\",index);\r\n\t\t\t\t\tthis.$emit(\"change\",index,item)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\r\n\t$color:var(--colors);\n\t.ut-tabs-container{\r\n\t\twidth: 100%;\r\n\t}\r\n\t.ut-tabs-scroll-box{\r\n\t\twhite-space: nowrap !important;\r\n\t\tpadding: 4rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-evenly;\r\n\t\t&-item{\r\n\t\t\tmin-width: 100rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 0rpx 10rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tletter-spacing: 4rpx;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\ttext-align: center;\r\n\t\t\t.name{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tflex-shrink: 0;\r\n\t\t\tposition: relative;\r\n\t\t\ttransition: all 0.2s linear;\r\n\t\t\t&::after{\r\n\t\t\t\ttransition: all 0.2s linear;\r\n\t\t\t\ttransform: translateX(-50%) scaleX(0);\r\n\t\t\t\tcontent: '';\r\n\t\t\t\twidth: 50%;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\tbottom: 20rpx;\r\n\t\t\t\tborder-bottom: 6rpx solid red;\r\n\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.active{\r\n\t\tcolor: $color;\r\n\t\t\r\n\t\t&::after{\r\n\t\t\tcontent: '';\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%) scaleX(1);\r\n\t\t\tbottom: 10rpx;\r\n\t\t\tborder-bottom: 4rpx solid red;\r\n\t\t}\r\n\t\t// \r\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-tabs.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-tabs.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673591\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}