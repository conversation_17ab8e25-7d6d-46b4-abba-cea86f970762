{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?d41d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?6066", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?667e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?f410", "uni-app:///components/ut/ut-image-upload/ut-image-upload.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?24b6", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?08f7"], "names": ["name", "props", "width", "type", "default", "colors", "height", "borderRadius", "dataType", "max", "chooseNum", "remove", "add", "disabled", "sourceType", "action", "headers", "formData", "compress", "quality", "value", "uploadSuccess", "success", "url", "mediaType", "maxDuration", "camera", "mode", "longPress", "previewImageWidth", "spacing", "data", "timer", "islongPress", "imageLoaded", "uploadLists", "mediaTypeData", "previewVideoSrc", "uploadTasks", "uploadTask", "load", "uploadData", "uploadTaskProgress", "isLoaded", "mounted", "id", "state", "progress", "uni", "title", "content", "showCancel", "watch", "val", "methods", "imageLoadingWidth", "imageLoad", "checkLoad", "isVideo", "isPass", "getFileUrl", "previewVideoClick", "previewVideo", "previewVideoClose", "imgDel", "delIndex", "clear", "imgPreviewClick", "imgPreview", "imgData", "arr", "urls", "current", "loop", "chooseFile", "itemList", "fail", "videoAdd", "compressed", "imgAdd", "count", "sizeType", "appCamera", "cmr", "resolution", "format", "appGallery", "plus", "filter", "multiple", "maximum", "chooseSuccessMethod", "imgCompress", "tempFilePaths", "compressImgs", "src", "results", "resolve", "reject", "complete", "Promise", "then", "catch", "imgUpload", "that", "filePath", "fileType", "header", "task", "uploadImgs", "uniCloudUpload", "uniCloud", "cloudPath", "console", "fileList", "res", "getFileType", "result", "guid", "v", "triggerMonitor", "mergerProgress", "tasks", "setUpMonitor", "obj", "canvasDataURL", "img", "h", "scale", "w", "anw", "anh", "canvas", "ctx", "callback", "showImg", "urlTmp", "str", "parseBlob", "u8arr", "longpress", "touchstart", "touchend", "clearTimeout", "setTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACmE;AACL;AACa;;;AAG3E;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0F/tB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MAAA;MACAL;MACAC;IACA;;IACAK;MAAA;MACAN;MACAC;IACA;IACAM;MAAA;MACAP;MACAC;IACA;IACAJ;MAAA;MACAG;MACAC;IACA;IACAO;MAAA;MACAR;MACAC;IACA;IACAQ;MAAA;MACAT;MACAC;IACA;IACAS;MAAA;MACAV;MACAC;IACA;IACAU;MAAA;MACAX;MACAC;QAAA;MAAA;IACA;IACAW;MAAA;MACAZ;MACAC;IACA;IACAY;MAAA;MACAb;MACAC;IACA;IACAa;MAAA;MACAd;MACAC;IACA;IACAc;MAAA;MACAf;MACAC;IACA;IACAe;MAAA;MACAhB;MACAC;IACA;IACAgB;MAAA;MACAjB;MACAC;QAAA;MAAA;IACA;IACAiB;MACAjB;QACA;UACAkB;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACArB;MACAC;IACA;IACAqB;MAAA;MACAtB;MACAC;IACA;IACAsB;MAAA;MACAvB;MACAC;IACA;IACAuB;MACAxB;MACAC;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACA;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA0B;MACA3B;MACAC;IACA;EACA;EACA2B;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;MAAA;MACA;MACA;QACA;UACA;YACAC;YACAtB;YACAuB;YACAC;UACA;QACA;UACA;YACAF;YACAtB;YACAuB;YACAC;UACA;QACA;MACA;MACA;;MAEA;QACAC;UACAC;UACAC;UACAC;UACA7B;YACA,kBACA,wBACA;UACA;QACA;MACA;IACA;EACA;EACA8B;IACAhC;MAAA;MACA;MACA;QACA;QACA;MACA;MACA;MACAiC;QACA;UACAR;UACAtB;UACAuB;UACAC;QACA;MACA;MACA;IACA;EAEA;EACAO;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA,wGACAvD;QACAwD;MACA;MACA;IACA;IACAC;MACA;MACA;QACArC;MACA;MACA;IACA;IACAsC;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACAhB;QACAC;QACAC;QACA5B;UACA;YAEA;YACA;YACA;YACA;YACA;cACA;YACA;YACA;YACA;cACAC;cACAsB;YACA;UACA;QACA;MACA;IACA;IACAoB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACAC;QACA;QACA;UACA;YACAC;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;MACAtB;QACAuB;QACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;MACA;QACA;UAAA;UACA;UACA;QACA;UAAA;UACA1B;YACA2B;YACArD;cACA;gBACA;cACA;gBACA;cACA;YACA;YACAsD,0BACA;UACA;UACA;QACA;UAAA;UACA;UACA;MAAA;IAEA;IACAC;MAAA;MACA;MACA;MACA7B;QACA8B;QACAhE;QACAY;QACAD;QACAH;UACA;QACA;MACA;IACA;IACAyD;MAAA;MACA;MACA;;MAyBA/B;QACAgC;QACAC;QAAA;QACAnE;QACAQ;UACA;QACA;MACA;IAEA;IACA4D;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA,GACA,kBACA;QACAC;QACAC;MACA,EACA;IACA;IACAC;MAAA;MACAC;QACA;MACA,iBACA;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;QAAA;QACA;QACA;MACA;MAEA;QACA;MACA;QACA;UAAA;UACA;QACA;UACA;QACA;MACA;IAGA;IACAC;MAAA;MACA5C;QACAC;MACA;MAEA;MACA;MACA4C;QACAC;UAEA9C;YACA+C;YACA5E;YACAG;cACA0E;cACAC;YACA;YACArB;cACAsB;YACA;YACAC;cACA;YAAA;UAEA;QAWA;MACA;MACAC;MAAA,CACAC;QACArD;QACA;MACA,GACAsD;QACAtD;MACA;IACA;IACAuD;MAAA;MACA;QACA;QACA;MACA;MACAvD;QACAC;MACA;MAEA;MAEA4C;QACA;UACAhD;UACAC;UACAC;UACAxB;UACAQ;QACA;QACA;QACAyE;QACA;MACA;;MAGA;MACA;QAAA;MAAA;MACAlC;QACA;UAEA;YACA/C;YAAA;YACAkF;YACAzG;YACA0G;YACAzF;YACA0F;YACArF;cACA;gBAAA;cAAA;cACA,SACA;gBACA;gBACAkF;gBACAA;gBACAA;gBACA;gBACA;cACA;;cAEAP;YACA;YACArB;cACAsB;cACAM;YACA;YACAL;UACA;UACAS;UACAA;YACA,yBACA;cACA;gBAAA;cAAA;cACA,SACA;gBACAJ;gBACAA;cACA;YACA;UAEA;QACA;QACAK;MACA;MACAT;MAAA,CACAC;QACArD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAwD;MACA,GACAF;QACAtD;QACA;MACA;IAEA;IACA8D;MAAA;MACA9D;QACAC;MACA;MACA;MACA4C;QACAgB;UAEAE;YACAN;YACAO;YACA1F;cACA;gBACA2E;cACA;YACA;YACArB;cACAqC;cACAf;YACA;YACAC;UACA;QAEA;MACA;MACAC;MAAA,CACAC;QACArD;QAEA+D;UACAG;UACA5F;YACA6F;cACA;;cAEA;cACA;YAMA;UACA;UACAvC;UACAuB;QACA;MACA,GACAG;QACAtD;MACA;IACA;IACAoE;MAAA;;MAOA;MAEA;QAAA;QACAC;MACA;MAGA;IACA;IACAC;MACA;QACA;UACAC;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACAjF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAkF;MACA;MACAC;QACA;UACA3E;QACA;UACAA;QACA;MACA;MACA;QACAA;QACA2E;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACAnF;MAAA,GACAoF,IACA;IACA;IACAC;MACA;MACAC;MACAA;QACA;QACA;QACA;UACAC;UACAC;QACAC;QACAF;QACA;QACA;QACA;QACA;QACA;QACA;QACAG;QACA;QACAC;QACAC;QACAA;QACAC;QACA;QACA;UACAlH;QACA;QACA;QACA;QACA;QACAmH;MACA;IACA;IACAC;MAAA;MAAA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;QACA;QACAC;QACAA;QACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;QACAxI;MACA;IAEA;IACAyI;MACA;MACA;MACA;MACApC;MACAxD;QACA2B;QACArD;UACA;YACA;cACA;gBAAA;gBACAkF;gBACA;cACA;gBACAA;gBACA;YAAA;UAEA;YACAA;UACA;QACA;QACA5B,0BACA;MACA;MACA;MACA;MACA;IACA;IACAiE;MAAA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACAC;MACAC;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC92BA;AAAA;AAAA;AAAA;AAAogC,CAAgB,04BAAG,EAAC,C;;;;;;;;;;;ACAxhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-image-upload/ut-image-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-image-upload.vue?vue&type=template&id=fe64dc84&\"\nvar renderjs\nimport script from \"./ut-image-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-image-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-image-upload.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-image-upload/ut-image-upload.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=template&id=fe64dc84&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.uploadLists, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = /.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url)\n    var m0 = !g0 ? _vm.showImg(item.url) : null\n    var m1 = !g0 ? _vm.checkLoad(index) : null\n    var m2 = !g0 && !m1 ? _vm.imageLoadingWidth() : null\n    var m3 = !g0 && !m1 ? _vm.imageLoadingWidth() : null\n    var m4 = !g0\n      ? !_vm.disabled &&\n        _vm.remove &&\n        item.progress == 100 &&\n        _vm.checkLoad(index)\n      : null\n    var m5 = !g0 && m4 ? _vm.checkLoad(index) : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      m5: m5,\n    }\n  })\n  var g1 = _vm.uploadLists.length < _vm.max && _vm.add\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ut-image-upload-list\">\r\n\t\t<view class=\"ut-image-upload-Item\"\r\n\t\t\t:style=\"{width:width+'rpx',height:height+'rpx',borderRadius:borderRadius+'rpx',padding:spacing+'rpx'}\"\r\n\t\t\tv-for=\"(item,index) in uploadLists\" :key=\"index\">\r\n\t\t\t<view class=\"ut-image-upload-Item-video\" :style=\"{borderRadius:borderRadius+'rpx'}\"\r\n\t\t\t\tv-if=\"(/.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url))\" @touchstart=\"touchstart(item,index,1)\"\r\n\t\t\t\t@touchend=\"touchend\">\r\n\t\t\t\t<video :disabled=\"false\" :controls=\"false\" :src=\"item.url\">\r\n\t\t\t\t\t<cover-view class=\"ut-image-upload-Item-video-fixed\" :style=\"{borderRadius:borderRadius+'rpx'}\"\r\n\t\t\t\t\t\t@click=\"previewVideoClick(item.url)\">\r\n\t\t\t\t\t\t<cover-view class=\"ut-image-upload-Item-del-cover image-show\"\r\n\t\t\t\t\t\t\tv-if=\"false && remove && previewVideoSrc==''\" @click.stop=\"imgDel(item,index)\">×</cover-view>\r\n\t\t\t\t\t</cover-view>\r\n\t\t\t\t</video>\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"image-loading-box\" @touchstart=\"touchstart(item,index,0)\" @touchend=\"touchend\">\r\n\t\t\t\t<image :lazy-load=\"true\" class=\"image-real\" :src=\"showImg(item.url)\"\r\n\t\t\t\t\t@click=\"imgPreviewClick(item,index)\" :style=\"{borderRadius:borderRadius+'rpx'}\" :mode=\"mode\"\r\n\t\t\t\t\t@load=\"imageLoad(index)\"></image>\r\n\t\t\t\t<view class=\"image-loading\" v-if=\"!checkLoad(index)\">\r\n\t\t\t\t\t<view class=\"loader\">\r\n\t\t\t\t\t\t<view class=\"loaders\">\r\n\t\t\t\t\t\t\t<view class=\"loader2\"\r\n\t\t\t\t\t\t\t\t:style=\"{width:imageLoadingWidth()+'rpx',height:imageLoadingWidth()+'rpx'}\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t加载中\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ut-image-upload-progress-text\" v-if=\"item.progress!=100\" :style=\"{lineHeight:height+'rpx'}\">{{item.progress}}%</view>\r\n\t\t\t\t<view class=\"ut-image-upload-progress\" v-if=\"item.progress!=100\" @click=\"imgPreviewClick(item,index)\" :style=\"{lineHeight:height+'rpx',opacity:1-item.progress/100}\"></view>\r\n\t\t\t\t<view class=\"ut-image-upload-Item-del\" :class=\"{'image-show':checkLoad(index)}\"\r\n\t\t\t\t\tv-if=\"!disabled && remove && item.progress==100 && checkLoad(index)\" @click=\"imgDel(item,index)\">×</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"ut-image-upload-Item\"\r\n\t\t\t:style=\"{width:width+'rpx',height:height+'rpx',padding:spacing+'rpx'}\"\r\n\t\t\tv-if=\"uploadLists.length<max && add\" @click=\"chooseFile\">\r\n\t\t\t<view class=\" ut-image-upload-Item-add\" :style=\"{borderRadius:borderRadius+'rpx',lineHeight:(height-2*spacing)+'rpx'}\">\r\n\t\t\t\t<text v-if=\"!$slots.default\">+</text>\r\n\t\t\t\t<slot name=\"default\" v-else></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"preview-full\" v-if=\"previewVideoSrc!=''\">\r\n\t\t\t<video :autoplay=\"true\" :src=\"previewVideoSrc\" :show-fullscreen-btn=\"false\">\r\n\t\t\t\t<cover-view class=\"preview-full-close\" @click=\"previewVideoClose\"> ×\r\n\t\t\t\t</cover-view>\r\n\t\t\t</video>\r\n\t\t</view>\r\n\r\n\r\n\t\t<!--  -->\r\n\t</view>\r\n</template>\r\n\r\n<!-- 使用方法 -->\r\n<!-- <ut-image-upload\r\n\tname=\"file\"\r\n\tv-model=\"myPhotos\"   \t\t\t\t\t\t\t['url']\r\n\tmediaType=\"video\"\t\t\t\t\t\t\t\timage video all\r\n\t:colors=\"colors\"\r\n\t:max=\"1\"\r\n\t:headers=\"headers\" \t\t\t\t\t\t\t\t{Token}\r\n\t:action=\"uploadInfo.server+uploadInfo.single\" \t上传地址uploadUrl\r\n\t:preview-image-width=\"1200\"\t\t\t\t\t\t预览图片宽度\r\n\t:width=\"150\"\t\t\t\t\t\t\t\t\t方格宽度\r\n\t:height=\"120\"\t\t\t\t\t\t\t\t\t方格高度\r\n\t:border-radius=\"8\"\t\t\t\t\t\t\t\t圆角\r\n\t@uploadSuccess=\"uploadSuccess\" \t\t\t\t\t成功事件\r\n\t@imgDelete=\"imgDelete\">\t\t\t\t\t\t\t删除事件\r\n\t<template v-slot:default>\r\n\t\t<view class=\"camera\">\r\n\t\t\t<u-icon name=\"camera-fill\" color=\"#ccc\" size=\"28\"></u-icon>\r\n\t\t\t<text class=\"text\">正面照片</text>\r\n\t\t</view>\r\n\t</template>\r\n</ut-image-upload> -->\r\n<!-- uploadSuccess(uploadFileRes){\r\n\r\n\t\tuploadFileRes.forEach(r=>{\r\n\t\t\t\t\tlet item=r.data\r\n\t\t\t\t\tif(item) this.myPhotos.push(this.uploadInfo.preview  + '?file='+item.name+item.ext)\r\n\t\t\t\t})\r\n\t\t\t\t\r\n},\r\nimgDelete(e){\r\n}, -->\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'ut-image-upload',\r\n\t\tprops: {\r\n\t\t\twidth: {\r\n\t\t\t\ttype: [Number,String],\r\n\t\t\t\tdefault: 120,\r\n\t\t\t},\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: [Number,String],\r\n\t\t\t\tdefault: 120,\r\n\t\t\t},\r\n\t\t\tborderRadius: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 8,\r\n\t\t\t},\r\n\t\t\tdataType: { //v-model的数据结构类型\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0, // 0: ['http://xxxx.jpg','http://xxxx.jpg'] 1:[{type:0,url:'http://xxxx.jpg'}]  type 0 图片 1 视频 url 文件地址 此类型是为了给没有文件后缀的状况使用的\r\n\t\t\t},\r\n\t\t\tmax: { //展示图片最大值\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 1,\r\n\t\t\t},\r\n\t\t\tchooseNum: { //选择图片数\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 9,\r\n\t\t\t},\r\n\t\t\tname: { //发到后台的文件参数名\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'file',\r\n\t\t\t},\r\n\t\t\tremove: { //是否展示删除按钮\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\tadd: { //是否展示添加按钮\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\tdisabled: { //是否禁用\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\tsourceType: { //选择照片来源 【ps：H5就别费劲了，设置了也没用。不是我说的，官方文档就这样！！！】\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => ['album', 'camera'],\r\n\t\t\t},\r\n\t\t\taction: { //上传服务器\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\theaders: { //上传的请求头部\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tformData: { //HTTP 请求中其他额外的 form data\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tcompress: { //是否需要压缩\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\tquality: { //压缩质量，范围0～100\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 100,\r\n\t\t\t},\r\n\t\t\tvalue: { //受控图片列表\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => [],\r\n\t\t\t},\r\n\t\t\tuploadSuccess: {\r\n\t\t\t\tdefault: (res) => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tsuccess: false,\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tmediaType: { //文件类型 image/video/all\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'image',\r\n\t\t\t},\r\n\t\t\tmaxDuration: { //拍摄视频最长拍摄时间，单位秒。最长支持 60 秒。 (只针对拍摄视频有用)\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 60,\r\n\t\t\t},\r\n\t\t\tcamera: { //'front'、'back'，默认'back'(只针对拍摄视频有用)\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'back',\r\n\t\t\t},\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'aspectFill',\r\n\t\t\t},\r\n\t\t\tlongPress: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\t// listIndex: {\r\n\t\t\t// \ttype: Number,\r\n\t\t\t// \tdefault: -1,\r\n\t\t\t// },\r\n\t\t\tpreviewImageWidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 750,\r\n\t\t\t},\r\n\t\t\tspacing:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:10,\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tislongPress: false,\r\n\t\t\t\timageLoaded: {},\r\n\t\t\t\tuploadLists: [],\r\n\t\t\t\tmediaTypeData: ['image', 'video', 'all'],\r\n\t\t\t\tpreviewVideoSrc: '',\r\n\t\t\t\t// 储存上传文件的请求 用于整理思路\r\n\t\t\t\tuploadTasks: [],\r\n\t\t\t\t// 保存父组件/页面的监听事件\r\n\t\t\t\tuploadTask: {\r\n\t\t\t\t\t// 默认不监听 调用setUpMonitor函数则监听\r\n\t\t\t\t\tload: false\r\n\t\t\t\t},\r\n\t\t\t\tuploadData:[],\r\n\t\t\t\t// 保存当前所有任务的进度 用于返回父组件/页面的监听事件\r\n\t\t\t\tuploadTaskProgress: [],\r\n\t\t\t\tisLoaded:false,\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted: function() {\r\n\t\t\tthis.$nextTick(function() {\r\n\t\t\t\tthis.uploadLists = []\r\n\t\t\t\tthis.value.forEach(item => {\r\n\t\t\t\t\tif(typeof item==='object'){\r\n\t\t\t\t\t\tthis.uploadLists.push({\r\n\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\turl: item.url,\r\n\t\t\t\t\t\t\tstate:6,\r\n\t\t\t\t\t\t\tprogress: 100,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.uploadLists.push({\r\n\t\t\t\t\t\t\tid: this.guid(),\r\n\t\t\t\t\t\t\turl: item,\r\n\t\t\t\t\t\t\tstate:6,\r\n\t\t\t\t\t\t\tprogress: 100,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t//this.uploadLists = this.value;\r\n\r\n\t\t\t\tif (this.mediaTypeData.indexOf(this.mediaType) == -1) {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\tcontent: 'mediaType参数不正确',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue(val, oldVal) {\r\n\t\t\t\tif(this.isLoaded) return\r\n\t\t\t\tif(!oldVal.length && !val.length){\r\n\t\t\t\t\tthis.isLoaded=true\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.uploadLists = []\r\n\t\t\t\tval.forEach(item => {\r\n\t\t\t\t\tthis.uploadLists.push({\r\n\t\t\t\t\t\tid: this.guid(),\r\n\t\t\t\t\t\turl: item,\r\n\t\t\t\t\t\tstate:6,\r\n\t\t\t\t\t\tprogress: 100,\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\tthis.isLoaded=true\r\n\t\t\t},\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\timageLoadingWidth() {\r\n\t\t\t\tif (this.width > this.height) {\r\n\t\t\t\t\treturn this.height > 40 ? this.height / 3 : 40\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn this.width > 40 ? this.width / 3 : 40\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageLoad(index) {\r\n\t\t\t\tthis.$set(this.imageLoaded, 'l_' + index, true)\r\n\t\t\t},\r\n\t\t\tcheckLoad(index) {\r\n\t\t\t\tif (this.imageLoaded['l_' + index]) return true\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\tisVideo(item) {\r\n\t\t\t\tlet isPass = false\r\n\t\t\t\tif ((!/.(gif|jpg|jpeg|png|gif|jpg|png)$/i.test(item) && this.dataType == 0) || (this.dataType == 1 && item\r\n\t\t\t\t\t\t.type == 1)) {\r\n\t\t\t\t\tisPass = true\r\n\t\t\t\t}\r\n\t\t\t\treturn isPass\r\n\t\t\t},\r\n\t\t\tgetFileUrl(item) {\r\n\t\t\t\tvar url = item;\r\n\t\t\t\tif (this.dataType == 1) {\r\n\t\t\t\t\turl = item.url\r\n\t\t\t\t}\r\n\t\t\t\treturn url\r\n\t\t\t},\r\n\t\t\tpreviewVideoClick(src) {\r\n\t\t\t\tif (!this.islongPress) {\r\n\t\t\t\t\tthis.previewVideo(src)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpreviewVideo(src) {\r\n\t\t\t\tthis.previewVideoSrc = src;\r\n\t\t\t},\r\n\t\t\tpreviewVideoClose() {\r\n\t\t\t\tthis.previewVideoSrc = ''\r\n\t\t\t},\r\n\t\t\timgDel(item,index) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '您确定要删除么?',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tlet delObj = this.uploadLists[index]\r\n\t\t\t\t\t\t\t// console.log(index)\r\n\t\t\t\t\t\t\tthis.uploadLists.splice(index, 1)\r\n\t\t\t\t\t\t\tlet arr=[]\r\n\t\t\t\t\t\t\tthis.uploadLists.forEach(item=>{\r\n\t\t\t\t\t\t\t\tif(item.state==6)arr.push(item.url)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.$emit(\"input\", arr);\r\n\t\t\t\t\t\t\tthis.$emit(\"imgDelete\", {\r\n\t\t\t\t\t\t\t\turl: delObj.url,\r\n\t\t\t\t\t\t\t\tid: delObj.id\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else if (res.cancel) {}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdelIndex(index){\r\n\t\t\t\tthis.uploadLists.splice(index, 1)\r\n\t\t\t},\r\n\t\t\tclear(){\r\n\t\t\t\tthis.uploadLists=[]\r\n\t\t\t},\r\n\t\t\timgPreviewClick(item, index) {\r\n\t\t\t\tif (!this.islongPress) {\r\n\t\t\t\t\tthis.imgPreview(item.url, index)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timgPreview(url, index) {\r\n\t\t\t\tvar imgData = this.uploadLists.filter(item => !/.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url)) //只预览图片的\r\n\t\t\t\tlet arr = []\r\n\t\t\t\timgData.forEach(item => {\r\n\t\t\t\t\tif (!item) return true\r\n\t\t\t\t\tif(typeof item.url==='object' && item.url!==null){\r\n\t\t\t\t\t\tif (item.url.url.indexOf('oss.') > 0) {\r\n\t\t\t\t\t\t\tarr.push(item.url.url + \"&width=\" + this.previewImageWidth + \"&quality=70\")\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tarr.push(item.url.url)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tif (item.url.indexOf('oss.') > 0) {\r\n\t\t\t\t\t\t\tarr.push(item.url + \"&width=\" + this.previewImageWidth + \"&quality=70\")\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tarr.push(item.url)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: arr,\r\n\t\t\t\t\tcurrent: index,\r\n\t\t\t\t\tloop: true,\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchooseFile() {\r\n\t\t\t\tif (this.disabled) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.uploadData=[]\r\n\t\t\t\tswitch (this.mediaTypeData.indexOf(this.mediaType)) {\r\n\t\t\t\t\tcase 1: //视频\r\n\t\t\t\t\t\tthis.videoAdd();\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2: //全部\r\n\t\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\t\titemList: ['相册', '视频'],\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\t\t\tthis.videoAdd();\r\n\t\t\t\t\t\t\t\t} else if (res.tapIndex == 0) {\r\n\t\t\t\t\t\t\t\t\tthis.imgAdd();\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault: //图片\r\n\t\t\t\t\t\tthis.imgAdd();\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvideoAdd() {\r\n\t\t\t\tlet nowNum = Math.abs(this.uploadLists.length - this.max);\r\n\t\t\t\tlet thisNum = (this.chooseNum > nowNum ? nowNum : this.chooseNum) //可选数量\r\n\t\t\t\tuni.chooseVideo({\r\n\t\t\t\t\tcompressed: this.compress,\r\n\t\t\t\t\tsourceType: this.sourceType,\r\n\t\t\t\t\tcamera: this.camera,\r\n\t\t\t\t\tmaxDuration: this.maxDuration,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tthis.chooseSuccessMethod([res.tempFilePath], 1)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\timgAdd() {\r\n\t\t\t\tlet nowNum = Math.abs(this.uploadLists.length - this.max);\r\n\t\t\t\tlet thisNum = (this.chooseNum > nowNum ? nowNum : this.chooseNum) //可选数量\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (this.sourceType.length > 1) {\r\n\t\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\t\titemList: ['拍摄', '从手机相册选择'],\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\t\tthis.appGallery(thisNum);\r\n\t\t\t\t\t\t\t} else if (res.tapIndex == 0) {\r\n\t\t\t\t\t\t\t\tthis.appCamera();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (this.sourceType.length == 1 && this.sourceType.indexOf('album') > -1) {\r\n\t\t\t\t\tthis.appGallery(thisNum);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (this.sourceType.length == 1 && this.sourceType.indexOf('camera') > -1) {\r\n\t\t\t\t\tthis.appCamera();\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t//#ifndef APP-PLUS\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: thisNum,\r\n\t\t\t\t\tsizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n\t\t\t\t\tsourceType: this.sourceType,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tthis.chooseSuccessMethod(res.tempFilePaths, 0)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tappCamera() {\r\n\t\t\t\tvar cmr = plus.camera.getCamera();\r\n\t\t\t\tvar res = cmr.supportedImageResolutions[0];\r\n\t\t\t\tvar fmt = cmr.supportedImageFormats[0];\r\n\t\t\t\tcmr.captureImage((path) => {\r\n\t\t\t\t\t\tthis.chooseSuccessMethod([path], 0)\r\n\t\t\t\t\t},\r\n\t\t\t\t\t(error) => {\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tresolution: res,\r\n\t\t\t\t\t\tformat: fmt\r\n\t\t\t\t\t}\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\tappGallery(maxNum) {\r\n\t\t\t\tplus.gallery.pick((res) => {\r\n\t\t\t\t\tthis.chooseSuccessMethod(res.files, 0)\r\n\t\t\t\t}, function(e) {\r\n\t\t\t\t}, {\r\n\t\t\t\t\tfilter: \"image\",\r\n\t\t\t\t\tmultiple: true,\r\n\t\t\t\t\tmaximum: maxNum\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tchooseSuccessMethod(filePaths, type) {\r\n\t\t\t\tif (this.action == '') { //未配置上传路径\r\n\t\t\t\t\tthis.$emit(\"chooseSuccess\", filePaths, type); //filePaths 路径 type 0 为图片 1为视频\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (type == 1) {\r\n\t\t\t\t\tthis.imgUpload(filePaths, type);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.compress) { //设置了需要压缩\r\n\t\t\t\t\t\tthis.imgCompress(filePaths, type);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.imgUpload(filePaths, type);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\timgCompress(tempFilePaths, type) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '压缩中...'\r\n\t\t\t\t});\r\n\r\n\t\t\t\tlet compressImgs = [];\r\n\t\t\t\tlet results = [];\r\n\t\t\t\ttempFilePaths.forEach((item, index) => {\r\n\t\t\t\t\tcompressImgs.push(new Promise((resolve, reject) => {\r\n\t\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t\tuni.compressImage({\r\n\t\t\t\t\t\t\tsrc: item,\r\n\t\t\t\t\t\t\tquality: this.quality,\r\n\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\tresults.push(res.tempFilePath);\r\n\t\t\t\t\t\t\t\tresolve(res.tempFilePath);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t\t\t//uni.hideLoading();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\tthis.canvasDataURL(item, {\r\n\t\t\t\t\t\t\tquality: this.quality / 100\r\n\t\t\t\t\t\t}, (base64Codes) => {\r\n\t\t\t\t\t\t\tlet tempPath = this.parseBlob(base64Codes)\r\n\t\t\t\t\t\t\tresults.push(tempPath);\r\n\t\t\t\t\t\t\tresolve(tempPath);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t}))\r\n\t\t\t\t})\r\n\t\t\t\tPromise.all(compressImgs) //执行所有需请求的接口\r\n\t\t\t\t\t.then((results) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.imgUpload(results, type);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((res, object) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\timgUpload(tempFilePaths, type) {\r\n\t\t\t\tif (this.action == 'uniCloud') {\r\n\t\t\t\t\tthis.uniCloudUpload(tempFilePaths, type)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '上传中'\r\n\t\t\t\t});\r\n\r\n\t\t\t\tlet that = this\r\n\t\t\t\t\r\n\t\t\t\ttempFilePaths.forEach(item=>{\r\n\t\t\t\t\tlet obj = {\r\n\t\t\t\t\t\tid: that.guid(),\r\n\t\t\t\t\t\tstate:0,\r\n\t\t\t\t\t\tprogress: 0,\r\n\t\t\t\t\t\turl: item,\r\n\t\t\t\t\t\tdata: []\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// console.log(obj)\r\n\t\t\t\t\tthat.uploadLists.push(obj)\r\n\t\t\t\t\t//that.$emit(\"uploadSuccess\", obj)\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\r\n\t\t\t\tlet uploadImgs = []\r\n\t\t\t\tconst arr=that.uploadLists.filter(u=>u.state==0)\r\n\t\t\t\tarr.forEach((item, index) => {\r\n\t\t\t\t\tconst promise = new Promise((resolve, reject) => {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tlet task = uni.uploadFile({\r\n\t\t\t\t\t\t\turl: that.action, //仅为示例，非真实的接口地址\r\n\t\t\t\t\t\t\tfilePath: item.url,\r\n\t\t\t\t\t\t\tname: that.name,\r\n\t\t\t\t\t\t\tfileType: 'image',\r\n\t\t\t\t\t\t\tformData: that.formData,\r\n\t\t\t\t\t\t\theader: that.headers,\r\n\t\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\t\tlet obj=that.uploadLists.find(u=>u.id==task.id)\r\n\t\t\t\t\t\t\t\tif(obj)\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tconst _res = JSON.parse(uploadFileRes.data);\r\n\t\t\t\t\t\t\t\t\tthat.$set(obj, 'progress', 100)\r\n\t\t\t\t\t\t\t\t\tthat.$set(obj, 'data', _res[0])\r\n\t\t\t\t\t\t\t\t\tthat.$set(obj, 'state', 2)\r\n\t\t\t\t\t\t\t\t\tthis.uploadData.push(obj)\r\n\t\t\t\t\t\t\t\t\t// that.$emit(\"uploadSuccess\", obj)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tresolve(obj)\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\t\tthat.$emit(\"uploadFail\", err);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete: () => {}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\ttask.id=item.id\r\n\t\t\t\t\t\ttask.onProgressUpdate((res) => {\r\n\t\t\t\t\t\t\tif(res.progress!=100)\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tlet obj=this.uploadLists.find(u=>u.id==task.id)\r\n\t\t\t\t\t\t\t\tif(obj)\r\n\t\t\t\t\t\t\t\t {\r\n\t\t\t\t\t\t\t\t\t that.$set(obj, 'progress', res.progress)\r\n\t\t\t\t\t\t\t\t\t that.$set(obj, 'state', 1)\r\n\t\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\tuploadImgs.push(promise)\r\n\t\t\t\t})\r\n\t\t\t\tPromise.all(uploadImgs) //执行所有需请求的接口\r\n\t\t\t\t\t.then((results) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t//  console.log('uploadList',this.uploadLists)\r\n\t\t\t\t\t\t//  console.log('uploadData',this.uploadData)\r\n\t\t\t\t\t\t// // return\r\n\t\t\t\t\t\t// // uni.hideLoading();\r\n\t\t\t\t\t\t// // console.log(this.uploadLists,this.uploadData)\r\n\t\t\t\t\t\t// let arr=[]\r\n\t\t\t\t\t\t// this.uploadData.forEach(item=>{\r\n\t\t\t\t\t\t// \tlet obj = this.uploadData.find(u=>u.id==item.id)\r\n\t\t\t\t\t\t// \tif(!obj) arr.push(item)\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t// console.log(arr)\r\n\t\t\t\t\t\tthat.$emit(\"uploadSuccess\", this.uploadData)\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((res, object) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$emit(\"uploadFail\", res);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t},\r\n\t\t\tuniCloudUpload(tempFilePaths, type) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '上传中'\r\n\t\t\t\t});\r\n\t\t\t\tlet uploadImgs = [];\r\n\t\t\t\ttempFilePaths.forEach((item, index) => {\r\n\t\t\t\t\tuploadImgs.push(new Promise((resolve, reject) => {\r\n\r\n\t\t\t\t\t\tuniCloud.uploadFile({\r\n\t\t\t\t\t\t\tfilePath: item,\r\n\t\t\t\t\t\t\tcloudPath: this.guid() + '.' + this.getFileType(item, type),\r\n\t\t\t\t\t\t\tsuccess(uploadFileRes) {\r\n\t\t\t\t\t\t\t\tif (uploadFileRes.success) {\r\n\t\t\t\t\t\t\t\t\tresolve(uploadFileRes.fileID);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete() {}\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t}))\r\n\t\t\t\t})\r\n\t\t\t\tPromise.all(uploadImgs) //执行所有需请求的接口\r\n\t\t\t\t\t.then((results) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\t\tuniCloud.getTempFileURL({\r\n\t\t\t\t\t\t\tfileList: results,\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tres.fileList.forEach(item => {\r\n\t\t\t\t\t\t\t\t\t//this.value.push(item.tempFileURL)\r\n\t\t\t\t\t\t\t\t\t// #ifndef VUE3\r\n\t\t\t\t\t\t\t\t\tthis.value.push(item.tempFileURL)\r\n\t\t\t\t\t\t\t\t\tthis.$emit(\"input\", this.value);\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t\t\t\t\t\tthis.modelValue.push(item.tempFileURL)\r\n\t\t\t\t\t\t\t\t\tthis.$emit(\"update:modelValue\", this.modelValue);\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail() {},\r\n\t\t\t\t\t\t\tcomplete() {}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((res, object) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetFileType(path, type) { //手机端默认图片为jpg 视频为mp4\r\n\t\t\t\t// #ifdef H5 \r\n\t\t\t\tvar result = type == 0 ? 'jpg' : 'mp4';\r\n\t\t\t\t// #endif\r\n\r\n\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tvar result = path.split('.').pop().toLowerCase();\r\n\t\t\t\t// #ifdef MP \r\n\t\t\t\tif (this.compress) { //微信小程序压缩完没有后缀\r\n\t\t\t\t\tresult = type == 0 ? 'jpg' : 'mp4';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn result;\r\n\t\t\t},\r\n\t\t\tguid() {\r\n\t\t\t\treturn 'xxxxxxxx-date-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n\t\t\t\t\tvar r = Math.random() * 16 | 0,\r\n\t\t\t\t\t\tv = c == 'x' ? r : (r & 0x3 | 0x8);\r\n\t\t\t\t\treturn v.toString(16);\r\n\t\t\t\t}).replace(/date/g, function(c) {\r\n\t\t\t\t\treturn Date.parse(new Date());\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 触发监听\r\n\t\t\t * @param {Number} key 当前任务的下标\r\n\t\t\t * @param {Object} uploadTask uni.uploadFile 的返回值\r\n\t\t\t */\r\n\t\t\ttriggerMonitor(key, uploadTask) {\r\n\t\t\t\tuploadTask.onProgressUpdate(res => {\r\n\t\t\t\t\t// 触发父组件/页面的监听事件\r\n\t\t\t\t\tthis.uploadTaskProgress[key] = res;\r\n\t\t\t\t\t// 合并所有任务的列表用于父组件/页面的监听\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t//\t// 当前的进度\r\n\t\t\t\t\t// \tprogress: 0,\r\n\t\t\t\t\t//\t// 当前的所有进度 保存每条任务的进度等等\r\n\t\t\t\t\t// \ttasks: []\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tthis.uploadTask.onProgressUpdate(this.mergerProgress(this.uploadTaskProgress));\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 合并进度\r\n\t\t\t * @param {Array} tasks 所有的任务\r\n\t\t\t */\r\n\t\t\tmergerProgress(tasks) {\r\n\t\t\t\tvar progress = 0;\r\n\t\t\t\ttasks.forEach((value, key) => {\r\n\t\t\t\t\tif (value) {\r\n\t\t\t\t\t\tprogress += value.progress;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tprogress += 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn {\r\n\t\t\t\t\tprogress: progress / tasks.length,\r\n\t\t\t\t\ttasks\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置父组件/页面的监听事件\r\n\t\t\t * onProgressBegin 开始监听触发事件\r\n\t\t\t * onProgressUpdate 进度变化事件\r\n\t\t\t * onProgressEnd 结束监听事件\r\n\t\t\t * [req 1,req 2...]\r\n\t\t\t * @param {Object} obj {onProgressUpdate,onProgressBegin,onProgressEnd}\r\n\t\t\t */\r\n\t\t\tsetUpMonitor(obj) {\r\n\t\t\t\tthis.uploadTask = {\r\n\t\t\t\t\tload: true,\r\n\t\t\t\t\t...obj\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcanvasDataURL(path, obj, callback) {\r\n\t\t\t\tvar img = new Image();\r\n\t\t\t\timg.src = path;\r\n\t\t\t\timg.onload = function() {\r\n\t\t\t\t\tvar that = this;\r\n\t\t\t\t\t// 默认按比例压缩\r\n\t\t\t\t\tvar w = that.width,\r\n\t\t\t\t\t\th = that.height,\r\n\t\t\t\t\t\tscale = w / h;\r\n\t\t\t\t\tw = obj.width || w;\r\n\t\t\t\t\th = obj.height || (w / scale);\r\n\t\t\t\t\tvar quality = 0.8; // 默认图片质量为0.8\r\n\t\t\t\t\t//生成canvas\r\n\t\t\t\t\tvar canvas = document.createElement('canvas');\r\n\t\t\t\t\tvar ctx = canvas.getContext('2d');\r\n\t\t\t\t\t// 创建属性节点\r\n\t\t\t\t\tvar anw = document.createAttribute(\"width\");\r\n\t\t\t\t\tanw.nodeValue = w;\r\n\t\t\t\t\tvar anh = document.createAttribute(\"height\");\r\n\t\t\t\t\tanh.nodeValue = h;\r\n\t\t\t\t\tcanvas.setAttributeNode(anw);\r\n\t\t\t\t\tcanvas.setAttributeNode(anh);\r\n\t\t\t\t\tctx.drawImage(that, 0, 0, w, h);\r\n\t\t\t\t\t// 图像质量\r\n\t\t\t\t\tif (obj.quality && obj.quality <= 1 && obj.quality > 0) {\r\n\t\t\t\t\t\tquality = obj.quality;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// quality值越小，所绘制出的图像越模糊\r\n\t\t\t\t\tvar base64 = canvas.toDataURL('image/jpeg', quality);\r\n\t\t\t\t\t// 回调函数返回base64的值\r\n\t\t\t\t\tcallback(base64);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowImg(url, size = 200, quality = 70) {\r\n\t\t\t\tlet urlTmp;\r\n\t\t\t\tif(typeof url==='object'){\r\n\t\t\t\t\turlTmp=url.url\r\n\t\t\t\t}else{\r\n\t\t\t\t\turlTmp=url\r\n\t\t\t\t}\r\n\t\t\t\tif (urlTmp.indexOf('oss.') > 0) {\r\n\t\t\t\t\tlet str = urlTmp\r\n\t\t\t\t\tstr += \"&width=\" + this.width\r\n\t\t\t\t\tstr += \"&height=\" + this.height\r\n\t\t\t\t\tif (quality) str += \"&quality=\" + quality\r\n\t\t\t\t\treturn str\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn urlTmp\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tparseBlob(base64) {\r\n\t\t\t\tvar arr = base64.split(',');\r\n\t\t\t\tvar mime = arr[0].match(/:(.*?);/)[1];\r\n\t\t\t\tvar bstr = atob(arr[1]);\r\n\t\t\t\tvar n = bstr.length;\r\n\t\t\t\tvar u8arr = new Uint8Array(n);\r\n\t\t\t\tfor (var i = 0; i < n; i++) {\r\n\t\t\t\t\tu8arr[i] = bstr.charCodeAt(i);\r\n\t\t\t\t}\r\n\t\t\t\tvar url = URL || webkitURL;\r\n\t\t\t\treturn url.createObjectURL(new Blob([u8arr], {\r\n\t\t\t\t\ttype: mime\r\n\t\t\t\t}));\r\n\r\n\t\t\t},\r\n\t\t\tlongpress(item, index, type) {\r\n\t\t\t\tlet itemList = ['预览']\r\n\t\t\t\tif (this.remove) itemList.push('删除')\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.islongPress = true\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: itemList,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tif (res.tapIndex == 0) {\r\n\t\t\t\t\t\t\tswitch (type) {\r\n\t\t\t\t\t\t\t\tcase 1: //视频\r\n\t\t\t\t\t\t\t\t\tthat.previewVideo(item.url)\r\n\t\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t\t\t\tthat.imgPreview(item.url, index)\r\n\t\t\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (res.tapIndex == 1) {\r\n\t\t\t\t\t\t\tthat.imgDel(item,index)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// uni.showModal({\r\n\t\t\t\t// \ttitle:'长按'+index\r\n\t\t\t\t// })\r\n\t\t\t},\r\n\t\t\ttouchstart(item, index, type) {\r\n\t\t\t\tif (!this.longPress && type != 1) return\r\n\t\t\t\tthis.timer = setTimeout(() => {\r\n\t\t\t\t\tthis.longpress(item, index, type)\r\n\t\t\t\t}, 1000)\r\n\t\t\t},\r\n\t\t\ttouchend() {\r\n\t\t\t\tclearTimeout(this.timer)\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.islongPress = false\r\n\t\t\t\t}, 200)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.image-real {\r\n\t\tanimation: fadenum .3s 1;\r\n\t}\r\n\r\n\t@keyframes fadenum {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 0.7;\r\n\t\t}\r\n\t}\r\n\r\n\t.ut-image-upload-Item-del.image-show {\r\n\t\topacity: 0.7;\r\n\t\tanimation: fadenum .8s 1;\r\n\t}\r\n\r\n\t.image-loading-box {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.image-loading {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.image-loading .loader {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\r\n\t.loaders {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 16rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t.loader2 {\r\n\t\tborder: 4rpx solid #f3f3f3;\r\n\t\tborder-radius: 50%;\r\n\t\tborder-top: 4rpx solid var(--colors);\r\n\r\n\t\tanimation: spin2 1s linear infinite;\r\n\t}\r\n\r\n\t@keyframes spin2 {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\r\n\t.preview-full {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 10001;\r\n\t}\r\n\r\n\t.preview-full video {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t}\r\n\r\n\t.preview-full-close {\r\n\t\tposition: fixed;\r\n\t\tleft: 32rpx;\r\n\t\ttop: 30rpx;\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 60rpx;\r\n\t\ttext-align: center;\r\n\t\tz-index: 3004;\r\n\t\t/* \tbackground-color: #808080; */\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 65rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 2px 5px rgb(0 0 0);\r\n\t}\r\n\r\n\r\n\r\n\t/* .preview-full-close-before,\r\n\t.preview-full-close-after {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\tcontent: '';\r\n\t\theight: 60rpx;\r\n\t\tmargin-top: -30rpx;\r\n\t\twidth: 6rpx;\r\n\t\tmargin-left: -3rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tz-index: 20000;\r\n\t}\r\n\r\n\t.preview-full-close-before {\r\n\t\ttransform: rotate(45deg);\r\n\r\n\t}\r\n\r\n\t.preview-full-close-after {\r\n\t\ttransform: rotate(-45deg);\r\n\r\n\t} */\r\n\r\n\t.ut-image-upload-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.ut-image-upload-Item {\r\n\t\t/* \t\twidth: 160rpx;\r\n\t\theight: 160rpx; */\r\n\t\t/* border-radius: 8rpx; */\r\n\t\t/* margin-right: 10rpx; */\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\toverflow: hidden;\r\n\t\t/* margin-bottom: 10rpx */\r\n\t}\r\n\t\r\n\t.ut-image-upload-Item:last-child{\r\n\t\tmargin-right: 0;\r\n\t}\r\n\r\n\t.ut-image-upload-Item image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.ut-image-upload-Item-video {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\r\n\t.ut-image-upload-Item-video-fixed {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 1;\r\n\r\n\t}\r\n\r\n\t.ut-image-upload-Item video {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 10rpx;\r\n\r\n\t}\r\n\r\n\t.ut-image-upload-Item-add {\r\n\t\tfont-size: 105rpx;\r\n\t\t/* line-height: 160rpx; */\r\n\t\ttext-align: center;\r\n\t\tborder: 1px dashed #d9d9d9;\r\n\t\tcolor: #d9d9d9;\r\n\t\twidth:100%;\r\n\t\theight: 100%;\r\n\t\t/* line-height: 160rpx; */\r\n\t}\r\n\r\n\t.ut-image-upload-Item-del {\r\n\t\tbackground-color: var(--colors);\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: absolute;\r\n\t\twidth: 35rpx;\r\n\t\theight: 35rpx;\r\n\t\tline-height: 35rpx;\r\n\t\ttext-align: center;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 9;\r\n\t\tcolor: #fff;\r\n\t\topacity: 0.7;\r\n\r\n\t}\r\n\r\n\r\n\r\n\t.ut-image-upload-Item-del-cover {\r\n\t\tbackground-color: var(--colors);\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: absolute;\r\n\t\twidth: 35rpx;\r\n\t\theight: 35rpx;\r\n\t\ttext-align: center;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tcolor: #fff;\r\n\t\t/* #ifdef APP-PLUS */\r\n\t\tline-height: 25rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-PLUS */\r\n\t\tline-height: 35rpx;\r\n\t\t/* #endif */\r\n\t\tz-index: 2;\r\n\t\topacity: 0.7;\r\n\t}\r\n\t\r\n\t.ut-image-upload-progress, .ut-image-upload-progress-text{\r\n\t\tposition: absolute;\r\n\t\tleft:0;\r\n\t\ttop: 0;\r\n\t\tcolor: var(--colors);\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\t\r\n\t}\r\n\t\r\n\t.ut-image-upload-progress{\r\n\t\tbackground-color: #fff;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360663580\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}