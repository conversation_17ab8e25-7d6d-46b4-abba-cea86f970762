{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-grid/ut-grid.vue?a578", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-grid/ut-grid.vue?9ba1", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-grid/ut-grid.vue?0c60", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-grid/ut-grid.vue?c248", "uni-app:///components/ut/ut-grid/ut-grid.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-grid/ut-grid.vue?e779", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-grid/ut-grid.vue?676e"], "names": ["name", "components", "props", "colors", "type", "textColor", "count", "default", "list", "cellColor", "opacity", "jump<PERSON><PERSON><PERSON>", "computed", "cellStyle", "background", "borderRadius", "padding", "data", "methods", "jump", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCoBvtB;EACAA;EACAC,aACA;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;IACA;IACAE;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;QAAA;MAAA;IACA;IACAE;MACAL;MACAG;IACA;IACAG;MACAN;MACAG;IACA;IACAI;MACAP;MACAG;IACA;EAEA;EACAK;IACAC;MACA;MACA;QACA;UAAAC;UAAAC;UAAAC;QAAA;MACA;QACA;UAAAF;UAAAC;UAAAC;QAAA;MACA;IACA;EACA;EAEAC;IACA,QAEA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACAC;QACA;QACA;QACA;QACA;MACA;QACA;UACA;QACA;UACA;QACA;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAA0zC,CAAgB,ooCAAG,EAAC,C;;;;;;;;;;;ACA90C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-grid/ut-grid.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-grid.vue?vue&type=template&id=117db0e4&\"\nvar renderjs\nimport script from \"./ut-grid.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-grid.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-grid.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-grid/ut-grid.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-grid.vue?vue&type=template&id=117db0e4&\"", "var components\ntry {\n  components = {\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-badge/u-badge\" */ \"@/components/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n    \"u-Image\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--image/u--image\" */ \"@/components/uview-ui/components/u--image/u--image.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-grid.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-grid.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"menu-box\">\r\n\t\t<view v-for=\"(item, index) in list\" :key=\"index\" class=\"menu-item\" @tap=\"jump(item)\" :style=\"{width:count>4?100/count:25+'%'}\">\r\n\t\t\t<view class=\"menu-item-cell\" :style=\"cellStyle\">\r\n\t\t\t\t<u-badge :absolute=\"true\" :offset=\"['-6rpx','-4rpx']\" max=\"99\" :bgColor=\"colors\" :value=\"item.num\"></u-badge>\r\n\t\t\t\t<text class=\"iconfont\" :style=\"{color:colors}\" :class=\"item.icon\" v-if=\"!item.image\"></text>\r\n\t\t\t\t<view v-else class=\"menu-item-image\">\r\n\t\t\t\t\t<u--image :showLoading=\"true\" :src=\"item.image\" width=\"64rpx\" height=\"64rpx\" style=\"padding: 12rpx;\" radius=\"4rpx\"></u--image>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t\t<view :style=\"{color:textColor?textColor:colors}\" >{{item.name}}</view>\r\n\t\t\t<view v-if=\"item.star\" class=\"star\">\r\n\t\t\t\t<u-icon name=\"star-fill\" :color=\"colors\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\n</template>\n\n<script>\r\n\texport default {\r\n\t\tname:'ut-grid',\r\n\t\tcomponents: {\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\ttextColor:{\r\n\t\t\t\ttype:String\r\n\t\t\t},\r\n\t\t\tcount:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:4,\r\n\t\t\t},\r\n\t\t\tlist:{\r\n\t\t\t\ttype:Array,\r\n\t\t\t\tdefault:()=>[],\r\n\t\t\t},\r\n\t\t\tcellColor:{\r\n\t\t\t\ttype:[Boolean,String],\r\n\t\t\t\tdefault:false,\r\n\t\t\t},\r\n\t\t\topacity:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:20,\r\n\t\t\t},\r\n\t\t\tjumpParam:{\r\n\t\t\t\ttype:Object,\r\n\t\t\t\tdefault:()=>{},\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcellStyle(){\r\n\t\t\t\tif(this.cellColor==false || !this.cellColor) return {}\r\n\t\t\t\tif(typeof this.cellColor ==='string'){\r\n\t\t\t\t\treturn {background:this.cellColor,borderRadius:'8rpx',padding:'2rpx'}\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn {background:this.colors+this.opacity,borderRadius:'8rpx',padding:'2rpx'}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tjump(item){\r\n\t\t\t\tif(!item)return\r\n\t\t\t\tif(item.isTab){\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl:item.url\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// uni.switchTab({\r\n\t\t\t\t\t// \turl:item.url,\r\n\t\t\t\t\t// })\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(this.jumpParam){\r\n\t\t\t\t\t\tthis.$tools.routerTo(item.url,this.jumpParam)\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthis.$tools.routerTo(item.url)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\n\n<style lang=\"scss\">\r\n\t.menu-box {\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: flex-start;\r\n\t}\r\n\t\r\n\t.menu-item-image{\r\n\t}\r\n\t\r\n\t.menu-item {\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content:flex-start;\r\n\t\talign-items: center;\r\n\t\tmargin: 10upx 0 20upx;\r\n\t\tpadding: 20upx 0 10upx;\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #75787d;\r\n\t\tline-height: 40upx;\r\n\t\tposition: relative;\r\n\r\n\t\t\r\n\t\t.menu-item-cell{\r\n\t\t\tpadding: 10rpx;\r\n\t\t\tposition: relative;\r\n\t\t\t.iconfont{\r\n\t\t\t\tfont-size: 70rpx;\r\n\t\t\t\tpadding: 12px 0;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t\t.u-badge{\r\n\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\topacity:0.85;\r\n\t\t\t\tz-index: 1;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n\t\r\n\t.star{\r\n\t\tposition: absolute;\r\n\t\tleft: 30rpx;\r\n\t\ttop: 5rpx;\r\n\t}\n</style>\r\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-grid.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-grid.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360672431\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}