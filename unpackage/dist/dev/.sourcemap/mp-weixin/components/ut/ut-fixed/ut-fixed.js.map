{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-fixed/ut-fixed.vue?1bc6", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-fixed/ut-fixed.vue?9d64", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-fixed/ut-fixed.vue?2eb1", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-fixed/ut-fixed.vue?bbbf", "uni-app:///components/ut/ut-fixed/ut-fixed.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-fixed/ut-fixed.vue?470f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-fixed/ut-fixed.vue?8776"], "names": ["name", "mixins", "props", "position", "type", "default", "top", "bottom", "fixed", "zIndex", "background", "safeAreaInset", "data", "isFixed", "width", "height", "windowTop", "windowBottom", "computed", "wrap_style", "style", "c_style", "offset", "otherHeight", "refreshState", "watch", "created", "mounted", "methods", "refresh"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAosB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACWxtB;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;IACAC;IACAC;EACA;EACAC;IAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EAAA;EACAC;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAA;MACA;IACA;IACAC;MACA;QACAX;MACA;MACA;QACAU;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA,QACA,YACA,eACA,UACA,aACA,mBACA;IACA;EACA;EACAC;IACAD;MACA;IACA;EACA;EACAE;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;QACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3HA;AAAA;AAAA;AAAA;AAAm1C,CAAgB,6pCAAG,EAAC,C;;;;;;;;;;;ACAv2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-fixed/ut-fixed.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-fixed.vue?vue&type=template&id=9e6eae8c&scoped=true&\"\nvar renderjs\nimport script from \"./ut-fixed.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-fixed.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-fixed.vue?vue&type=style&index=0&id=9e6eae8c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9e6eae8c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-fixed/ut-fixed.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-fixed.vue?vue&type=template&id=9e6eae8c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-fixed.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-fixed.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view :style=\"wrap_style\">\r\n    <view :id=\"$sUid\" class=\"ut-fixed\" :class=\"custom_class\" :style=\"c_style\">\r\n      <view v-if=\"fixed && safeAreaInset && position === 'top'\" style=\"height: var(--safe-area-inset-top)\"></view>\r\n      <slot />\r\n      <view v-if=\"fixed && safeAreaInset && position === 'bottom'\" style=\"height: var(--safe-area-inset-bottom)\"></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport componentMixin from './mixins/componentMixin';\r\n\r\n/**\r\n * s-fixed 固定定位\r\n * @description 固定某个元素到一个位置\r\n * @property {String} position = [top|bottom] 固定方向\r\n * @property {Number|String} top 顶部的距离，单位rpx\r\n * @property {Number|String} bottom 底部的距离，单位rpx\r\n * @property {Boolean} fixed 启用固定功能\r\n * @property {Number|String} zIndex z-index\r\n * @property {String} background 背景色\r\n * @property {Boolean} safeAreaInset 是否空出底部或顶部安全距离\r\n * @example <s-fixed></s-fixed>\r\n */\r\nexport default {\r\n  name: 'UtFixed',\r\n  mixins: [componentMixin],\r\n  props: {\r\n    position: {\r\n      type: String,\r\n      default: 'bottom',\r\n    },\r\n    top: {\r\n      type: [Number, String],\r\n      default: 0,\r\n    },\r\n    bottom: {\r\n      type: [Number, String],\r\n      default: 0,\r\n    },\r\n    fixed: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    zIndex: [Number, String],\r\n    background: String,\r\n    safeAreaInset: Boolean,\r\n  },\r\n  data: () => ({\r\n    isFixed: false,\r\n    width: 0,\r\n    height: 0,\r\n    windowTop: 0,\r\n    windowBottom: 0,\r\n  }),\r\n  computed: {\r\n    wrap_style() {\r\n      const style = {};\r\n      if (this.fixed && !this.isFixed) {\r\n        style.opacity = 0;\r\n      } else if (this.isFixed) {\r\n        style.height = this.height + 'px';\r\n      }\r\n\t  style.display='inline'\r\n      return this.$mergeStyle(style);\r\n    },\r\n    c_style() {\r\n      const style = {\r\n        background: this.background,\r\n      };\r\n      if (this.isFixed) {\r\n        style.position = 'fixed';\r\n        style.width = this.width + 'px';\r\n        style[this.position] = (this.offset + this.otherHeight) + 'px';\r\n        style.zIndex = this.zIndex;\r\n      }\r\n      return this.$mergeStyle(style, this.custom_style);\r\n    },\r\n    offset() {\r\n      return this.$toPx(this.position === 'top' ? this.top : this.bottom);\r\n    },\r\n    otherHeight() {\r\n      return parseInt(this.position === 'top' ? this.windowTop : this.windowBottom);\r\n    },\r\n    refreshState() {\r\n      return [\r\n        this.fixed,\r\n        this.position,\r\n        this.top,\r\n        this.bottom,\r\n        this.safeAreaInset,\r\n      ];\r\n    },\r\n  },\r\n  watch: {\r\n    refreshState() {\r\n      this.refresh();\r\n    },\r\n  },\r\n  created() {\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.windowTop = systemInfo.windowTop || 0;\r\n    this.windowBottom = systemInfo.windowBottom || 0;\r\n  },\r\n  mounted() {\r\n    this.refresh();\r\n  },\r\n  methods: {\r\n    refresh() {\r\n      this.$nextTick(() => {\r\n        if (!this._isMounted || this._isDestroyed || !this.fixed) {\r\n          this.isFixed = false;\r\n        } else {\r\n          this.$getRect(`#${this.$sUid}`).then(rect => {\r\n            this.isFixed = true;\r\n            this.width = rect.width;\r\n            this.height = rect.height;\r\n          });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.ut-fixed {\r\n  z-index: 99;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-fixed.vue?vue&type=style&index=0&id=9e6eae8c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-fixed.vue?vue&type=style&index=0&id=9e6eae8c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671636\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}