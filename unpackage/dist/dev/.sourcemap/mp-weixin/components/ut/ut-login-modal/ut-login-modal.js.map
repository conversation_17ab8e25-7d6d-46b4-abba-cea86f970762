{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-login-modal/ut-login-modal.vue?fe1a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-login-modal/ut-login-modal.vue?4f79", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-login-modal/ut-login-modal.vue?0418", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-login-modal/ut-login-modal.vue?56fa", "uni-app:///components/ut/ut-login-modal/ut-login-modal.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-login-modal/ut-login-modal.vue?d210", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-login-modal/ut-login-modal.vue?c1d8"], "names": ["name", "components", "data", "userInfo", "hasUserInfo", "canIUseGetUserProfile", "noClick", "screenShot", "props", "value", "type", "default", "modalType", "colors", "computed", "loginTip", "comm<PERSON>ey", "websocket", "showLogin", "get", "set", "onLoad", "mounted", "methods", "hideModal", "onLogin", "token", "$platform", "wechat", "uni", "url", "mpCodeToApiToken", "code", "nickname", "headimgurl", "title", "setTimeout", "id", "phone", "headimg", "gender", "wxL<PERSON>in", "getuserinfo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0sB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoC9tB;AAKA;AACA;AAAA;AAAA;AAAA,eACA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;IACA;EACA;EACAI,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;MACA;IACA;EAAA,EACA;EACAC,2BAGA;EACAC;IAEA;MACA;QACAjB;MACA;IACA;EAEA;EACAkB,yCACA;IAEA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBACAA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAC;MAAA;MAAA;MACA;QACAf;QACAgB;QACAC;QACAC;MACA;MAEAL;QACAM;MACA;MACA;QACA;QACAC;QACA;UACA;UACA;UACA;YACA;cACAC;cACAJ;cACAK;cACAC;cACAC;YACA;YACA;UACA;UACAJ;YACAP;YACAA;YACAA;cACAM;YACA;UACA;QAEA;MACA;IACA;IACA;IACAM;MAAA;MACAb;QACA;MACA;IACA;IACA;IACAc;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAd;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAAA;AAIA;AAAA,2B;;;;;;;;;;;;;AC3KA;AAAA;AAAA;AAAA;AAAi0C,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACAr1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-login-modal/ut-login-modal.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-login-modal.vue?vue&type=template&id=756d0cda&\"\nvar renderjs\nimport script from \"./ut-login-modal.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-login-modal.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-login-modal.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-login-modal/ut-login-modal.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-login-modal.vue?vue&type=template&id=756d0cda&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-login-modal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-login-modal.vue?vue&type=script&lang=js&\"", "<template>\r\n\r\n\t<view class=\"cu-modal\" v-if=\"showLogin && !screenShot\" :class=\"[{ show: showLogin }, modalType]\" cathctouchmove\r\n\t\t@tap=\"hideModal\">\r\n\t\t<view class=\"cu-dialog\" @tap.stop style=\"background: none;overflow: visible;\">\r\n\t\t\t<view class=\"modal-box\">\r\n\t\t\t\t<image class=\"head-bg\" src=\"https://oss.afjy.net/api/file/preview?file=eWtn09p.png&width=750\" mode=\"\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<view class=\"detail\">\r\n\t\t\t\t\t<view class=\"title1\">您还没有登录</view>\r\n\t\t\t\t\t<view class=\"title2\">登录即刻开启品质生活</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-box y-f\">\r\n\t\t\t\t\t<!-- #ifndef MP-WEIXIN  -->\r\n\t\t\t\t\t<button class=\"cu-btn login-btn\" @tap=\"$shaken(onLogin)\">立即登录</button>\r\n\t\t\t\t\t<!-- #endif  -->\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN  -->\r\n\t\t\t\t\t<button class=\"cu-btn login-btn\" v-if=\"canIUseGetUserProfile\" @click=\"$shaken(wxLogin)\"> 授权并登录\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<button class=\"cu-btn login-btn\" v-else open-type=\"getUserInfo\" @getuserinfo=\"getUserInfo\"> 授权并登录\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<!-- <button class=\"cu-btn login-btn\" open-type=\"getUserInfo\" @getuserinfo=\"xcxWxLogin\">授权并登录</button> -->\r\n\t\t\t\t\t<!-- #endif  -->\r\n\t\t\t\t\t<button class=\"cu-btn close-btn\" @tap=\"hideModal\">取消</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\r\n\r\n\r\n</template>\r\n\r\n<script>\r\n\t// import Wechat from '@/common/wechat/wechat';\r\n\timport {\r\n\t\tmapMutations,\r\n\t\tmapActions,\r\n\t\tmapState\r\n\t} from 'vuex';\r\n\timport wechat from '@/common/wechat/wechat';\r\n\timport $platform from \"@/common/platform\";\r\n\texport default {\r\n\t\tname: 'ut-login-modal',\r\n\t\tcomponents: {},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\thasUserInfo: false,\r\n\t\t\t\tcanIUseGetUserProfile: false,\r\n\t\t\t\tnoClick: true,\r\n\t\t\t\tscreenShot: uni.getStorageSync('screenShot')\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tmodalType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState({\r\n\t\t\t\tloginTip: state => state.user.loginTip,\r\n\t\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t\t\twebsocket: state => state.websocket.socketTask,\r\n\t\t\t}),\r\n\t\t\tshowLogin: {\r\n\t\t\t\tget() {\r\n\t\t\t\t\treturn this.loginTip;\r\n\t\t\t\t},\r\n\t\t\t\tset(val) {\r\n\t\t\t\t\tthis.$store.commit('LOGIN_TIP', val);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef MP\r\n\t\t\tif (wx.getUserProfile) {\r\n\t\t\t\tthis.setData({\r\n\t\t\t\t\tcanIUseGetUserProfile: true\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['setToken', 'setUser']),\r\n\r\n\t\t\t// 隐藏登录弹窗\r\n\t\t\thideModal() {\r\n\t\t\t\tthis.showLogin = false;\r\n\t\t\t},\r\n\t\t\t// 去登录\r\n\t\t\tasync onLogin() {\r\n\t\t\t\tthis.hideModal()\r\n\t\t\t\tlet token = ''\r\n\t\t\t\tif ($platform.get() === \"wxOfficialAccount\") {\r\n\t\t\t\t\tawait wechat.login();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.showLogin = false;\r\n\t\t\t\t\tuni.setStorageSync('lastPage', this.$Route);\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmpCodeToApiToken(code, userInfo) { //根据code换取自家系统token\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tcommKey: this.commKey,\r\n\t\t\t\t\tcode: code,\r\n\t\t\t\t\tnickname: userInfo.nickName,\r\n\t\t\t\t\theadimgurl: userInfo.avatarUrl\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '登录中...'\r\n\t\t\t\t})\r\n\t\t\t\tthis.$ut.api('comm/login/mp', params).then(res => {\r\n\t\t\t\t\tthis.setToken(res.data);\r\n\t\t\t\t\tsetTimeout(() => {}, 500)\r\n\t\t\t\t\tthis.$ut.api('comm/login/myinfo').then(res2 => {\r\n\t\t\t\t\t\tlet userInfo=res2.data\r\n\t\t\t\t\t\tthis.setUser(userInfo)\r\n\t\t\t\t\t\tthis.websocket.on('connected', function(res) {\r\n\t\t\t\t\t\t\tlet param = {\r\n\t\t\t\t\t\t\t\tid: userInfo.id,\r\n\t\t\t\t\t\t\t\tnickname: userInfo.nickname,\r\n\t\t\t\t\t\t\t\tphone: userInfo.phone,\r\n\t\t\t\t\t\t\t\theadimg: userInfo.headimgurl,\r\n\t\t\t\t\t\t\t\tgender: userInfo.gender,\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.websocket.invoke('setMyInfo', param)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\tuni.showTabBar()\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '登陆成功'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 500)\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//小程序登录方式1（优先）\r\n\t\t\twxLogin() {\r\n\t\t\t\twechat.login().then((res) => {\r\n\t\t\t\t\tthis.mpCodeToApiToken(res.code, res.userInfo)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 小程序登录方式2\r\n\t\t\tasync getuserinfo(e) {\r\n\t\t\t\tawait wechat.login().then((res) => {\r\n\t\t\t\t\tthis.mpCodeToApiToken(res.code, res.userInfo)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t// 登录提示\r\n\t.modal-box {\r\n\t\twidth: 610rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tposition: relative;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tpadding-bottom: 30rpx;\r\n\r\n\t\t.head-bg {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 210rpx;\r\n\t\t}\r\n\r\n\t\t.detail {\r\n\t\t\t.title1 {\r\n\t\t\t\tcolor: #46351b;\r\n\t\t\t\tfont-size: 35rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.title2 {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tpadding-top: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn-box {\r\n\t\t\tmargin-top: 80rpx;\r\n\r\n\t\t\t.login-btn {\r\n\t\t\t\twidth: 492rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tbackground: linear-gradient(90deg, var(--colors), var(--colors2));\r\n\t\t\t\tbox-shadow: 0px 7rpx 6rpx 0px var(--colors3);\r\n\t\t\t\tborder-radius: 35rpx;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: rgba(#fff, 0.9);\r\n\t\t\t}\r\n\r\n\t\t\t.close-btn {\r\n\t\t\t\twidth: 492rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tcolor: var(--colors);\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tbackground: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 小程序登录提醒\r\n\t/* #ifdef MP-WEIXIN */\r\n\t.force-login-wrap {\r\n\t\tposition: fixed;\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t\toverflow: hidden;\r\n\t\tz-index: 11111;\r\n\t\ttop: 0;\r\n\t\t// background: linear-gradient(180deg, var(--colors), var(--colors2));\r\n\t\tbackground: #efefef;\r\n\r\n\t\t.logo-bg {\r\n\t\t\twidth: 640rpx;\r\n\t\t\theight: 300rpx;\r\n\t\t}\r\n\r\n\t\t.force-login__content {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\r\n\t\t\t.user-avatar {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 160rpx;\r\n\t\t\t\theight: 160rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.user-name {\r\n\t\t\t\tfont-size: 35rpx;\r\n\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: var(--colors);\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.login-notice {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: var(--colors);\r\n\t\t\t\tline-height: 44rpx;\r\n\t\t\t\twidth: 400rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-bottom: 80rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.author-btn {\r\n\t\t\t\twidth: 630rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tbackground: linear-gradient(90deg, var(--colors), var(--colors2));\r\n\t\t\t\tbox-shadow: 0px 7rpx 6rpx 0px var(--colors3);\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: rgba(255, 255, 255, 1);\r\n\t\t\t}\r\n\r\n\t\t\t.close-btn {\r\n\t\t\t\twidth: 630rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tborder: 2rpx solid var(--colors);\r\n\t\t\t\tbackground: none;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-family: PingFang SC;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: var(--colors);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-login-modal.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-login-modal.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671355\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}