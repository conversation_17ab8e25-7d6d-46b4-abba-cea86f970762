{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-banner/ut-banner.vue?7eb5", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-banner/ut-banner.vue?19e1", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-banner/ut-banner.vue?bc49", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-banner/ut-banner.vue?024a", "uni-app:///components/ut/ut-banner/ut-banner.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-banner/ut-banner.vue?a850", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-banner/ut-banner.vue?463b"], "names": ["name", "components", "data", "swiper<PERSON><PERSON>rent", "webviewId", "props", "detail", "type", "default", "imageName", "showDots", "width", "height", "computed", "created", "methods", "swiper<PERSON><PERSON>e", "getIndex", "routerTo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAqsB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgBztB;;;;;;;;;;;;;;;;eACA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAA4zC,CAAgB,soCAAG,EAAC,C;;;;;;;;;;;ACAh1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-banner/ut-banner.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-banner.vue?vue&type=template&id=60259d70&\"\nvar renderjs\nimport script from \"./ut-banner.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-banner.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-banner.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-banner/ut-banner.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-banner.vue?vue&type=template&id=60259d70&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.detail\n    ? _vm.__map(_vm.detail, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = _vm.$tools.showImg(item[_vm.imageName], 750)\n        return {\n          $orig: $orig,\n          g0: g0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-banner.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-banner.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 轮播 -->\r\n\t<view class=\"banner-swiper-box\" v-if=\"detail\">\r\n\t\t<canvas canvas-id=\"colorThief\" class=\"hide-canvas\"></canvas>\r\n\t\t<swiper class=\"banner-carousel shopro-selector-rect\" circular @change=\"swiperChange\" :autoplay=\"true\" :style=\"{width:width+'rpx',height:height+'rpx'}\">\r\n\t\t\t<swiper-item v-for=\"(item, index) in detail\" :key=\"index\" class=\"carousel-item \" @tap=\"routerTo(item.path)\">\r\n\t\t\t\t<image class=\"swiper-image \" :src=\"$tools.showImg(item[imageName],750)\" mode=\"aspectFill\"  lazy-load></image>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view class=\"banner-swiper-dots\" v-if=\"showDots\">\r\n\t\t\t<text :class=\"swiperCurrent === index ? 'banner-dot-active' : 'banner-dot'\" v-for=\"(dot, index) in detail.length\" :key=\"index\"></text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport colorThief from 'miniapp-color-thief';\r\nexport default {\r\n\tname:'ut-banner',\r\n\tcomponents: {},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tswiperCurrent: 0, //轮播下标\r\n\t\t\twebviewId: 0,\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\tdetail: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: null\r\n\t\t},\r\n\t\timageName:{\r\n\t\t\ttype:String,\r\n\t\t\tdefault:'image'\r\n\t\t},\r\n\t\tshowDots:{\r\n\t\t\ttype:Boolean,\r\n\t\t\tdefault:true\r\n\t\t},\r\n\t\twidth:{\r\n\t\t\ttype:Number,\r\n\t\t\tdefault:750,\r\n\t\t},\r\n\t\theight:{\r\n\t\t\ttype:Number,\r\n\t\t\tdefault: 300,\r\n\t\t},\r\n\t},\r\n\tcomputed: {},\r\n\tcreated() {\r\n\t\tthis.getIndex(0)\r\n\t},\r\n\tmethods: {\r\n\t\t// 轮播切换\r\n\t\tswiperChange(e) {\r\n\t\t\tthis.swiperCurrent = e.detail.current;\r\n\t\t\tthis.getIndex(this.swiperCurrent)\r\n\t\t},\r\n\t\tgetIndex(index){\r\n\t\t\tthis.$emit('getIndex', index);\r\n\t\t},\r\n\t\trouterTo(path){\r\n\t\t\tif(path == ''){\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tthis.$tools.routerTo(path)\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.hide-canvas {\r\n\tposition: fixed !important;\r\n\ttop: -99999upx;\r\n\tleft: -99999upx;\r\n\tz-index: 1;\r\n}\r\n\r\n// 轮播\r\n.banner-swiper-box {\r\n}\r\n\r\n.banner-swiper-box,\r\n.banner-carousel {\r\n\tposition: relative;\r\n\r\n\t.carousel-item {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\t// padding: 0 28upx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.swiper-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\t// border-radius: 10upx;\r\n\t\t// background: #ccc;\r\n\t}\r\n}\r\n\r\n.banner-swiper-dots {\r\n\tdisplay: flex;\r\n\tposition: absolute;\r\n\tleft: 50%;\r\n\ttransform: translateX(-50%);\r\n\tbottom: 40rpx;\r\n\r\n\t.banner-dot {\r\n\t\twidth: 36rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.5);\r\n\t}\r\n\r\n\t.banner-dot-active {\r\n\t\twidth: 36rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground: #fff;\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-banner.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-banner.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360672680\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}