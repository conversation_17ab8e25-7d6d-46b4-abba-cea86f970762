{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?ede3", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?e32f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?1e6b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?62bd", "uni-app:///components/ut/ut-top/ut-top.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?9ef3", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?fe37", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?e90c", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-top/ut-top.vue?996c"], "names": ["name", "props", "bgColor", "type", "default", "full", "options", "multipleSlots", "styleIsolation", "addGlobalClass", "virtualHost", "data", "h", "statusHeight", "top", "wrapHeight", "query"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACa;AACyB;;;AAG3F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAksB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACSttB;AAAA;AAAA;AAAA;AAAA;EAEAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;AAAA,mEACA;EACAJ;AACA,qGAEA;EACAK;IAAA;EAAA;AACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AAAA,wFAGA;EAAA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA2/B,CAAgB,i4BAAG,EAAC,C;;;;;;;;;;;ACA/gC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAi1C,CAAgB,2pCAAG,EAAC,C;;;;;;;;;;;ACAr2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ut/ut-top/ut-top.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ut-top.vue?vue&type=template&id=744e615a&scoped=true&\"\nvar renderjs\nimport script from \"./ut-top.vue?vue&type=script&lang=js&\"\nexport * from \"./ut-top.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ut-top.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./ut-top.vue?vue&type=style&index=1&id=744e615a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"744e615a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ut/ut-top/ut-top.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=template&id=744e615a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ut-top-wrap\" :style=\"{paddingTop:statusHeight+'px',height:wrapHeight+'px','--status':-statusHeight+'px'}\">\r\n\t\t<view class=\"ut-top-nav\" :style=\"{paddingTop: top+'px',backgroundColor:bgColor}\">\r\n\t\t\t<slot name=\"default\"></slot>\r\n\t\t</view>\r\n\t</view>\n</template>\n\n<script>\r\n\timport {mapState} from 'vuex'\r\n\texport default {\r\n\t\tname:'ut-top',\r\n\t\tprops:{\r\n\t\t\tbgColor:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'none',\r\n\t\t\t},\r\n\t\t\tfull:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false,\r\n\t\t\t}\r\n\t\t},\r\n\t\toptions: {\r\n\t\t\t// 微信小程序中 options 选项\r\n\t\t\tmultipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用\r\n\t\t\tstyleIsolation: \"isolated\",  //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式\r\n\t\t\taddGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared\r\n\t\t\tvirtualHost: true,  //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\th:0,\r\n\t\t\t}\r\n\t\t},\r\n\t\toptions: {\r\n\t\t\tstyleIsolation: 'shared'\r\n\t\t},\r\n\t\tcomputed:{\r\n\t\t\t...mapState({\r\n\t\t\t\tstatusHeight: state => state.init.statusHeight,\r\n\t\t\t}),\r\n\t\t\ttop(){\r\n\t\t\t\tif(this.full) return 0\r\n\t\t\t\treturn  this.statusHeight\r\n\t\t\t},\r\n\t\t\twrapHeight(){\r\n\t\t\t\tif(this.full) return 0\r\n\t\t\t\treturn this.h\r\n\t\t\t}\r\n\t\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tconst query=uni.createSelectorQuery().in(this)\r\n\t\t\tquery.select('.ut-top-nav').boundingClientRect(data=>{\r\n\t\t\t\tthis.h=data.height\r\n\t\t\t\tthis.$emit('topHeight',this.h,this.statusHeight)\r\n\t\t\t}).exec()\r\n\t\t},\r\n\t\r\n\t}\n</script>\n<style>\r\n\t.ut-top-nav .f-navbar{\r\n\t\tmargin-top: var(--status);\r\n\t}\r\n\t\r\n</style>\n<style lang=\"scss\" scoped>\r\n\t.ut-top-wrap{\r\n\t\t// z-index: 1;\r\n\t\t// position: fixed;\r\n\t\t// top: 0; /* css变量 */\r\n\t\t// left: 0;\r\n\t\twidth: 100%;\r\n\t}\r\n\t.ut-top-nav{\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft:0;\r\n\t\tz-index: 100;\r\n\t\twidth: 100%;\t\r\n\t}\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360663044\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=style&index=1&id=744e615a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-top.vue?vue&type=style&index=1&id=744e615a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360666862\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}