{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shop-select/shop-select.vue?2b07", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shop-select/shop-select.vue?6173", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shop-select/shop-select.vue?e7f9", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shop-select/shop-select.vue?ea32", "uni-app:///pagesC/components/shop-select/shop-select.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shop-select/shop-select.vue?ba91", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shop-select/shop-select.vue?103f"], "names": ["components", "stepper", "data", "colors", "showTime", "selectNum", "timeValue", "dateValue", "contentObj", "img", "price", "hospitalList", "id", "title", "times", "time", "onLoad", "onReady", "onShow", "created", "methods", "selectHospitalItem", "temp", "item", "selectTimeItem", "formatter", "selectTime", "closeDatePicker", "dateSure", "btnSure", "changeNum"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iWAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAusB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4D3tB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC,eACA;UACAC;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,GACA;UACAD;UACAC;QACA,EACA;QACAC,QACA;UACAF;UACAG;QACA,GACA;UACAH;UACAG;QACA;MAEA;IACA;EACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACA;IACA;EACA;EACAC;IACA;MACAf;IACA;EACA;EACAgB,6BACA;EACAC;IACAC;MACA;QACA;MACA;QACA;QACA;QACA;UAAA;QAAA;QACA;UACAC;QACA;QACAA;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;QACA;QACA;UAAA;QAAA;QACA;UACAF;QACA;QACAA;QACAC;MACA;MACA;IACA;IACAE;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvQA;AAAA;AAAA;AAAA;AAAs1C,CAAgB,gqCAAG,EAAC,C;;;;;;;;;;;ACA12C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/components/shop-select/shop-select.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./shop-select.vue?vue&type=template&id=9174d460&scoped=true&\"\nvar renderjs\nimport script from \"./shop-select.vue?vue&type=script&lang=js&\"\nexport * from \"./shop-select.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shop-select.vue?vue&type=style&index=0&id=9174d460&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9174d460\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/components/shop-select/shop-select.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop-select.vue?vue&type=template&id=9174d460&scoped=true&\"", "var components\ntry {\n  components = {\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/components/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop-select.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop-select.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"shop-list\">\r\n\t\t<view class=\"img-price\">\r\n\t\t\t<image :src=\"contentObj.img\" mode=\"aspectFill\"></image>\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t<text style=\"font-size: 24rpx;\">￥</text>123<text style=\"font-size: 28rpx;\">.00</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"select-hos-time\">选择：医院 时间</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view>\r\n\t\t\t<view style=\"margin: 30rpx 0;\">医院</view>\r\n\t\t\t<view class=\"hos-flex\">\r\n\t\t\t\t<template v-for=\"(item,index) in contentObj.hospitalList\">\r\n\t\t\t\t\t<view :key=\"index\" :class=\"item.active?'hospital-title-active':'hospital-title'\" @click=\"selectHospitalItem(item)\">{{ item.title }}</view>\r\n\t\t\t\t</template>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view>\r\n\t\t\t<view style=\"margin: 10rpx 0 30rpx;\">时间</view>\r\n\t\t\t<view class=\"hos-flex\" style=\"padding-bottom: 40rpx;\">\r\n\t\t\t\t<template v-for=\"(item,index) in contentObj.times\">\r\n\t\t\t\t\t<view :key=\"index\" :class=\"item.active?'hospital-title-active':'hospital-title'\" @click=\"selectTimeItem(item)\">{{ item.time }}</view>\r\n\t\t\t\t</template>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"flex-between\" style=\"padding-bottom: 30rpx;\">\r\n\t\t\t<view>购买数量</view>\r\n\t\t\t<view>\r\n\t\t\t\t<!-- <stepper size=\"small\" :min=\"1\" :max=\"allowMax\" :defaultValue=\"selectNum\"\r\n\t\t\t\t\t:display=\"canBuy\" @change=\"changeNum\"></stepper> -->\r\n\t\t\t\t<stepper size=\"small\" @change=\"changeNum\" :defaultValue=\"selectNum\"></stepper>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"flex-between\">\r\n\t\t\t<view>预约日期</view>\r\n\t\t\t<view style=\"color: #999;font-size: 24rpx;\" @click=\"selectTime\">{{ dateValue || '请选择日期'}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"fixed-btn\">\r\n\t\t\t<view class=\"sure\" @click=\"btnSure\">确定</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 日期选择 -->\r\n\t\t <u-datetime-picker\r\n\t\t\t:show=\"showTime\"\r\n\t\t\tv-model=\"timeValue\"\r\n\t\t\t:closeOnClickOverlay=\"true\"\r\n\t\t\tref=\"datetimePicker\"\r\n\t\t\tmode=\"date\"\r\n\t\t\t:formatter=\"formatter\"\r\n\t\t\t@close=\"closeDatePicker\"\r\n\t\t\t@confirm=\"dateSure\"\r\n\t\t\t@cancel=\"closeDatePicker\"\r\n\t\t></u-datetime-picker>\r\n\t</view>\n</template>\n\n<script>\r\n\tlet app = getApp();\r\n\timport stepper from './stepper.vue';\r\n\t\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tstepper\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tcolors: '',\r\n\t\t\t\tshowTime: false,\r\n\t\t\t\tselectNum: 1, //选中数量\r\n\t\t\t\ttimeValue: '',\r\n\t\t\t\tdateValue: '',//日期\r\n\t\t\t\t// itemObj: {\r\n\t\t\t\t// \thospitalName: '',\r\n\t\t\t\t// \ttime: '',\r\n\t\t\t\t// \tdate: '',\r\n\t\t\t\t// \tnum: ''\r\n\t\t\t\t// },\r\n\t\t\t\tcontentObj: {\r\n\t\t\t\t\timg: 'https://oss.afjy.net/api/file/preview?file=7BERovb',\r\n\t\t\t\t\tprice: '188',\r\n\t\t\t\t\thospitalList: [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '01',\r\n\t\t\t\t\t\t\ttitle: '云南省第一人民医院(昆华医院)'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '01',\r\n\t\t\t\t\t\t\ttitle: '云南省第一人民医院(昆华医院)',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '01',\r\n\t\t\t\t\t\t\ttitle: '云南省第一人民医院(昆华医院)',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '01',\r\n\t\t\t\t\t\t\ttitle: '云南省第一人民医院(昆华医院)',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '01',\r\n\t\t\t\t\t\t\ttitle: '云南省第一人民医院(昆华医院)',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttitle: '昆明医学院',\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\ttimes: [\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '01',\r\n\t\t\t\t\t\t\ttime: '上午 8:00~12:00'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tid: '02',\r\n\t\t\t\t\t\t\ttime: '下午 13:00~18:00'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: async function(options) {\r\n\t\t\tawait this.$onLaunched;\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// 微信小程序需要用此写法\r\n\t\t\tthis.$refs.datetimePicker.setFormatter(this.formatter)\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tthis.setData({\r\n\t\t\t\tcolors: app.globalData.newColor\r\n\t\t\t});\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselectHospitalItem(item){\r\n\t\t\t\tif(item.active){\r\n\t\t\t\t\tthis.$set(item,'active',false)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$set(item,'active',false)\r\n\t\t\t\t\tlet temp = {}\r\n\t\t\t\t\tlet obj = this.contentObj.hospitalList.find(v => v.active)\r\n\t\t\t\t\tif(obj != undefined){\r\n\t\t\t\t\t\ttemp = obj\r\n\t\t\t\t\t}\r\n\t\t\t\t\ttemp.active = false\r\n\t\t\t\t\titem.active = true\r\n\t\t\t\t}\r\n\t\t\t\t// this.itemObj.hospitalName = item.title\r\n\t\t\t},\r\n\t\t\tselectTimeItem(item){\r\n\t\t\t\tif(item.active){\r\n\t\t\t\t\tthis.$set(item,'active',false)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.$set(item,'active',false)\r\n\t\t\t\t\tlet temp = {}\r\n\t\t\t\t\tlet obj = this.contentObj.times.find(v => v.active)\r\n\t\t\t\t\tif(obj != undefined){\r\n\t\t\t\t\t\ttemp = obj\r\n\t\t\t\t\t}\r\n\t\t\t\t\ttemp.active = false\r\n\t\t\t\t\titem.active = true\r\n\t\t\t\t}\r\n\t\t\t\t// this.itemObj.time = item.time\r\n\t\t\t},\r\n\t\t\tformatter(type, value) {\r\n\t\t\t\tif (type === 'year') {\r\n\t\t\t\t\treturn `${value}年`\r\n\t\t\t\t}\r\n\t\t\t\tif (type === 'month') {\r\n\t\t\t\t\treturn `${value}月`\r\n\t\t\t\t}\r\n\t\t\t\tif (type === 'day') {\r\n\t\t\t\t\treturn `${value}日`\r\n\t\t\t\t}\r\n\t\t\t\treturn value\r\n\t\t\t},\r\n\t\t\tselectTime(){\r\n\t\t\t\tthis.showTime = true\r\n\t\t\t},\r\n\t\t\tcloseDatePicker(){\r\n\t\t\t\tthis.showTime = false\r\n\t\t\t},\r\n\t\t\tdateSure(e){\r\n\t\t\t\tconst date = new Date(e.value)\r\n\t\t\t\tconst year = date.getFullYear()\r\n\t\t\t\tconst month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)\r\n\t\t\t\tconst day = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())\r\n\t\t\t\tthis.dateValue = `${year}年${month}月${day}日`\r\n\t\t\t\t// this.itemObj.date = this.dateValue\r\n\t\t\t\tthis.showTime = false\r\n\t\t\t},\r\n\t\t\tbtnSure(){\r\n\t\t\t\tthis.$emit('submit')\r\n\t\t\t\t// console.log(this.itemObj);\r\n\t\t\t},\r\n\t\t\tchangeNum(val) {\r\n\t\t\t\tthis.selectNum = parseInt(val);\r\n\t\t\t\t// this.itemObj.num = this.selectNum\r\n\t\t\t},\r\n\t\t}\r\n\t}\n</script>\n\n<style scoped lang=\"scss\">\r\n\t.shop-list {\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\tpadding: 40rpx 40rpx 100rpx;\r\n\t}\r\n\t.img-price {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\timage {\r\n\t\t\theight: 100px;\r\n\t\t\twidth: 100px;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t}\r\n\t\t.price {\r\n\t\t\tcolor: red;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-bottom: 4rpx;\r\n\t\t}\r\n\t\t.select-hos-time {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.hos-flex {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\t.hospital-title {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tbackground: #F2F2F2;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tpadding: 10rpx 30rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tborder: 1px solid transparent;\r\n\t\t}\r\n\t\t.hospital-title-active {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tbackground: #F2F2F2;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tpadding: 10rpx 30rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tborder: 1px solid #EB040E;\r\n\t\t\tcolor: #EB040E;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.flex-between {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t.fixed-btn {\r\n\t\twidth: 100%;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 10;\r\n\t\tpadding: 10rpx 24rpx;\r\n\t\tbackground: #fff;\r\n\t\t.sure {\r\n\t\t\tbackground: #EB040E;\r\n\t\t\tcolor: #fff;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tletter-spacing: 2rpx;\r\n\t\t}\r\n\t}\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop-select.vue?vue&type=style&index=0&id=9174d460&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shop-select.vue?vue&type=style&index=0&id=9174d460&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671396\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}