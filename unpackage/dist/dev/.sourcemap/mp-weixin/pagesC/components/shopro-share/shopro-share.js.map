{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shopro-share/shopro-share.vue?f880", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shopro-share/shopro-share.vue?7a0f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shopro-share/shopro-share.vue?196b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shopro-share/shopro-share.vue?ad96", "uni-app:///pagesC/components/shopro-share/shopro-share.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shopro-share/shopro-share.vue?6246", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/shopro-share/shopro-share.vue?a7e1"], "names": ["name", "components", "data", "showShareGuide", "showPoster", "platform", "posterImage", "canvasParams", "props", "posterType", "type", "default", "posterInfo", "value", "computed", "comm<PERSON>ey", "showShare", "get", "set", "val", "created", "uni", "methods", "onClose<PERSON><PERSON>er", "onSuccess", "on<PERSON><PERSON>er", "that", "title", "desc", "path", "imgurl", "shareFriend", "saveImage", "authState", "filePath", "success", "fail", "console", "copySharePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundImage", "drawArray", "text", "isBgCenter", "size", "dy", "color", "textAlign", "textBaseLine", "url", "alpha", "dWidth", "dHeight", "circleSet", "dx", "textBaseline", "roundRectSet", "r", "fontWeight", "lineFeed", "max<PERSON><PERSON><PERSON>", "lineHeight", "lineNum", "fontFamily", "lineThrough", "style"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAAwsB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6D5tB;AACA;AAAA;AAAA;AAAA,gBACA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;EACA;EACAC,wDACA,2EACA;IACAC;MAAA;IAAA;EACA;IACAC;MACAC;QACA;MACA;MACAC;QACA;UACAC;QACA;QACA;MACA;IACA;EAAA,EACA;EAEAC;IAAA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAF;IACA;IACA;IACAG;MACA;IACA;IACA;IACAC;MACA;MAEAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACA;MACAT;MACA;QACA;QACA;MACA;QACA;MAAA;MAEA;IACA;IACA;IACAU;MACA;IAuBA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAN;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA,iCACA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAO;gBACA;kBACAZ;oBACAa;oBACAC;sBACA;sBACA;oBACA;oBACAC;sBACAC;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACAjB;QACAnB;QACAiC;UACAT;UACAA;QACA;MACA;IACA;IACA;IACAa;MACA;MACA;MACA;QACA;UACArC;YACAsC;YACAC,YACA;cACAzC;cACAU;cACAgC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA,GACA;cACAhD;cACAU;cACAuC;cACAC;cACAP;cACAE;cACAM;cACAC;cACAC;YACA,GAEA;cACArD;cACAU;cACAuC;cACAC;cACAL;cACAF;cACAQ;cACAC;YACA;UAaA;UACA;QACA;UACAlD;YACAsC;YACAC,YACA;cACAzC;cACAU;cACAuC;cACAC;cACAL;cACAS;cACAH;cACAC;cACAC;YACA,GACA;cACA3C;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAD;cACAT;YACA,GACA;cACAnC;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAD;cACAT;YACA,GACA;cACA7C;cACAU;cACAuC;cACAC;cACAP;cACAE;cACAM;cACAC;cACAI;gBACAC;cACA;YACA,GACA;cACA/C;cAAA;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAG;cACAC;gBACAC;gBACAC;gBACAC;cACA;cACAR;cACAT;YACA,GACA;cACAnC;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAG;cACAK;cACAT;cACAT;YACA,GACA;cACAnC;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAS;gBACAC;cACA;cACAX;cACAT;YACA,GAEA;cACA7C;cACAU;cAAA;cACAuC;cACAC;cACAI;cACAT;cACAM;cACAC;YACA;UAYA;UACA;QACA;UACAlD;YACAsC;YACAC,YACA;cACAzC;cACAU;cACAuC;cACAC;cACAL;cACAS;cACAH;cACAC;cACAC;YACA,GACA;cACA3C;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAD;cACAT;YACA,GACA;cACAnC;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAD;cACAT;YACA,GACA;cACA7C;cACAU;cACAuC;cACAC;cACAP;cACAE;cACAM;cACAC;cACAI;gBACAC;cACA;YACA,GACA;cACA/C;cAAA;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAG;cACAC;gBACAC;gBACAC;gBACAC;cACA;cACAR;cACAT;YACA,GACA;cACAnC;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAG;cACAK;cACAT;cACAT;YACA,GACA;cACAnC;cACAgC;cACAE;cACAE;cACAI;cACAH;cACAQ;cACAD;cACAT;YACA,GAEA;cACA7C;cACAU;cAAA;cACAuC;cACAC;cACAI;cACAT;cACAM;cACAC;YACA;UAYA;UACA;QACA;UACAf;UACA;MAAA;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjfA;AAAA;AAAA;AAAA;AAA+zC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAn1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/components/shopro-share/shopro-share.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./shopro-share.vue?vue&type=template&id=007d7ff0&\"\nvar renderjs\nimport script from \"./shopro-share.vue?vue&type=script&lang=js&\"\nexport * from \"./shopro-share.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shopro-share.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/components/shopro-share/shopro-share.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopro-share.vue?vue&type=template&id=007d7ff0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = [\"wxOfficialAccount\", \"H5\"].includes(_vm.platform)\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPoster = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showShare = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showShare = false\n    }\n    _vm.e3 = function ($event) {\n      _vm.showShareGuide = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopro-share.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopro-share.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<!-- 海报弹窗 -->\r\n\t\t<view class=\"cu-modal\" :class=\"{ show: showPoster }\" @tap=\"onClosePoster\">\r\n\t\t\t<view class=\"cu-dialog\" style=\"width: 640rpx;background: none;\">\r\n\t\t\t\t<view class=\"poster-img-box\"><image class=\"poster-img\" :src=\"posterImage\" mode=\"widthFix\"></image></view>\r\n\t\t\t\t<view class=\"poster-btn-box u-m-t-20 u-flex u-row-between u-col-center\" v-show=\"posterImage\">\r\n\t\t\t\t\t<button class=\"cancel-btn u-reset-button\" @tap=\"showPoster = false\">取消</button>\r\n\t\t\t\t\t<button class=\"save-btn u-reset-button\" @tap=\"saveImage\">{{ ['wxOfficialAccount', 'H5'].includes(platform) ? '长按图片保存' : '保存图片' }}</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 分享tools -->\r\n\t\t<view class=\"cu-modal bottom-modal\" :class=\"{ show: showShare }\" @tap=\"showShare = false\">\r\n\t\t\t<view class=\"cu-dialog safe-area-inset-bottom\" style=\"border-radius: 20rpx 20rpx 0 0;background: none;\">\r\n\t\t\t\t<view class=\"share-box\">\r\n\t\t\t\t\t<view class=\"share-list-box ut-flex\">\r\n\t\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t\t<button class=\"share-item share-btn ut-flex-col ut-col-center\" open-type=\"share\">\r\n\t\t\t\t\t\t\t<image class=\"share-img\" src=\"http://file.shopro.top/imgs/share/share_wx.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<text class=\"share-title\">微信好友</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifndef MP-WEIXIN  -->\r\n\t\t\t\t\t\t<view v-if=\"platform !== 'H5'\" class=\"share-item ut-flex-col ut-col-center\" @tap=\"shareFriend\">\r\n\t\t\t\t\t\t\t<image class=\"share-img\" src=\"http://file.shopro.top/imgs/share/share_wx.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<text class=\"share-title\">微信好友</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<view class=\"share-item ut-flex-col ut-col-center\" @tap=\"onPoster\">\r\n\t\t\t\t\t\t\t<image class=\"share-img\" src=\"http://file.shopro.top/imgs/share/share_poster.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<text class=\"share-title\">生成海报</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"share-item ut-flex-col ut-col-center\" @tap=\"copySharePath\">\r\n\t\t\t\t\t\t\t<image class=\"share-img\" src=\"http://file.shopro.top/imgs/share/share_link.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<text class=\"share-title\">复制链接</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"share-foot ut-flex ut-row-center ut-col-center\" @tap=\"showShare = false\">取消</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 分享指引 -->\r\n\t\t<view class=\"cu-modal bottom-modal\" :class=\"{ show: showShareGuide }\" @tap=\"showShareGuide = false\">\r\n\t\t\t<view class=\"cu-dialog safe-area-inset-bottom\" style=\"border-radius: 20rpx 20rpx 0 0;background: none;vertical-align: top;\">\r\n\t\t\t\t<view class=\"guide-wrap u-flex u-col-top u-row-center\"><image class=\"guide-img\" src=\"http://file.shopro.top/imgs/share/share_guide.png\" mode=\"\"></image></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 各海报模块 -->\r\n\t\t<shopro-canvas v-if=\"showPoster\" ref=\"shoproCanvas\" :canvasParams=\"canvasParams\" @success=\"onSuccess\"></shopro-canvas>\r\n\t</view>\r\n</template>\r\n<script>\r\n/**\r\n * 分享弹窗\r\n * @property {Boolean} value = showModal - v-model控制显隐\r\n * @property {String} posterType - 海报类别\r\n * @property {Object} posterInfo - 海报数据\r\n */\r\nimport { mapMutations, mapActions, mapState, mapGetters } from 'vuex';\r\nimport Auth from '@/common/permission/index.js';\r\nexport default {\r\n\tname: 'shoproShare',\r\n\tcomponents: {},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tshowShareGuide: false, //H5的指引。\r\n\t\t\tshowPoster: false, //海报弹窗\r\n\t\t\tplatform: this.$platform.get(),\r\n\t\t\tposterImage: '',\r\n\t\t\tcanvasParams: {}\r\n\t\t};\r\n\t},\r\n\tprops: {\r\n\t\tposterType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tposterInfo: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: () => {}\r\n\t\t},\r\n\t\tvalue: {}\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapGetters(['initShare', 'userInfo', 'isLogin', 'shareInfo']),\r\n\t\t...mapState({\r\n\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t}),\r\n\t\tshowShare: {\r\n\t\t\tget() {\r\n\t\t\t\treturn this.value;\r\n\t\t\t},\r\n\t\t\tset(val) {\r\n\t\t\t\tif (!this.showPoster) {\r\n\t\t\t\t\tval ? uni.hideTabBar() : uni.showTabBar();\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('input', val);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\r\n\tcreated() {\r\n\t\tuni.$on('ON_WX_SHARE', () => {\r\n\t\t\tthis.showShare = false;\r\n\t\t});\r\n\t},\r\n\tmethods: {\r\n\t\t// 关闭弹窗\r\n\t\tonClosePoster() {\r\n\t\t\tthis.showPoster = false;\r\n\t\t\tuni.showTabBar();\r\n\t\t},\r\n\t\t// 绘制成功\r\n\t\tonSuccess(e) {\r\n\t\t\tthis.posterImage = e;\r\n\t\t},\r\n\t\t// 开始绘制\r\n\t\tonPoster() {\r\n\t\t\tlet that=this\r\n\r\n\t\t\tthat.$wxsdk.share({\r\n\t\t\t\ttitle:'我的分享',\r\n\t\t\t\tdesc:'我的描述',\r\n\t\t\t\tpath:'http://test.afjy.net',\r\n\t\t\t\timgurl:'http://oss.ynyzmx.com/api/file/preview?file=31OT1FQ&width=150&quality=70',\r\n\t\t\t})\r\n\r\n\t\t\treturn\r\n\t\t\tthis.posterImage = '';\r\n\t\t\tuni.hideTabBar();\r\n\t\t\tif (this.$store.getters.isLogin) {\r\n\t\t\t\tthis.canvasParams = this.getPosterFormatter();\r\n\t\t\t\tthis.showPoster = true;\r\n\t\t\t} else {\r\n\t\t\t\t// this.$store.dispatch('showAuthModal', 'accountLogin');\r\n\t\t\t}\r\n\t\t\tthis.showShare = false;\r\n\t\t},\r\n\t\t// 分享好友\r\n\t\tshareFriend() {\r\n\t\t\tlet that = this;\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tuni.share({\r\n\t\t\t\tprovider: 'weixin',\r\n\t\t\t\tscene: 'WXSceneSession',\r\n\t\t\t\ttype: 0,\r\n\t\t\t\thref: that.shareInfo.path,\r\n\t\t\t\ttitle: that.shareInfo.title,\r\n\t\t\t\tsummary: that.shareInfo.title,\r\n\t\t\t\timage: that.shareInfo.image,\r\n\t\t\t\tsuccess: res => {\r\n\t\t\t\t\tconsole.log('success:' + JSON.stringify(res));\r\n\t\t\t\t\tthis.showShare = false;\r\n\t\t\t\t},\r\n\t\t\t\tfail: err => {\r\n\t\t\t\t\tconsole.log('fail:' + JSON.stringify(err));\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.showShare = false;\r\n\t\t\tthis.showShareGuide = true;\r\n\t\t\t// #endif\r\n\t\t},\r\n\r\n\t\t// 保存图片\r\n\t\tasync saveImage() {\r\n\t\t\tlet that = this;\r\n\t\t\tif (['wxOfficialAccount', 'H5'].includes(this.platform)) {\r\n\t\t\t\tthis.$u.toast('长按图片保存');\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tlet authState = await new Auth('writePhotosAlbum').check();\r\n\t\t\tif (authState) {\r\n\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\tfilePath: that.posterImage,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.$u.toast('保存成功');\r\n\t\t\t\t\t\tthis.showPoster = false;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: err => {\r\n\t\t\t\t\t\tconsole.log(`图片保存失败:`, err);\r\n\t\t\t\t\t\tthis.$u.toast('保存失败');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 复制链接\r\n\t\tcopySharePath() {\r\n\t\t\tlet that = this;\r\n\t\t\tuni.setClipboardData({\r\n\t\t\t\tdata: that.shareInfo.copyLink,\r\n\t\t\t\tsuccess: data => {\r\n\t\t\t\t\tthat.$u.toast('已复制到剪切板');\r\n\t\t\t\t\tthat.showShare = false;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t// 获取海报格式,规则说明在@/shopro/poster/tools.js中的initDrawArray\r\n\t\tgetPosterFormatter() {\r\n\t\t\tconst that = this;\r\n\t\t\tlet data = {};\r\n\t\t\tswitch (this.posterType) {\r\n\t\t\t\tcase 'user':\r\n\t\t\t\t\tdata = {\r\n\t\t\t\t\t\tbackgroundImage: that.initShare.user_poster_bg,\r\n\t\t\t\t\t\tdrawArray: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: '用户昵称',\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: that.userInfo.nickname,\r\n\t\t\t\t\t\t\t\tisBgCenter: true,\r\n\t\t\t\t\t\t\t\tsize: 28,\r\n\t\t\t\t\t\t\t\tdy: 250,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\ttextAlign: 'middle',\r\n\t\t\t\t\t\t\t\ttextBaseLine: 'middle'\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'avatar',\r\n\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\turl: that.userInfo.avatar,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tisBgCenter: true,\r\n\t\t\t\t\t\t\t\tdy: 95,\r\n\t\t\t\t\t\t\t\tdWidth: 120,\r\n\t\t\t\t\t\t\t\tdHeight: 120,\r\n\t\t\t\t\t\t\t\tcircleSet: {}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'wxCode',\r\n\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\turl: `${that.$API_URL}wechat/wxacode?scene=${that.shareInfo.query}`,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tdy: 560,\r\n\t\t\t\t\t\t\t\tisBgCenter: true,\r\n\t\t\t\t\t\t\t\tdWidth: 180,\r\n\t\t\t\t\t\t\t\tdHeight: 180\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef  MP-WEIXIN\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: '普通二维码',\r\n\t\t\t\t\t\t\t\ttype: 'qrcode',\r\n\t\t\t\t\t\t\t\ttext: that.shareInfo.path,\r\n\t\t\t\t\t\t\t\tsize: 180,\r\n\t\t\t\t\t\t\t\tdy: 560,\r\n\t\t\t\t\t\t\t\tisBgCenter: true\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'goods':\r\n\t\t\t\t\tdata = {\r\n\t\t\t\t\t\tbackgroundImage: that.initShare.goods_poster_bg,\r\n\t\t\t\t\t\tdrawArray: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'avatar',\r\n\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\turl: that.userInfo.avatar,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tdy: 40,\r\n\t\t\t\t\t\t\t\tdx: 38,\r\n\t\t\t\t\t\t\t\tdWidth: 80,\r\n\t\t\t\t\t\t\t\tdHeight: 80,\r\n\t\t\t\t\t\t\t\tcircleSet: {}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: that.userInfo.nickname,\r\n\t\t\t\t\t\t\t\tsize: 28,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'middle',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'bottom',\r\n\t\t\t\t\t\t\t\tdx: 140,\r\n\t\t\t\t\t\t\t\tdy: 40\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: '推荐一个好物给你,请查收！',\r\n\t\t\t\t\t\t\t\tsize: 26,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'middle',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\tdx: 140,\r\n\t\t\t\t\t\t\t\tdy: 80\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'goodsImage',\r\n\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\turl: that.posterInfo.image,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tisBgCenter: true,\r\n\t\t\t\t\t\t\t\tdy: 140,\r\n\t\t\t\t\t\t\t\tdWidth: 620,\r\n\t\t\t\t\t\t\t\tdHeight: 620,\r\n\t\t\t\t\t\t\t\troundRectSet: {\r\n\t\t\t\t\t\t\t\t\tr: 20\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text', //标题\r\n\t\t\t\t\t\t\t\ttext: that.posterInfo.title,\r\n\t\t\t\t\t\t\t\tsize: 28,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\t\t\t\tlineFeed: {\r\n\t\t\t\t\t\t\t\t\tmaxWidth: 620,\r\n\t\t\t\t\t\t\t\t\tlineHeight: 40,\r\n\t\t\t\t\t\t\t\t\tlineNum: 2\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tdx: 36,\r\n\t\t\t\t\t\t\t\tdy: 780\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: `￥${that.posterInfo.price}`,\r\n\t\t\t\t\t\t\t\tsize: 38,\r\n\t\t\t\t\t\t\t\tcolor: '#E1212B',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\t\t\t\tfontFamily: 'OPPOSANS',\r\n\t\t\t\t\t\t\t\tdx: 30,\r\n\t\t\t\t\t\t\t\tdy: 860\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: `￥${that.posterInfo.original_price}`,\r\n\t\t\t\t\t\t\t\tsize: 28,\r\n\t\t\t\t\t\t\t\tcolor: '#999999',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'top',\r\n\t\t\t\t\t\t\t\tlineThrough: {\r\n\t\t\t\t\t\t\t\t\tstyle: '#999999'\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tdx: 400,\r\n\t\t\t\t\t\t\t\tdy: 860\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'wxCode',\r\n\t\t\t\t\t\t\t\ttype: 'image', //微信小程序码\r\n\t\t\t\t\t\t\t\turl: `${that.$API_URL}wechat/wxacode?scene=${that.shareInfo.query}`,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tdx: 522,\r\n\t\t\t\t\t\t\t\tdy: 911,\r\n\t\t\t\t\t\t\t\tdWidth: 110,\r\n\t\t\t\t\t\t\t\tdHeight: 110\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'qrcode',\r\n\t\t\t\t\t\t\t\ttext: that.shareInfo.copyLink,\r\n\t\t\t\t\t\t\t\tsize: 110,\r\n\t\t\t\t\t\t\t\tdx: 530,\r\n\t\t\t\t\t\t\t\tdy: 930\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 'groupon':\r\n\t\t\t\t\tdata = {\r\n\t\t\t\t\t\tbackgroundImage: that.initShare.groupon_poster_bg,\r\n\t\t\t\t\t\tdrawArray: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'avatar',\r\n\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\turl: that.userInfo.avatar,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tdy: 40,\r\n\t\t\t\t\t\t\t\tdx: 38,\r\n\t\t\t\t\t\t\t\tdWidth: 80,\r\n\t\t\t\t\t\t\t\tdHeight: 80,\r\n\t\t\t\t\t\t\t\tcircleSet: {}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: that.userInfo.nickname,\r\n\t\t\t\t\t\t\t\tsize: 28,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'middle',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'bottom',\r\n\t\t\t\t\t\t\t\tdx: 140,\r\n\t\t\t\t\t\t\t\tdy: 40\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: '发现一个好物，快来和我一起拼吧！',\r\n\t\t\t\t\t\t\t\tsize: 26,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'middle',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\tdx: 140,\r\n\t\t\t\t\t\t\t\tdy: 80\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'goodsImage',\r\n\t\t\t\t\t\t\t\ttype: 'image',\r\n\t\t\t\t\t\t\t\turl: that.posterInfo.goods.image,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tisBgCenter: true,\r\n\t\t\t\t\t\t\t\tdy: 140,\r\n\t\t\t\t\t\t\t\tdWidth: 620,\r\n\t\t\t\t\t\t\t\tdHeight: 620,\r\n\t\t\t\t\t\t\t\troundRectSet: {\r\n\t\t\t\t\t\t\t\t\tr: 20\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text', //标题\r\n\t\t\t\t\t\t\t\ttext: that.posterInfo.goods.title,\r\n\t\t\t\t\t\t\t\tsize: 28,\r\n\t\t\t\t\t\t\t\tcolor: '#333',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\t\t\t\tlineFeed: {\r\n\t\t\t\t\t\t\t\t\tmaxWidth: 620,\r\n\t\t\t\t\t\t\t\t\tlineHeight: 40,\r\n\t\t\t\t\t\t\t\t\tlineNum: 2\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tdx: 36,\r\n\t\t\t\t\t\t\t\tdy: 780\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: `拼团价：￥${that.posterInfo.goods.groupon_price}`,\r\n\t\t\t\t\t\t\t\tsize: 32,\r\n\t\t\t\t\t\t\t\tcolor: '#E1212B',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'middle',\r\n\t\t\t\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\t\t\t\tfontFamily: 'OPPOSANS',\r\n\t\t\t\t\t\t\t\tdx: 30,\r\n\t\t\t\t\t\t\t\tdy: 860\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'text',\r\n\t\t\t\t\t\t\t\ttext: `${that.posterInfo.num}人团`,\r\n\t\t\t\t\t\t\t\tsize: 24,\r\n\t\t\t\t\t\t\t\tcolor: '#fff',\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\ttextAlign: 'left',\r\n\t\t\t\t\t\t\t\ttextBaseline: 'top',\r\n\t\t\t\t\t\t\t\tdx: 565,\r\n\t\t\t\t\t\t\t\tdy: 863\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tname: 'wxCode',\r\n\t\t\t\t\t\t\t\ttype: 'image', //微信小程序码\r\n\t\t\t\t\t\t\t\turl: `${that.$API_URL}wechat/wxacode?scene=${that.shareInfo.query}`,\r\n\t\t\t\t\t\t\t\talpha: 1,\r\n\t\t\t\t\t\t\t\tdx: 530,\r\n\t\t\t\t\t\t\t\tdy: 930,\r\n\t\t\t\t\t\t\t\tdWidth: 110,\r\n\t\t\t\t\t\t\t\tdHeight: 110\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttype: 'qrcode',\r\n\t\t\t\t\t\t\t\ttext: that.shareInfo.path,\r\n\t\t\t\t\t\t\t\tsize: 110,\r\n\t\t\t\t\t\t\t\tdx: 530,\r\n\t\t\t\t\t\t\t\tdy: 930\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tconsole.log('%cerr:没有此类型海报数据', 'color:green;background:yellow');\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t\treturn data;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n// 指引\r\n.guide-wrap {\r\n\theight: 100%;\r\n\t.guide-img {\r\n\t\twidth: 580rpx;\r\n\t\theight: 430rpx;\r\n\t}\r\n}\r\n// 分享海报\r\n.poster-btn-box {\r\n\t.cancel-btn {\r\n\t\twidth: 300rpx;\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tbackground: #ffffff;\r\n\t\tborder-radius: 35rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #999999;\r\n\t}\r\n\t.save-btn {\r\n\t\twidth: 300rpx;\r\n\t\theight: 70rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tbackground: linear-gradient(90deg, #e9b461, #eecc89);\r\n\t\tborder-radius: 35rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n}\r\n.poster-img-box {\r\n\t.poster-img {\r\n\t\twidth: 660rpx;\r\n\t\tmin-height: 800rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n}\r\n// 分享tool\r\n.share-box {\r\n\tbackground: #fff;\r\n\twidth: 750rpx;\r\n\tborder-radius: 30rpx 30rpx 0 0;\r\n\tpadding-top: 30rpx;\r\n\tposition: relative;\r\n\r\n\t.share-foot {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #bfbfbf;\r\n\t\theight: 80rpx;\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.share-list-box {\r\n\t\t.share-btn {\r\n\t\t\tbackground: none;\r\n\t\t\tborder: none;\r\n\t\t\tline-height: 1;\r\n\t\t\tpadding: 0;\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.share-item {\r\n\t\t\tflex: 1;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\r\n\t\t\t.share-img {\r\n\t\t\t\twidth: 70rpx;\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tbackground: rgba(246, 246, 254, 1);\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.share-title {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopro-share.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shopro-share.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671446\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}