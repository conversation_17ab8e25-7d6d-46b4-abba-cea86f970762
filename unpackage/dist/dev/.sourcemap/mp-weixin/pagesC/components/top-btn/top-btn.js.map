{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/top-btn/top-btn.vue?f526", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/top-btn/top-btn.vue?eeb8", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/top-btn/top-btn.vue?ce1c", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/top-btn/top-btn.vue?bbdb", "uni-app:///pagesC/components/top-btn/top-btn.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/top-btn/top-btn.vue?ac9f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/components/top-btn/top-btn.vue?bf75"], "names": ["data", "navBarH", "colors", "topList", "id", "icon", "url", "list", "onLoad", "onShow", "created", "methods", "tabItem"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACgBvtB;AAAA,eAEA;EACAA;IACA;MACAC;MACAC;MACAC,UACA;QAAAC;QACAC;QACAC;MACA,GACA;QAAAF;QACAC;QACAC;MACA,GACA;QAAAF;QACAC;QACAC;MACA,GACA;QAAAF;QACAC;QACAC;MACA,EACA;MACAC;IACA;EACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACA;MACAP;IACA;EACA;EACAQ;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAAk1C,CAAgB,4pCAAG,EAAC,C;;;;;;;;;;;ACAt2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/components/top-btn/top-btn.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./top-btn.vue?vue&type=template&id=ba71199c&scoped=true&\"\nvar renderjs\nimport script from \"./top-btn.vue?vue&type=script&lang=js&\"\nexport * from \"./top-btn.vue?vue&type=script&lang=js&\"\nimport style0 from \"./top-btn.vue?vue&type=style&index=0&id=ba71199c&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ba71199c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/components/top-btn/top-btn.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./top-btn.vue?vue&type=template&id=ba71199c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./top-btn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./top-btn.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"top-list\" :style=\"{top: navBarH + 'px'}\">\r\n\t\t<view class=\"btn-item-first\" @click=\"tabItem(topList[0].url)\">\r\n\t\t\t<text class=\"iconfont\" :class=\"topList[0].icon\"></text>\r\n\t\t</view>\r\n\t\t<view class=\"flex\">\r\n\t\t\t<template v-for=\"(item,index) in list\">\r\n\t\t\t\t<view :key=\"index\" class=\"btn-item\" @click=\"tabItem(item.url)\">\r\n\t\t\t\t\t<text class=\"iconfont\" :class=\"item.icon\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t</view>\n</template>\n\n<script>\r\n\tlet app = getApp();\r\n\t\r\n\texport default {\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tnavBarH: app.globalData.statusHeight+app.globalData.toBar,\r\n\t\t\t\tcolors: '',\r\n\t\t\t\ttopList: [\r\n\t\t\t\t\t{\tid: '01',\r\n\t\t\t\t\t\ticon: 'icon-home',\r\n\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\tid: '02',\r\n\t\t\t\t\t\ticon: 'icon-code',\r\n\t\t\t\t\t\turl: '',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\tid: '03',\r\n\t\t\t\t\t\ticon: 'icon-gouwuche',\r\n\t\t\t\t\t\turl: '',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\tid: '04',\r\n\t\t\t\t\t\ticon: 'icon-home',\r\n\t\t\t\t\t\turl: '',\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tlist: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: async function(options) {\r\n\t\t\tawait this.$onLaunched;\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tthis.setData({\r\n\t\t\t\tcolors: app.globalData.newColor\r\n\t\t\t});\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tif(this.topList){\r\n\t\t\t\tthis.list = this.topList.slice(1,this.topList.length)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttabItem(url){\r\n\t\t\t\t// console.log(url);\r\n\t\t\t\tif(url == ''){\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.$tools.routerTo(url)\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</script>\n\n<style scoped lang=\"scss\">\r\n\t.top-list {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 20rpx;\r\n\t\twidth: 100%;\r\n\t\tposition: absolute;\r\n\t\t.btn-item {\r\n\t\t\tbackground: #534E4B;\r\n\t\t\theight: 50rpx;\r\n\t\t\twidth: 50rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tmargin-left: 20rpx;\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t\t&-first {\r\n\t\t\t\tbackground: #534E4B;\r\n\t\t\t\topacity: 0.7;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\twidth: 50rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 50rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./top-btn.vue?vue&type=style&index=0&id=ba71199c&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./top-btn.vue?vue&type=style&index=0&id=ba71199c&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360672372\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}