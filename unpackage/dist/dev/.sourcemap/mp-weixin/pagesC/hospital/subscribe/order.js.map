{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?e55b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?faf0", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?0e35", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?1044", "uni-app:///pagesC/hospital/subscribe/order.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?5931", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?9ae4", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?7e04", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/subscribe/order.vue?d367"], "names": ["components", "hospitalList", "userAgreement", "props", "colors", "type", "firstBase", "default", "projectInfo", "shopInfo", "shopList", "agreement", "price", "options", "styleIsolation", "data", "noClick", "showHospital", "checkboxValue", "hospitalInfo", "hospital", "time", "person", "showTime", "value1", "dateValue", "showUserAgreement", "isChecked", "form", "date", "people", "idcard", "phone", "remark", "address", "onReady", "computed", "<PERSON><PERSON><PERSON><PERSON>", "formatter", "closeDatePicker", "dateSure", "clickAgree", "clickContent", "createOrder", "uni", "title", "icon", "findSelected", "infoItemClick", "toPay", "console", "hospitalClose", "hospitalSelect", "getLocation", "success"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,2VAEN;AACP,KAAK;AACL;AACA,aAAa,uTAEN;AACP,KAAK;AACL;AACA,aAAa,iWAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAAisB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EC2JrtBA;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;MACAE;IACA;IACAC;MACAH;MACAE;IACA;IACAE;MACAJ;MACAE;IACA;IACAG;MACAL;MACAE;QAAA;MAAA;IACA;IACAI;MACAN;MACAE;IACA;IACAK;MACAP;MACAE;IACA;EACA;EACAM;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAP;QACAQ;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AAAA,mEACA;EACAvB;AACA,oEACA;EACAwB;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC,mCAEA;EACAC;IACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACA;IACA;MACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACA;IACA;EACA;EACAC;IACA;MAAA;IAAA;IACA;IACA;EACA;EACAC;IACA;IACA;MACA;IACA;MACA;QAAA;QACA;MACA,sHACA;IACA;EAEA;EACAC;IACAC;IACAN;MACAC;MACAC;IACA;IACA;EACA;EACAK;IACA;EACA;EACAC,8CACA;IACA;IACA;IACA;EACA;EACAC;IAAA;IACAT;MACAU;QACA;QACAJ;MACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7UA;AAAA;AAAA;AAAA;AAA0/B,CAAgB,g4BAAG,EAAC,C;;;;;;;;;;;ACA9gC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAg1C,CAAgB,0pCAAG,EAAC,C;;;;;;;;;;;ACAp2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/subscribe/order.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=5540ef34&scoped=true&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./order.vue?vue&type=style&index=1&id=5540ef34&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5540ef34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/subscribe/order.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=template&id=5540ef34&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Image\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--image/u--image\" */ \"@/components/uview-ui/components/u--image/u--image.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--textarea/u--textarea\" */ \"@/components/uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"@/components/uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/components/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uDatetimePicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-datetime-picker/u-datetime-picker\" */ \"@/components/uview-ui/components/u-datetime-picker/u-datetime-picker.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.projectInfo.timesList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.findSelected(item)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showHospital = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showTime = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showUserAgreement = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"hospital-order\" :style=\"{'--colors-bg':colors+'30'}\">\r\n\t\t<view class=\"service-content\">\r\n\t\t\t<view class=\"service-content-icon\">\r\n\t\t\t\t<u--image v-if=\"projectInfo.imgHead\" :showLoading=\"true\" :src=\"projectInfo.imgHead\" width=\"44rpx\"\r\n\t\t\t\t\theight=\"44rpx\" radius=\"4rpx\"></u--image>\r\n\t\t\t\t<text v-else class=\"iconfont icon-vip\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"service-content-title\">{{projectInfo.name}}</view>\r\n\t\t\t<view class=\"service-content-info\" @click=\"clickContent\">\r\n\t\t\t\t<text class=\"iconfont icon-vip\"></text>\r\n\t\t\t\t服务内容\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"order-info\">\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t<view class=\"info-item-container\">\r\n\t\t\t\t\t<!-- <view class=\"info-item-title hospital\">就诊医院<text class=\"required\">*</text></view> -->\r\n\t\t\t\t\t<view v-if=\"!firstBase\">\r\n\t\t\t\t\t\t<text class=\"info-item-text info-selected\">{{shopInfo.shop?shopInfo.shop.name:''}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<view @click=\"showHospital=true\">\r\n\t\t\t\t\t\t\t<text v-if=\"hospitalInfo.id\" class=\"info-item-text info-selected\">{{hospitalInfo.name}}</text>\r\n\t\t\t\t\t\t\t<text v-else>请选择医院</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n<!-- \t\t\t\t\t<text :class=\"['info-item-content',hospital ? '' : 'no-content']\">\r\n\t\t\t\t\t\t{{ hospital ? hospital : '请选择就诊医院' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"iconfont icon-right\"></text> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t<view class=\"info-item-title\">\r\n\t\t\t\t\t<view>就诊时间<text class=\"required\">*</text></view>\r\n\t\t\t\t\t<view class=\"required-text\" v-if=\"isChecked && !form.time\">需要选择时间</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item-container info-time-box\">\r\n\t\t\t\t\t<template v-for=\"(item,index) in projectInfo.timesList\">\r\n\t\t\t\t\t\t<text class=\"info-item-text info-time\" :class=\"{'info-selected':findSelected(item)}\" :key=\"index\" @click=\"infoItemClick(item)\">{{item.title}}</text>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- <text class=\"info-item-text info-time\">下午13:00~18:00</text> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t<view class=\"info-item-title\">\r\n\t\t\t\t\t<view>就诊日期<text class=\"required\">*</text></view>\r\n\t\t\t\t\t<view class=\"required-text\"  v-if=\"isChecked && !form.date\">需要选择日期</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item-container\" @click=\"showTime=true\">\r\n\t\t\t\t\t <text v-if=\"!dateValue\" >选择日期</text>\r\n\t\t\t\t\t <text v-else class=\"info-date\">{{dateValue}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t<view class=\"info-item-title\">\r\n\t\t\t\t\t<view>姓名<text class=\"required\">*</text></view>\r\n\t\t\t\t\t<view class=\"required-text\"  v-if=\"isChecked && !form.people\">需要填写</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item-container\">\r\n\t\t\t\t\t<input class=\"item-cotent-input\" type=\"text\" placeholder=\"请填写就诊人姓名\"\r\n\t\t\t\t\t\tplaceholder-class=\"input-placeholder-class\" v-model=\"form.people\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t<view class=\"info-item-title\">\r\n\t\t\t\t\t<view>身份证号码<text class=\"required\">*</text></view>\r\n\t\t\t\t\t<view class=\"required-text\"  v-if=\"isChecked && !form.idcard\">需要填写</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item-container\">\r\n\t\t\t\t\t<input class=\"item-cotent-input\" type=\"idcard\" placeholder=\"请填写就诊人身份证号\"\r\n\t\t\t\t\t\tplaceholder-class=\"input-placeholder-class\" v-model=\"form.idcard\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t<view class=\"info-item-title\">\r\n\t\t\t\t\t<view>联系电话<text class=\"required\">*</text></view>\r\n\t\t\t\t\t<view class=\"required-text\" v-if=\"isChecked && !form.phone\">需要填写</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item-container\">\r\n\t\t\t\t\t<input class=\"item-cotent-input\" type=\"number\" placeholder=\"请填写联系电话\"\r\n\t\t\t\t\t\tplaceholder-class=\"input-placeholder-class\" v-model=\"form.phone\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"order-info-item\">\r\n\t\t\t\t<view class=\"info-item-title\">地址\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-item-container address\">\r\n\t\t\t\t\t<u--textarea v-model=\"form.address\" height=\"40\" placeholder=\"请填写或选择您所在地址\" maxlength=\"150\" autoHeight></u--textarea>\r\n<!-- \t\t\t\t\t<input class=\"item-cotent-input\" type=\"text\" placeholder=\"请填写或选择您所在地址\"\r\n\t\t\t\t\t\tplaceholder-class=\"input-placeholder-class\" v-model=\"form.address\" /> -->\r\n\t\t\t\t\t<view class=\"gps cuIcon-icon icon-address-1\" v-if=\"!form.address\" @click.stop=\"getLocation\">地图</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"tip-message\">带*号的请您务必填写</view>\r\n\t\t<view class=\"service-demand\">\r\n\t\t\t\r\n\t\t\t<view class=\"demand-content\">\r\n\t\t\t\t<view class=\"demand-title\">服务需求</view>\r\n\t\t\t\t<u--textarea v-model=\"form.remark\" height=\"40\" placeholder=\"请简单描述您的需求...\" confirmType=\"done\" maxlength=\"300\" count autoHeight></u--textarea>\r\n\t\t\t\t<!-- <textarea value=\"\" placeholder=\"请简单描述您的需求...\" placeholder-class=\"textarea-placeholder-class\" /> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"order-clause-sbmit\">\r\n\t\t\t<view class=\"service-clause\">\r\n\t\t\t\t<u-checkbox-group class=\"check-box\" :activeColor=\"colors\" v-model=\"checkboxValue\">\r\n\t\t\t\t\t<u-checkbox name=\"agree\" shape=\"square\" size=\"20\"  label=\"我已阅读并同意\"></u-checkbox>\r\n\t\t\t\t</u-checkbox-group>\r\n\t\t\t\t<text class=\"link\" @click=\"showUserAgreement=true\">《陪诊服务协议》</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"service-submit\">\r\n\t\t\t\t<view v-if=\"agreeClause && projectInfo.price\" class=\"btn btnok\" @click=\"$shaken(createOrder) \">\r\n\t\t\t\t\t确认下单\r\n\t\t\t\t\t<text v-if=\"projectInfo.price\">（支付{{projectInfo.price}}元）</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"btn submit-disable\">确认下单\r\n\t\t\t\t\t<text v-if=\"projectInfo.price\">（支付{{projectInfo.price}}元）</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<u-datetime-picker\r\n\t\t\tref=\"datetimePicker\"\r\n\t\t\t:show=\"showTime\"\r\n\t\t\tv-model=\"value1\"\r\n\t\t\t:closeOnClickOverlay=\"true\"\r\n\t\t\tmode=\"date\"\r\n\t\t\t:formatter=\"formatter\"\r\n\t\t\t@close=\"closeDatePicker\"\r\n\t\t\t@confirm=\"dateSure\"\r\n\t\t\t@cancel=\"closeDatePicker\"\r\n\t\t\t></u-datetime-picker>\r\n\t\t<user-agreement :show.sync=\"showUserAgreement\" :content=\"agreement\"></user-agreement>\r\n\t\t\r\n\t\t<u-popup class=\"\" :show=\"showHospital\" mode=\"bottom\" border-radius=\"34\" :closeable=\"true\" :safe-area-inset-bottom=\"true\"\r\n\t\t\t:mask-close-able=\"true\" height=\"800\" close-icon-pos=\"top-left\" @close=\"hospitalClose\">\r\n\t\t\t<view class=\"hospital-wrap\">\r\n\t\t\t\t<hospital-list :list=\"shopList\" :colors=\"colors\" @select=\"hospitalSelect\"></hospital-list>\r\n\t\t\t</view>\r\n\t\t\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport hospitalList from '../components/hospital-list.vue'\r\n\timport userAgreement from '../components/user-agreement.vue'\r\n\texport default {\r\n\t\tcomponents:{\r\n\t\t\thospitalList,\r\n\t\t\tuserAgreement,\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tcolors:{\r\n\t\t\t\ttype:String\r\n\t\t\t},\r\n\t\t\tfirstBase:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false,\r\n\t\t\t},\r\n\t\t\tprojectInfo: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tshopInfo: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tshopList: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => [],\r\n\t\t\t},\r\n\t\t\tagreement: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tprice: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 0.00\r\n\t\t\t}\r\n\t\t},\r\n\t\toptions: {\r\n\t\t\tstyleIsolation: 'shared'\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnoClick:true,\r\n\t\t\t\tshowHospital:false,\r\n\t\t\t\tcheckboxValue: [],\r\n\t\t\t\thospitalInfo:{},\r\n\t\t\t\thospital: '',\r\n\t\t\t\ttime: '',\r\n\t\t\t\tperson: '',\r\n\t\t\t\tshowTime: false,\r\n\t\t\t\tvalue1: Number(new Date()),\r\n\t\t\t\tdateValue:'',\r\n\t\t\t\tshowUserAgreement:false,\r\n\t\t\t\tisChecked:false,\r\n\t\t\t\tform:{\r\n\t\t\t\t\ttime:[],\r\n\t\t\t\t\tdate:'',\r\n\t\t\t\t\tpeople:'',\r\n\t\t\t\t\tidcard:'',\r\n\t\t\t\t\tphone:'',\r\n\t\t\t\t\tremark:'',\r\n\t\t\t\t\taddress:'',\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// 微信小程序需要用此写法\r\n\t\t\tthis.$refs.datetimePicker.setFormatter(this.formatter)\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tagreeClause() {\r\n\t\t\t\treturn this.checkboxValue.length > 0\r\n\t\t\t}\r\n\t\t},\r\n\t\toptions: {\r\n\t\t\tstyleIsolation: 'shared'\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tformatter(type, value) {\r\n\t\t\t\tif (type === 'year') {\r\n\t\t\t\t\treturn `${value}年`\r\n\t\t\t\t}\r\n\t\t\t\tif (type === 'month') {\r\n\t\t\t\t\treturn `${value}月`\r\n\t\t\t\t}\r\n\t\t\t\tif (type === 'day') {\r\n\t\t\t\t\treturn `${value}日`\r\n\t\t\t\t}\r\n\t\t\t\treturn value\r\n\t\t\t},\r\n\t\t\tcloseDatePicker(){\r\n\t\t\t\tthis.showTime = false\r\n\t\t\t},\r\n\t\t\tdateSure(e){\r\n\t\t\t\tconst date = new Date(e.value)\r\n\t\t\t\tconst year = date.getFullYear()\r\n\t\t\t\tconst month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)\r\n\t\t\t\tconst day = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())\r\n\t\t\t\tthis.dateValue = `${year}年${month}月${day}日`\r\n\t\t\t\tthis.form.date= `${year}-${month}-${day}`\r\n\t\t\t\t// this.itemObj.date = this.dateValue\r\n\t\t\t\tthis.showTime = false\r\n\t\t\t},\r\n\t\t\tclickAgree(){\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tclickContent() {\r\n\t\t\t\tif(!this.hospitalInfo.id){\r\n\t\t\t\t\tthis.showHospital=true\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('onShowContent')\r\n\t\t\t},\r\n\t\t\tcreateOrder(){\r\n\t\t\t\tthis.isChecked=true\r\n\t\t\t\tif(!this.form.time || !this.form.date || !this.form.people || !this.form.idcard || !this.form.phone){\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle:'请填写必填信息',\r\n\t\t\t\t\t\t\ticon:'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.isChecked = false\r\n\t\t\t\tthis.$emit('createOrder',this.form)\r\n\t\t\t\t// console.log(this.form)\r\n\t\t\t},\r\n\t\t\tfindSelected(item){\r\n\t\t\t\tlet obj = this.form.time.find(u=>u==item.title)\r\n\t\t\t\tif(obj) return true\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\tinfoItemClick(item){\r\n\t\t\t\tlet index = this.form.time.indexOf(item.title)\r\n\t\t\t\tif(index>=0){\r\n\t\t\t\t\tthis.form.time.splice(index)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(this.projectInfo.selTimesCount==1){//单选操作\r\n\t\t\t\t\t\tthis.form.time=[item.title]\r\n\t\t\t\t\t}else if(this.projectInfo.selTimesCount>0 && this.form.time.length>=this.projectInfo.selTimesCount) return\r\n\t\t\t\t\telse this.form.time.push(item.title)\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\ttoPay() {\r\n\t\t\t\tconsole.log('调用支付');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '支付完成，请选择陪诊',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\t// this.$emit('next')\r\n\t\t\t},\r\n\t\t\thospitalClose(){\r\n\t\t\t\tthis.showHospital=false\r\n\t\t\t},\r\n\t\t\thospitalSelect(item)\r\n\t\t\t{\r\n\t\t\t\tthis.hospitalInfo=item\r\n\t\t\t\tthis.showHospital=false\r\n\t\t\t\tthis.$emit('hospitalSelect',item)\r\n\t\t\t},\r\n\t\t\tgetLocation() {\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tsuccess:(res)=>{\r\n\t\t\t\t\t\tthis.form.address=res.address+res.name\r\n\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t},\r\n\t\t\t\t})\r\n\t\t\t\t// uni.showLoading({\r\n\t\t\t\t// \ttitle:'请稍等...'\r\n\t\t\t\t// })\r\n\t\t\t\t// this.$wxsdk.getFuzzyLocationToAddress().then(res => {\r\n\t\t\t\t\t\r\n\t\t\t\t// \t\tconsole.log(res.address)\r\n\t\t\t\t// \tthis.form.address=res.address\r\n\t\t\t\t// \tuni.hideLoading()\r\n\t\t\t\t// }).catch(err=>{\r\n\t\t\t\t// \tconsole.log(err)\r\n\t\t\t\t// \tuni.hideLoading()\r\n\t\t\t\t// })\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\t.u-checkbox-group{\r\n\t\tpadding: 5rpx 20rpx;\r\n\t}\r\n\t\r\n\t.u-checkbox-label--left{\r\n\t\tpadding: 20rpx 5rpx;\r\n\t}\r\n\t\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.hospital-wrap{\r\n\t\tpadding-top: 80rpx;\r\n\t}\r\n\t.info-date{\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tletter-spacing:4rpx;\r\n\t}\r\n\t.info-time{\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\t\r\n\t.required{\r\n\t\tcolor: #ff0000;\r\n\t}\r\n\t\r\n\t.info-time-box{\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.hospital-order {\r\n\t\toverflow-y: scroll;\r\n\t\tpadding-bottom: 260rpx;\r\n\r\n\t\t.service-content {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 38rpx 20rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t.order-info {\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t.service-demand {\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.order-clause-sbmit {\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tz-index: 2;\r\n\t\t\tborder-top: 1rpx solid rgba(0, 0, 0, 0.1);\r\n\t\t}\r\n\t}\r\n\r\n\t.service-content {\r\n\t\t.service-content-icon {\r\n\t\t\twidth: 44rpx;\r\n\t\t\theight: 44rpx;\r\n\t\t\tline-height: 44rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin: 10rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\toverflow: hidden;\r\n\t\t\tbackground-color: #f6d0ff;\r\n\r\n\t\t\t.iconfont {\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tcolor: #9860e9;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.service-content-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tletter-spacing: 2rpx;\r\n\t\t}\r\n\r\n\t\t.service-content-info {\r\n\t\t\tcolor: #353535;\r\n\t\t\tmargin-left: auto;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.iconfont {\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info-item-text{\r\n\t\tborder: 1rpx solid var(--colors2);\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding: 5rpx 16rpx;\r\n\t\tcolor: var(--colors);\r\n\t\tfont-size: 26rpx;\r\n\t\t\r\n\t\tmin-width: 190rpx;\r\n\t}\r\n\t\r\n\t.info-item-container .info-item-text{\r\n\t\tmargin: 10rpx;\r\n\t\tline-height: 1.5;\r\n\t\twidth: fit-content;\r\n\t\t&:first-child{\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\t\r\n\t\t}\r\n\t\t&:last-child{\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.info-selected{\r\n\t\t// background: #ffe4e6;\r\n\t\tbackground-color: var(--colors-bg);\r\n\t\t\r\n\t}\r\n\t\r\n\t.required-text{\r\n\t\tposition: absolute;\r\n\t\tline-height: 1;\r\n\t\tfont-size: 16rpx;\r\n\t\tcolor: #f00;\r\n\t}\r\n\r\n\t.order-info {\r\n\t\t.order-info-item {\r\n\t\t\theight: auto;\r\n\t\t\tmin-height: 106rpx;\r\n\t\t\tline-height: 106rpx;\r\n\t\t\tborder-bottom: 2rpx solid #eee;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\t.info-item-title {\r\n\t\t\t\twidth: 160rpx;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.info-item-title.hospital{\r\n\t\t\t\twidth:auto;\r\n\t\t\t}\r\n\r\n\t\t\t.info-item-container {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t// padding-top: 20rpx;\r\n\r\n\t\t\t\t/deep/ .input-placeholder-class {\r\n\t\t\t\t\tcolor: #c9c9c9;\r\n\t\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.item-cotent-input {\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\tpadding-right: 20rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.info-item-content {\r\n\t\t\t\t\tmax-width: calc(100% - 140rpx);\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\tpadding-right: 20rpx;\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.no-content {\r\n\t\t\t\t\tcolor: #c8c7cd;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tcolor: #c8c7cd;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.service-demand {\r\n\t\t.demand-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tletter-spacing: 2rpx;\r\n\t\t\tpadding: 10rpx 0;\r\n\t\t}\r\n\r\n\t\t.demand-content {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 20rpx 20rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\r\n\t\t\t// /deep/ .textarea-placeholder-class {\r\n\t\t\t// \tcolor: #c9c9c9;\r\n\t\t\t// \tletter-spacing: 2rpx;\r\n\t\t\t// }\r\n\r\n\t\t\t// /deep/ textarea,uni-textarea {\r\n\t\t\t// \twidth: 100%;\r\n\t\t\t// \theight: 100rpx;\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n\r\n\t.order-clause-sbmit {\r\n\t\t.service-clause {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tcolor: #323232;\r\n\t\t\t// margin-bottom: 28rpx;\r\n\t\t\t.check-box{\r\n\t\t\t\tpadding:24rpx 10rpx\r\n\t\t\t}\r\n\t\t\t.link {\r\n\t\t\t\tcolor: var(--colors)\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.service-submit {\r\n\t\t\t//padding-right: 40rpx;\r\n\t\t\theight: 90rpx;\r\n\t\t\t\r\n\t\t\t.btn{\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tline-height: 90rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.btnok{\r\n\t\t\t\tbackground-color: var(--colors);\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\r\n\t\t\t.submit-disable {\r\n\t\t\t\tbackground-color: #f7f7f7;\r\n\t\t\t\tcolor: #9d9d9d;\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t}\r\n\t\r\n\t.tip-message{\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #c8c7cd;\r\n\t}\r\n\t\r\n\t.address{\r\n\t\tdisplay: flex;\r\n\t\tline-height: 1;\r\n\t\talign-items: center;\r\n\t\t.gps{\r\n\t\t\tline-height: 1;\r\n\t\t\tcolor: var(--colors);\r\n\t\t\tborder:1rpx solid var(--colors);\r\n\t\t\tborder-radius: 6rpx;\r\n\t\t\tcolor: var(--colors);\r\n\t\t\tpadding: 8rpx 16rpx;\r\n\t\t\t\r\n\t\t}\r\n\t\t\r\n\t\t/deep/ .u-textarea{\r\n\t\t\tborder:none;\r\n\t\t\ttext-align: left;\r\n\t\t}\r\n\t\t.item-cotent-input{\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360665599\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=1&id=5540ef34&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=1&id=5540ef34&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360672066\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}