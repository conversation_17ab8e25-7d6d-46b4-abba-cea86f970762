{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-info.vue?f6bb", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-info.vue?f95b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-info.vue?a594", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-info.vue?1f2e", "uni-app:///pagesC/hospital/components/order-info.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-info.vue?f91e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-info.vue?d1a9"], "names": ["name", "data", "orderList", "type", "value", "remark", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAssB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0B1tB;EACAA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;QACAF;QACAC;QACAC;MACA;IACA;EACA;EACAC,UAEA;AACA;AAAA,2B;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAq1C,CAAgB,+pCAAG,EAAC,C;;;;;;;;;;;ACAz2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/order-info.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./order-info.vue?vue&type=template&id=298cd443&scoped=true&\"\nvar renderjs\nimport script from \"./order-info.vue?vue&type=script&lang=js&\"\nexport * from \"./order-info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order-info.vue?vue&type=style&index=0&id=298cd443&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"298cd443\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/order-info.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-info.vue?vue&type=template&id=298cd443&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-info.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 这是订单页面 -->\r\n\t<view>\r\n\t\t<view class=\"order-title\">\r\n\t\t\t订单信息\r\n\t\t</view>\r\n\t\t<view class=\"order-body\">\r\n\t\t\t<template v-for=\"(item,index) in orderList\">\r\n\t\t\t\t<view :key=\"index\" class=\"order-item\">\r\n\t\t\t\t\t<view class=\"item-left\">\r\n\t\t\t\t\t\t{{ item.type }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t\t<text>{{ item.value }}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.remark == '0'\" class=\"iconfont color1 icon-sex-woman\"><text\r\n\t\t\t\t\t\t\t\tstyle=\"margin-left: 8rpx;\">女士</text></text>\r\n\t\t\t\t\t\t<text v-if=\"item.remark == '1'\" class=\"iconfont color2 icon-sex-man\"><text\r\n\t\t\t\t\t\t\t\tstyle=\"margin-left: 8rpx;\">男士</text></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: 'Order',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderList: [{\r\n\t\t\t\t\ttype: '就诊医院',\r\n\t\t\t\t\tvalue: '昆明医科大学第一附属医院',\r\n\t\t\t\t\tremark: ''\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttype: '就诊日期',\r\n\t\t\t\t\tvalue: '2019-08-08  全天',\r\n\t\t\t\t\tremark: ''\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttype: '接送地址',\r\n\t\t\t\t\tvalue: '址址址址址址址址址址址址址址址',\r\n\t\t\t\t\tremark: ''\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttype: '陪诊人员',\r\n\t\t\t\t\tvalue: '朱一光',\r\n\t\t\t\t\tremark: '0'\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttype: '陪诊电话',\r\n\t\t\t\t\tvalue: '13459654569',\r\n\t\t\t\t\tremark: ''\r\n\t\t\t\t}, ]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.order-title {\r\n\t\tcolor: rgb(141, 141, 141);\r\n\t\tmargin: 20rpx 0 20rpx 10rpx;\r\n\t\tletter-spacing: 2rpx;\r\n\t}\r\n\r\n\t.order-body {\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 20rpx;\r\n\r\n\t\t.order-item {\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid rgb(240, 240, 240);\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\t&:first-child .item-right {\r\n\t\t\t\twidth: 45%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t-o-text-overflow: ellipsis;\r\n\t\t\t}\r\n\r\n\t\t\t.item-left {\r\n\t\t\t\tcolor: gray;\r\n\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item-right {\r\n\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-weight: bolder;\r\n\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\tbackground-color: rgb(238, 238, 238);\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\tpadding: 4rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.color1 {\r\n\t\t\t\t\tcolor: rgb(254, 58, 212);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.color2 {\r\n\t\t\t\t\tcolor: blue;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-info.vue?vue&type=style&index=0&id=298cd443&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-info.vue?vue&type=style&index=0&id=298cd443&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671119\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}