{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/user-agreement.vue?7f44", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/user-agreement.vue?ffc8", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/user-agreement.vue?a71c", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/user-agreement.vue?945e", "uni-app:///pagesC/hospital/components/user-agreement.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/user-agreement.vue?c761", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/user-agreement.vue?755b"], "names": ["props", "show", "type", "default", "content", "colors", "data", "serviceList", "icon", "service", "serviceObject", "name", "methods", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA0sB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCkB9tB;EACAA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACAF;QACAG;MACA,GACA;QACAH;QACAG;MACA;QACAH;QACAG;MACA;QACAH;QACAG;MACA;IAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAyzC,CAAgB,ooCAAG,EAAC,C;;;;;;;;;;;ACA70C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/user-agreement.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./user-agreement.vue?vue&type=template&id=acb34304&scoped=true&\"\nvar renderjs\nimport script from \"./user-agreement.vue?vue&type=script&lang=js&\"\nexport * from \"./user-agreement.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user-agreement.vue?vue&type=style&index=0&id=acb34304&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"acb34304\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/user-agreement.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-agreement.vue?vue&type=template&id=acb34304&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-agreement.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-agreement.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-popup class=\"\" \r\n\t\t:show=\"show\" mode=\"bottom\" round=\"10\" :closeable=\"true\" :safe-area-inset-bottom=\"true\"\r\n\t\t:mask-close-able=\"true\" height=\"500\" close-icon-pos=\"top-left\" @close=\"close\">\r\n\t\t<view class=\"hospital-service-content\">\r\n\t\t\t<view class=\"title\">陪诊服务协议</view>\r\n\t\t\t<view class=\"content-body\">\r\n\t\t\t\t<view class=\"body-content\">\r\n\t\t\t\t<span v-html=\"content.detailContent\"></span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<u-button :color=\"colors\" @click=\"close\">确认</u-button>\r\n\t\t</view>\r\n\r\n\t</u-popup>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: () => false\r\n\t\t\t},\r\n\t\t\tcontent:{\r\n\t\t\t\ttype:Object,\r\n\t\t\t\tdefault:()=>{},\r\n\t\t\t},\r\n\t\t\tcolors:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'#0bb584'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tserviceList: [{\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"排队缴费\", \"市内接送\", \"送取结果\"]\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"了解病史\", \"诊前提示\", \"转达医诉\"]\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"规划流程\", \"陪同检查\", \"就医协助\", \"传达医嘱\"]\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"异地就医\", \"预定食宿\", \"定期回访\", \"诊后关爱\"]\r\n\t\t\t\t}],\r\n\t\t\t\tserviceObject: [{\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"没时间陪亲人看病\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"不想浪费时间等待\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"病情不明担心存在误诊\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"流程繁杂外来语言不通\"\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$emit('update:show', !this.show)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.hospital-service-content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx;\r\n\r\n\r\n\t\t.title {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 0 0 30rpx 0;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t.content-body {\r\n\t\t\tmax-height: 800rpx;\r\n\t\t\toverflow-y: scroll;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.content-body {\r\n\t\t.body-content {\r\n\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\tletter-spacing: 4rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t}\r\n\r\n\t\t\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-agreement.vue?vue&type=style&index=0&id=acb34304&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./user-agreement.vue?vue&type=style&index=0&id=acb34304&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360663627\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}