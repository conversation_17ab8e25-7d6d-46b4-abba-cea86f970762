{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/content.vue?27e5", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/content.vue?675d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/content.vue?603b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/content.vue?8e4a", "uni-app:///pagesC/hospital/components/content.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/content.vue?b0fe", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/content.vue?da1b"], "names": ["props", "show", "type", "default", "projectInfo", "data", "serviceList", "icon", "service", "serviceObject", "name", "methods", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0CvtB;EACAA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;MACAC;QACAF;QACAG;MACA,GACA;QACAH;QACAG;MACA;QACAH;QACAG;MACA;QACAH;QACAG;MACA;IAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAkzC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACAt0C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/content.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./content.vue?vue&type=template&id=8d20c21a&scoped=true&\"\nvar renderjs\nimport script from \"./content.vue?vue&type=script&lang=js&\"\nexport * from \"./content.vue?vue&type=script&lang=js&\"\nimport style0 from \"./content.vue?vue&type=style&index=0&id=8d20c21a&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8d20c21a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/content.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./content.vue?vue&type=template&id=8d20c21a&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./content.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./content.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-popup class=\"\" :show=\"show\" mode=\"bottom\" border-radius=\"34\" :closeable=\"true\" :safe-area-inset-bottom=\"true\"\r\n\t\t:mask-close-able=\"true\" height=\"500\" close-icon-pos=\"top-left\" @close=\"close\">\r\n\t\t<view class=\"hospital-service-content\">\r\n\t\t\t<view class=\"title\">尊享VIP陪诊服务内容</view>\r\n\t\t\t<view class=\"content-body\">\r\n\t\t\t\t<view class=\"body-content\">{{projectInfo.remark}}</view>\r\n\t\t\t\t<view class=\"body-dec\" v-html=\"projectInfo.detailContent\"></view>\r\n\t\t\t\t<!-- <view class=\"service-list\">\r\n\t\t\t\t\t<template v-for=\"(service, serviceIndex) in serviceList\">\r\n\t\t\t\t\t\t<view :key=\"serviceIndex\" class=\"service-item\">\r\n\t\t\t\t\t\t\t<view class=\"service-icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"service.icon\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"service-item-content\">\r\n\t\t\t\t\t\t\t\t<template v-for=\"(item, index) in service.service\">\r\n\t\t\t\t\t\t\t\t\t<view :key=\"index\" class=\"item-content-name\">{{ item }}</view>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"service-object-list\">\r\n\t\t\t\t\t<view class=\"service-object-title\">服务对象</view>\r\n\t\t\t\t\t<template v-for=\"(item, index) in serviceObject\">\r\n\t\t\t\t\t\t<view class=\"service-object-item\">\r\n\t\t\t\t\t\t\t<view class=\"object-item-icon\">\r\n\t\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"item.icon\"></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"object-item-content\">{{ item.name }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t\t<u-button color=\"#0bb584\" @click=\"close\">我知道了，开始预约</u-button>\r\n\t\t</view>\r\n\r\n\t</u-popup>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tshow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: () => false\r\n\t\t\t},\r\n\t\t\tprojectInfo:{\r\n\t\t\t\ttype:Object,\r\n\t\t\t\tdefault:()=>{},\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tserviceList: [{\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"排队缴费\", \"市内接送\", \"送取结果\"]\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"了解病史\", \"诊前提示\", \"转达医诉\"]\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"规划流程\", \"陪同检查\", \"就医协助\", \"传达医嘱\"]\r\n\t\t\t\t}, {\r\n\t\t\t\t\ticon: 'icon-vip',\r\n\t\t\t\t\tservice: [\"异地就医\", \"预定食宿\", \"定期回访\", \"诊后关爱\"]\r\n\t\t\t\t}],\r\n\t\t\t\tserviceObject: [{\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"没时间陪亲人看病\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"不想浪费时间等待\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"病情不明担心存在误诊\"\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ticon: \"icon-vip\",\r\n\t\t\t\t\t\tname: \"流程繁杂外来语言不通\"\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$emit('update:show', !this.show)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n\t.hospital-service-content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx;\r\n\r\n\r\n\t\t.title {\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 0 0 30rpx 0;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t.content-body {\r\n\t\t\tmax-height: 800rpx;\r\n\t\t\toverflow-y: scroll;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.content-body {\r\n\t\t.body-content {\r\n\t\t\tbackground-color: #fef2e4;\r\n\t\t\tborder: 2rpx solid #fecb93;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tcolor: #fe7300;\r\n\t\t\tletter-spacing: 2rpx;\r\n\t\t\tline-height: 1.7;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t}\r\n\r\n\t\t.body-dec {\r\n\t\t\tpadding-top: 10rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tletter-spacing: 2rpx;\r\n\t\t\tline-height: 1.7;\r\n\t\t\t\t\r\n\t\t\timg{\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\r\n\t\t.service-list {\r\n\t\t\t.service-item {\r\n\t\t\t\tpadding: 42rpx 14rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tborder-bottom: 2rpx solid #eee;\r\n\r\n\t\t\t\t.service-icon {\r\n\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\tline-height: 100rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tbackground-color: #efeff9;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\t\t\tcolor: #5f8efd;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.service-item-content {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t\t.item-content-name {\r\n\t\t\t\t\t\tmargin: 10rpx 20rpx;\r\n\t\t\t\t\t\tcolor: #147ed9;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.service-object-list {\r\n\t\t\t.service-object-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tpadding: 26rpx 0;\r\n\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.service-object-item {\r\n\t\t\t\theight: 50px;\r\n\t\t\t\tbackground-color: #f3f6ff;\r\n\t\t\t\tcolor: #656c76;\r\n\t\t\t\tletter-spacing: 4rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tmargin-top: 22rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\r\n\t\t\t\t.object-item-icon {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 70rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\t\t\tcolor: #6392fc;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.object-item-content {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./content.vue?vue&type=style&index=0&id=8d20c21a&lang=less&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--10-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--10-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./content.vue?vue&type=style&index=0&id=8d20c21a&lang=less&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360663619\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}