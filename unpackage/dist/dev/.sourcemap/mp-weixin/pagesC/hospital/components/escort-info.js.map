{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/escort-info.vue?d062", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/escort-info.vue?d63e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/escort-info.vue?a3c0", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/escort-info.vue?18a6", "uni-app:///pagesC/hospital/components/escort-info.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/escort-info.vue?b72b", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/escort-info.vue?3008"], "names": ["props", "escortInfo", "type", "default", "name", "phone", "icon", "content", "data"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAusB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCqB3tB;EACAA;IACAC;MACAC;MACAC;QACA;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA,QAEA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAAs1C,CAAgB,gqCAAG,EAAC,C;;;;;;;;;;;ACA12C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/escort-info.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./escort-info.vue?vue&type=template&id=59fec0f9&scoped=true&\"\nvar renderjs\nimport script from \"./escort-info.vue?vue&type=script&lang=js&\"\nexport * from \"./escort-info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./escort-info.vue?vue&type=style&index=0&id=59fec0f9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"59fec0f9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/escort-info.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./escort-info.vue?vue&type=template&id=59fec0f9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./escort-info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./escort-info.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"escort-info-container\">\r\n\t\t<view class=\"avatar\">\r\n\t\t\t<image class=\"img\" src=\"../../../static/imgs/ut-logo.png\"></image>\r\n\t\t</view>\r\n\t\t<view class=\"escort-info\">\r\n\t\t\t<view class=\"person\">\r\n\t\t\t\t{{ escortInfo.name }}\r\n\t\t\t\t<text class=\"phone\">{{ escortInfo.phone }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t<view class=\"icon\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"escortInfo.icon\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content\">{{ escortInfo.content }}</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tescortInfo: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tname: '李天龙',\r\n\t\t\t\t\t\tphone: '17658855548',\r\n\t\t\t\t\t\ticon: require('../../../static/imgs/ut-logo.png'),\r\n\t\t\t\t\t\tcontent: '尊享VIP陪诊'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.escort-info-container {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 100rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t.img {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.escort-info {\r\n\t\t\tpadding-left: 20rpx;\r\n\r\n\t\t\t.person {\r\n\t\t\t\t.phone {\r\n\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.info {\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.icon {\r\n\t\t\t\t\twidth: 50rpx;\r\n\t\t\t\t\theight: 50rpx;\r\n\r\n\t\t\t\t\t.img {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.content {\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./escort-info.vue?vue&type=style&index=0&id=59fec0f9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./escort-info.vue?vue&type=style&index=0&id=59fec0f9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671007\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}