{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/acc-msg.vue?880a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/acc-msg.vue?f367", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/acc-msg.vue?265e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/acc-msg.vue?ff31", "uni-app:///pagesC/hospital/components/acc-msg.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/acc-msg.vue?953d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/acc-msg.vue?66a7"], "names": ["name", "options", "styleIsolation", "props", "colors", "type", "default", "detail", "city", "data", "info", "showCity", "loading", "areaList", "myPhotos", "fileList1", "idcardImg1", "idcardImg2", "bgColor", "sex", "cityId", "computed", "comm<PERSON>ey", "uploadInfo", "headers", "Token", "watch", "deep", "handler", "methods", "getSex", "goSelect", "getAreaList", "myArea", "<PERSON><PERSON><PERSON><PERSON>", "columnIndex", "e", "value", "values", "index", "picker", "confirm", "uploadPhoneSuccess", "uploadFileRes", "uploadIdCard1Success", "uploadIdCard2Success", "imgDelete", "console", "myPhotoAfterRead", "lists", "fileListLen", "item", "status", "message", "i", "result", "url", "uploadPhotoFilePromise", "resolve", "afterRead", "myPhotoPreview", "deletePic", "checkboxChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2VAEN;AACP,KAAK;AACL;AACA,aAAa,uTAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAmsB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqIvtB;AAEA;AAAA;AAAA,gBACA;EACAA;EACAC;IAAAC;EAAA;EAAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACAC;MACA;IACA;EAAA,EACA;EACAC;IACAnB;MACAoB;MACAC;QACA;MACA;IACA;IACA;MACAA;QACA;MACA;IACA;IACA;MACAA;QACA;MACA;IACA;IACA;MACAA;QACA;MACA;IACA;IACA;MACAA;QACA;MACA;IACA;IACAlB;MACAiB;MACAC;QACA;MACA;IACA;EAEA;EACAC;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAV;QACAF;MACA;QACA;MACA;IACA;IACAa;MACA;MACA;QAAA;MAAA;MACA;MACA;IACA;IACAC;MACA,IACAC,cAMAC,EANAD;QACAE,QAKAD,EALAC;QACAC,SAIAF,EAJAE;QACAC,QAGAH,EAHAG;QAAA,YAGAH,EADAI;QAAAA;MAEA;MACA;QACA;QACAA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACA;QACA;MACA;IAEA;IACAC;MAAA;MACAD;QACA;QACA;MACA;IAEA;IACAE;MAAA;MACAF;QACA;QACA;MAEA;IAEA;IACAG;MACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBACAD;kBACA,qDACAE;oBACAC;oBACAC;kBAAA,GACA;gBACA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAJ;gBACA;kBACAC;kBACAC;kBACAG;gBACA;gBACAN;cAAA;gBARAI;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAUA;IACAG;MAAA;MACA;QACA,0BACA;UACAhC;QACA,GACA+B,IACA;UACA,oBACAE;UACA;QAEA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAV;gBACAC;gBACAD;kBACA,qDACAE;oBACAC;oBACAC;kBAAA,GACA;gBACA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAJ;gBACA;kBACAC;kBACAC;kBACAG;gBACA;gBACAN;cAAA;gBARAI;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAUA;IACAM;MACAb;IACA;IACAc;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AClWA;AAAA;AAAA;AAAA;AAAk1C,CAAgB,4pCAAG,EAAC,C;;;;;;;;;;;ACAt2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/acc-msg.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./acc-msg.vue?vue&type=template&id=c658cea2&scoped=true&\"\nvar renderjs\nimport script from \"./acc-msg.vue?vue&type=script&lang=js&\"\nexport * from \"./acc-msg.vue?vue&type=script&lang=js&\"\nimport style0 from \"./acc-msg.vue?vue&type=style&index=0&id=c658cea2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c658cea2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/acc-msg.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./acc-msg.vue?vue&type=template&id=c658cea2&scoped=true&\"", "var components\ntry {\n  components = {\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"@/components/uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/components/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    utImageUpload: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-image-upload/ut-image-upload\" */ \"@/components/ut/ut-image-upload/ut-image-upload.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPicker: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-picker/u-picker\" */ \"@/components/uview-ui/components/u-picker/u-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.info.cityId\n    ? _vm.__map(_vm.areaList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.myArea(item)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showCity = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showCity = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./acc-msg.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./acc-msg.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"card-body\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">陪诊城市：</view>\r\n\t\t\t\t<view class=\"item-right\" @click=\"goSelect()\">{{info.cityId?info.cityName:'选择城市'}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\" v-if=\"info.cityId\">\r\n\t\t\t\t<view class=\"item-left\">陪诊区域：</view>\r\n\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t\t<u-checkbox-group v-model=\"info.areas\"  @change=\"checkboxChange\" placement=\"row\">\r\n\t\t\t\t\t\t\t<u-checkbox :customStyle=\"{marginLeft: '16rpx',marginTop: '20rpx'}\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in areaList\" :key=\"index\" :checked=\"myArea(item)\" :label=\"item.name\"\r\n\t\t\t\t\t\t\t\t:name=\"item.id\" :activeColor=\"colors\">\r\n\t\t\t\t\t\t\t</u-checkbox>\r\n\t\t\t\t\t\t</u-checkbox-group>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- \t\t\t\t<view v-if=\"info.areas.length<=0\" class=\"item-right\">\r\n\t\t\t\t\t<view>请选择区域</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else class=\"item-right\">\r\n\t\t\t\t\t<view v-for=\"(itm,idx) in info.areas\">{{itm.name}}</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">身份证号：</view>\r\n\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t<input v-model=\"info.identityCard\" placeholder-class=\"ipt\" type=\"idcard\" placeholder=\"请填写身份证号\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">手机号码：</view>\r\n\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t<input v-model=\"info.phone\" placeholder-class=\"ipt\" type=\"number\" placeholder=\"请填写手机号码\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">性别：</view>\r\n\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text class=\"sex\" :class=\"{'sex-true':info.sex == 1}\" @click=\"getSex(1)\">男</text>\r\n\t\t\t\t\t\t<text class=\"sex\" :class=\"{'sex-true':info.sex == 2}\" @click=\"getSex(2)\">女</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"card-body\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">展示称呼：</view>\r\n\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t<input v-model=\"info.stageName\" placeholder-class=\"ipt\" type=\"text\" placeholder=\"请填写展示昵称\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n<!-- \t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">形象图片：</view>\r\n\t\t\t\t<view class=\"item-right\" style=\"flex: 1;\">\r\n\t\t\t\t\t<ut-image-upload\r\n\t\t\t\t\t\tname=\"file\" \r\n\t\t\t\t\t\tv-model=\"myPhotos\" \r\n\t\t\t\t\t\tmediaType=\"image\"\r\n\t\t\t\t\t\t:colors=\"colors\"\r\n\t\t\t\t\t\t:max=\"1\"\r\n\t\t\t\t\t\t:headers=\"headers\" \r\n\t\t\t\t\t\t:action=\"uploadInfo.server+uploadInfo.single\" \r\n\t\t\t\t\t\t:preview-image-width=\"1200\"\r\n\t\t\t\t\t\t:width=\"150\"\r\n\t\t\t\t\t\t:height=\"150\"\r\n\t\t\t\t\t\t:border-radius=\"8\"\r\n\t\t\t\t\t\t@uploadSuccess=\"uploadPhoneSuccess\" \r\n\t\t\t\t\t\t@imgDelete=\"imgDelete\">\r\n\t\t\t\t\t</ut-image-upload>\r\n\t\t\t\t\t<u-upload multiple :maxCount=\"3\" accept=\"image\" capture=\"camera\" :previewImage=\"true\" name=\"file\"\r\n\t\t\t\t\t\tuploadText=\"添加照片\" width=\"60\" height=\"60\" :fileList=\"myPhotos\" @afterRead=\"myPhotoAfterRead\"\r\n\t\t\t\t\t\t@delete=\"deletePic\" @clickPreview=\"myPhotoPreview\"></u-upload>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"item-left\">资料图片：</view>\r\n\t\t\t\t<view class=\"item-right idcard-image\">\r\n\t\t\t\t\t<ut-image-upload\r\n\t\t\t\t\t\tname=\"file\"\r\n\t\t\t\t\t\tv-model=\"idcardImg1\" \r\n\t\t\t\t\t\tmediaType=\"image\"\r\n\t\t\t\t\t\t:colors=\"colors\"\r\n\t\t\t\t\t\t:max=\"1\"\r\n\t\t\t\t\t\t:headers=\"headers\" \r\n\t\t\t\t\t\t:action=\"uploadInfo.server+uploadInfo.single\" \r\n\t\t\t\t\t\t:preview-image-width=\"1200\"\r\n\t\t\t\t\t\t:width=\"160\"\r\n\t\t\t\t\t\t:height=\"120\"\r\n\t\t\t\t\t\t:border-radius=\"8\"\r\n\t\t\t\t\t\t@uploadSuccess=\"uploadIdCard1Success\" \r\n\t\t\t\t\t\t@imgDelete=\"imgDelete\">\r\n\t\t\t\t\t\t<template v-slot:default>\r\n\t\t\t\t\t\t\t<view class=\"camera\">\r\n\t\t\t\t\t\t\t\t<u-icon name=\"camera-fill\" color=\"#ccc\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t\t\t<text class=\"text\">正面</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</ut-image-upload>\r\n\t\t\t\t\t<ut-image-upload\r\n\t\t\t\t\t\tname=\"file\"\r\n\t\t\t\t\t\tv-model=\"idcardImg2\" \r\n\t\t\t\t\t\tmediaType=\"image\"\r\n\t\t\t\t\t\t:colors=\"colors\"\r\n\t\t\t\t\t\t:max=\"1\"\r\n\t\t\t\t\t\t:headers=\"headers\" \r\n\t\t\t\t\t\t:action=\"uploadInfo.server+uploadInfo.single\" \r\n\t\t\t\t\t\t:preview-image-width=\"1200\"\r\n\t\t\t\t\t\t:width=\"160\"\r\n\t\t\t\t\t\t:height=\"120\"\r\n\t\t\t\t\t\t:border-radius=\"8\"\r\n\t\t\t\t\t\t@uploadSuccess=\"uploadIdCard2Success\" \r\n\t\t\t\t\t\t@imgDelete=\"imgDelete\">\r\n\t\t\t\t\t\t<template v-slot:default>\r\n\t\t\t\t\t\t\t<view class=\"camera\">\r\n\t\t\t\t\t\t\t\t<u-icon name=\"camera-fill\" color=\"#ccc\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t\t\t<text class=\"text\">反面</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</ut-image-upload>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<!-- @confirm=\"confirm\" -->\r\n\t\t<u-picker ref=\"cityPicker\" immediateChange closeOnClickOverlay :confirmColor=\"colors\" v-model=\"info.cityId\" :show.sync=\"showCity\"\r\n\t\t\t:columns=\"[city]\" keyName=\"name\" @close=\"showCity = false\" @cancel=\"showCity = false\" @confirm=\"confirm\" />\r\n\t\t<!-- <u-picker :show=\"showCity\" ref=\"uPicker\" :columns=\"columns\" @cancel=\"confirm\" @change=\"changeHandler\"></u-picker> -->\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t} from 'vuex'\r\n\texport default {\r\n\t\tname: 'Accmsg',\r\n\t\toptions: { styleIsolation: 'shared' },   //自定义组件小程序css穿透\r\n\t\tprops: {\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\tdetail: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tcity: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => [],\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tinfo:{},\r\n\t\t\t\tshowCity: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tareaList: [],\r\n\t\t\t\tmyPhotos: [],\r\n\t\t\t\tfileList1: [],\r\n\t\t\t\tidcardImg1:[],\r\n\t\t\t\tidcardImg2:[],\r\n\t\t\t\tbgColor: '#FFF',\r\n\t\t\t\tsex: '',\r\n\t\t\t\tcityId:'',\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState({\r\n\t\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t\t\tuploadInfo: state => state.init.oss,\r\n\t\t\t}),\r\n\t\t\theaders(){\r\n\t\t\t\treturn {\r\n\t\t\t\t\tToken:this.uploadInfo.token\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tdetail:{\r\n\t\t\t\tdeep:true,\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.info=v\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'info.cityId': {\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tif (v) this.getAreaList(v)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'info.imgHead':{\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.myPhotos=[v]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'info.identityCardImg1':{\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.idcardImg1=[v]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t'info.identityCardImg2':{\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.idcardImg2=[v]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinfo:{\r\n\t\t\t\tdeep:true,\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.$emit('update:detail',v)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetSex(flag) {\r\n\t\t\t\tif(this.info.sex==flag){\r\n\t\t\t\t\tthis.info.sex=''\r\n\t\t\t\t}else this.$set(this.info,'sex',flag)\r\n\t\t\t},\r\n\t\t\tgoSelect() {\r\n\t\t\t\tthis.showCity = true\r\n\t\t\t},\r\n\t\t\tgetAreaList(cityId) {\r\n\t\t\t\tthis.$ut.api('hospital/city/area/list', {\r\n\t\t\t\t\tcommKey: this.commKey,\r\n\t\t\t\t\tcityId: cityId,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.areaList = res.data\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tmyArea(item) {\r\n\t\t\t\tif (!this.info.areas) return false\r\n\t\t\t\tconst obj = this.info.areas.find(u => u == item.id)\r\n\t\t\t\tif (obj) return true\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\tchangeHandler(e) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcolumnIndex,\r\n\t\t\t\t\tvalue,\r\n\t\t\t\t\tvalues, // values为当前变化列的数组内容\r\n\t\t\t\t\tindex,\r\n\t\t\t\t\t// 微信小程序无法将picker实例传出来，只能通过ref操作\r\n\t\t\t\t\tpicker = this.$refs.uPicker\r\n\t\t\t\t} = e\r\n\t\t\t\t// 当第一列值发生变化时，变化第二列(后一列)对应的选项\r\n\t\t\t\tif (columnIndex === 0) {\r\n\t\t\t\t\t// picker为选择器this实例，变化第二列对应的选项\r\n\t\t\t\t\tpicker.setColumnValues(1, this.columnData[index])\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 回调参数为包含columnIndex、value、values\r\n\t\t\tconfirm(e) {\r\n\t\t\t\tthis.showCity = false\r\n\t\t\t\tconst selCity = e.value[0]\r\n\t\t\t\tthis.$set(this.info,'cityId',selCity.id)\r\n\t\t\t\tthis.$set(this.info,'cityName',selCity.name)\r\n\t\t\t\tthis.getAreaList(selCity.id)\r\n\t\t\t\t// console.log('e', e);\r\n\t\t\t},\r\n\t\t\tuploadPhoneSuccess(uploadFileRes){\r\n\t\t\t\tuploadFileRes.forEach(r=>{\r\n\t\t\t\t\tlet item=r.data\r\n\t\t\t\t\tthis.$set(this.info,'imgHead',this.uploadInfo.preview  + '?file='+item.name+item.ext)\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tuploadIdCard1Success(uploadFileRes){\r\n\t\t\t\tuploadFileRes.forEach(r=>{\r\n\t\t\t\t\tlet item=r.data\r\n\t\t\t\t\tthis.$set(this.info,'identityCardImg1',this.uploadInfo.preview  + '?file='+item.name+item.ext)\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tuploadIdCard2Success(uploadFileRes){\r\n\t\t\t\tuploadFileRes.forEach(r=>{\r\n\t\t\t\t\tlet item=r.data\r\n\t\t\t\t\tthis.$set(this.info,'identityCardImg2',this.uploadInfo.preview  + '?file='+item.name+item.ext)\r\n\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\timgDelete(e){\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t},\r\n\t\t\tasync myPhotoAfterRead(event) {\r\n\t\t\t\tlet lists = [].concat(event.file)\r\n\t\t\t\tlet fileListLen = this.myPhotos.length\r\n\t\t\t\tlists.map((item) => {\r\n\t\t\t\t\tthis.myPhotos.push({\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tstatus: 'uploading',\r\n\t\t\t\t\t\tmessage: '上传中'\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\tfor (let i = 0; i < lists.length; i++) {\r\n\t\t\t\t\tconst result = await this.uploadPhotoFilePromise(lists[i].url)\r\n\t\t\t\t\tlet item = this.myPhotos[fileListLen]\r\n\t\t\t\t\tthis.myPhotos.splice(fileListLen, 1, Object.assign(item, {\r\n\t\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\t\tmessage: '',\r\n\t\t\t\t\t\turl: result\r\n\t\t\t\t\t}))\r\n\t\t\t\t\tfileListLen++\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tuploadPhotoFilePromise(url) {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tthis.$tools.uploadImage(\r\n\t\t\t\t\t\tthis.uploadInfo.url, {\r\n\t\t\t\t\t\t\tToken: this.uploadInfo.token\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\turl\r\n\t\t\t\t\t).then(res => {\r\n\t\t\t\t\t\tif(res.length>0)\r\n\t\t\t\t\t\tresolve(this.uploadInfo.preview +'/'+ res[0].name+res[0].ext)\r\n\t\t\t\t\t\t// resolve(this.uploadInfo.preview +'?file='+ res[0].name+res[0].ext)\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync afterRead(event) {\r\n\t\t\t\t// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式\r\n\t\t\t\tlet lists = [].concat(event.file)\r\n\t\t\t\tlet fileListLen = this[`fileList${event.name}`].length\r\n\t\t\t\tlists.map((item) => {\r\n\t\t\t\t\tthis.myPhotos.push({\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tstatus: 'uploading',\r\n\t\t\t\t\t\tmessage: '上传中'\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\tfor (let i = 0; i < lists.length; i++) {\r\n\t\t\t\t\tconst result = await this.uploadFilePromise(lists[i].url)\r\n\t\t\t\t\tlet item = this[`fileList${event.name}`][fileListLen]\r\n\t\t\t\t\tthis[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {\r\n\t\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\t\tmessage: '',\r\n\t\t\t\t\t\turl: result\r\n\t\t\t\t\t}))\r\n\t\t\t\t\tfileListLen++\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmyPhotoPreview(e){\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t},\r\n\t\t\tdeletePic(event) {\r\n\t\t\t\tthis[`fileList${event.name}`].splice(event.index, 1)\r\n\t\t\t},\r\n\t\t\tcheckboxChange(e){\r\n\t\t\t\tthis.$set(this.info,'areas',e)\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n\t.idcard-image {\r\n\t\tdisplay: flex;\r\n\t}\r\n\t\r\n\t.camera{\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-content: center;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\t.text{\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tline-height: 1.5;\r\n\t\t}\r\n\t}\r\n\t.sex-true {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: var(--colors);\r\n\t\tcolor: #fff;\r\n\t\t//background-color: orange;\r\n\t\tborder: 1rpx solid orange;\r\n\t}\r\n\r\n\t.card-body {\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-top:20rpx;\r\n\r\n\t\t.item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid rgb(240, 240, 240);\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\t.item-left {\r\n\t\t\t\tcolor: gray;\r\n\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t\tmin-width: 160rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item-right {\r\n\t\t\t\t/deep/ .ut-image-upload-list{\r\n\t\t\t\t\tjustify-content: flex-end;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t.sex {\r\n\t\t\t\t\tborder: 1rpx solid rgba(0, 0, 0, 0.4);\r\n\t\t\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t\r\n\r\n\t\t\t\t.ipt {\r\n\t\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t\t\tcolor: rgba(0, 0, 0, 0.4);\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t\r\n\t}\r\n\t\r\n\t.card-body:first-child{\r\n\t\tmargin-top: 0;\r\n\t}\r\n\t\r\n\t/deep/ .u-checkbox-group{\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./acc-msg.vue?vue&type=style&index=0&id=c658cea2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./acc-msg.vue?vue&type=style&index=0&id=c658cea2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360670646\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}