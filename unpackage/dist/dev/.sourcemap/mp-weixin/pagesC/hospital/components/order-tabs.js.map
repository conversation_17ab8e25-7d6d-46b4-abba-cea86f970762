{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-tabs.vue?f8b0", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-tabs.vue?b4ce", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-tabs.vue?684a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-tabs.vue?f9ed", "uni-app:///pagesC/hospital/components/order-tabs.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-tabs.vue?d943", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/order-tabs.vue?7b74"], "names": ["props", "bjColor", "type", "default", "tabHeight", "tabsData", "fontColor", "activeColor", "bold", "disable", "initIndex", "data", "tabIndex", "watch", "handler", "methods", "onTabIndex"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAssB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCgB1tB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QAAA;MAAA;IACA;IACAM;MACAP;MACAC;QAAA;MAAA;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACA;MACAC;IACA;EACA;EACAC;IACAH;MACAI;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA6zC,CAAgB,uoCAAG,EAAC,C;;;;;;;;;;;ACAj1C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/order-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./order-tabs.vue?vue&type=template&id=2c2489da&\"\nvar renderjs\nimport script from \"./order-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./order-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order-tabs.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/order-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-tabs.vue?vue&type=template&id=2c2489da&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-tabs.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"ut-tabs-container\"\r\n\t\t:style=\"{'background-color':bjColor, '--active-olor': activeColor, '--inactive-color':fontColor, '--bold': bold ? 'bold' :''}\">\r\n\t\t<scroll-view scroll-x scroll-with-animation class=\"ut-tabs-scroll\">\r\n\t\t\t<view class=\"ut-tabs-scroll-box\" :style=\"{'height': tabHeight,'line-height': tabHeight}\">\r\n\t\t\t\t<view class=\"ut-tabs-scroll-box-item\" v-for=\"(item,index) in tabsData\" :key=\"index\"\r\n\t\t\t\t\t@click=\"onTabIndex(index,item)\">\r\n\t\t\t\t\t<view class=\"name\" :class=\"tabIndex==index?'active':''\">{{item.name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tbjColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#fff\"\r\n\t\t\t},\r\n\t\t\ttabHeight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '90rpx'\r\n\t\t\t},\r\n\t\t\ttabsData: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfontColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#fff\"\r\n\t\t\t},\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#fff\"\r\n\t\t\t},\r\n\t\t\tbold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: () => false\r\n\t\t\t},\r\n\t\t\tdisable: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: () => false\r\n\t\t\t},\r\n\t\t\tinitIndex:{\r\n\t\t\t\ttype:Number,\r\n\t\t\t\tdefault:0,\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabIndex: this.initIndex\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\tinitIndex:{\r\n\t\t\t\thandler(v){\r\n\t\t\t\t\tthis.tabIndex=v\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonTabIndex(index, item) {\r\n\t\t\t\tif (this.disable) return\r\n\t\t\t\tthis.tabIndex = index;\r\n\t\t\t\tthis.$emit(\"change\", index, item)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$activeColor:var(--active-olor);\r\n\t$inactiveColor:var(--inactive-color);\r\n\t$bold:var(--bold);\r\n\r\n\t.ut-tabs-container {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.ut-tabs-scroll-box {\r\n\t\twhite-space: nowrap !important;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\r\n\t\t&-item {\r\n\t\t\tmin-width: 100rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 0rpx 10rpx;\r\n\t\t\tfont-size: 25rpx;\r\n\t\t\tletter-spacing: 4rpx;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.name {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tcolor: $inactiveColor;\r\n\t\t\t}\r\n\r\n\t\t\t.line {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tbottom: 10rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 5rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.active {\r\n\t\tcolor: $activeColor !important;\r\n\t\tfont-weight: $bold;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-tabs.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order-tabs.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360667076\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}