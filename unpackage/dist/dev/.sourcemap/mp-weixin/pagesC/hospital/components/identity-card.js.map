{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/identity-card.vue?85ce", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/identity-card.vue?764a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/identity-card.vue?7245", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/identity-card.vue?a4d4", "uni-app:///pagesC/hospital/components/identity-card.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/identity-card.vue?1fef", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesC/hospital/components/identity-card.vue?79f3"], "names": ["name", "data", "colors", "bgColor", "carList", "type", "remark", "fileList1", "methods", "afterRead", "lists", "fileListLen", "item", "status", "message", "i", "result", "url", "deletePic"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAysB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqB7tB;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;QACAJ;QACAK;QACAC;MACA;QACAN;QACAK;QACAC;MACA;QACAN;QACAK;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBACAC;gBACAD;kBACA,0EACAE;oBACAC;oBACAC;kBAAA,GACA;gBACA;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAJ;gBACA;kBACAC;kBACAC;kBACAG;gBACA;gBACAN;cAAA;gBARAI;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAUA;IACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAw1C,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACA52C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesC/hospital/components/identity-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./identity-card.vue?vue&type=template&id=7c0078b9&scoped=true&\"\nvar renderjs\nimport script from \"./identity-card.vue?vue&type=script&lang=js&\"\nexport * from \"./identity-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./identity-card.vue?vue&type=style&index=0&id=7c0078b9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7c0078b9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesC/hospital/components/identity-card.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./identity-card.vue?vue&type=template&id=7c0078b9&scoped=true&\"", "var components\ntry {\n  components = {\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-upload/u-upload\" */ \"@/components/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./identity-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./identity-card.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\" :style=\"{'--colors':colors,'--colors2':colors+'60','--colors3':colors+'90'}\">\r\n\t\t<view class=\"card-body\">\r\n\t\t\t<template v-for=\"(item,index) in carList\">\r\n\t\t\t\t<view :key=\"index\" class=\"item\">\r\n\t\t\t\t\t<view class=\"item-left\">\r\n\t\t\t\t\t\t{{ item.name }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item-right\">\r\n\t\t\t\t\t\t<input v-if=\"item.type == 'input'\" placeholder-class=\"ipt\" type=\"text\"\r\n\t\t\t\t\t\t\t:placeholder=\"item.remark\" />\r\n\t\t\t\t\t\t<u-upload v-if=\"item.type == 'upload'\" accept=\"image\" capture=\"camera\" :previewImage=\"true\"\r\n\t\t\t\t\t\t\tname=\"1\" multiple :maxCount=\"2\" uploadText=\"添加图片\" width=\"60\" height=\"60\"\r\n\t\t\t\t\t\t\t:fileList=\"fileList1\" @afterRead=\"afterRead\" @delete=\"deletePic\"></u-upload>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tname: 'IdentityCard',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcolors: '',\r\n\t\t\t\tbgColor: '#FFF',\r\n\t\t\t\tcarList: [{\r\n\t\t\t\t\tname: '身份证名称',\r\n\t\t\t\t\ttype: 'input',\r\n\t\t\t\t\tremark: '请填写身份证上的姓名'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tname: '身份证号',\r\n\t\t\t\t\ttype: 'input',\r\n\t\t\t\t\tremark: '请填写身份证号'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tname: '资料图片（正反两面）',\r\n\t\t\t\t\ttype: 'upload',\r\n\t\t\t\t\tremark: ''\r\n\t\t\t\t}, ],\r\n\t\t\t\tfileList1: [],\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync afterRead(event) {\r\n\t\t\t\t// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式\r\n\t\t\t\tlet lists = [].concat(event.file)\r\n\t\t\t\tlet fileListLen = this[`fileList${event.name}`].length\r\n\t\t\t\tlists.map((item) => {\r\n\t\t\t\t\tthis[`fileList${event.name}`].push({\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tstatus: 'uploading',\r\n\t\t\t\t\t\tmessage: '上传中'\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\tfor (let i = 0; i < lists.length; i++) {\r\n\t\t\t\t\tconst result = await this.uploadFilePromise(lists[i].url)\r\n\t\t\t\t\tlet item = this[`fileList${event.name}`][fileListLen]\r\n\t\t\t\t\tthis[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {\r\n\t\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\t\tmessage: '',\r\n\t\t\t\t\t\turl: result\r\n\t\t\t\t\t}))\r\n\t\t\t\t\tfileListLen++\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdeletePic(event) {\r\n\t\t\t\tthis[`fileList${event.name}`].splice(event.index, 1)\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.card-body {\r\n\t\tborder-radius: 14rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-top: 20rpx;\r\n\r\n\t\t.item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid rgb(240, 240, 240);\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\t.item-left {\r\n\t\t\t\tcolor: gray;\r\n\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item-right {\r\n\r\n\t\t\t\t// text-align: center;\r\n\t\t\t\t.ipt {\r\n\t\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t\t\tcolor: rgba(#000, 0.4);\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./identity-card.vue?vue&type=style&index=0&id=7c0078b9&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./identity-card.vue?vue&type=style&index=0&id=7c0078b9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360668044\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}