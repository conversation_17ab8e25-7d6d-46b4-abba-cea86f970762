{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/attendant/customer-list.vue?d3d5", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/attendant/customer-list.vue?5bcd", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/attendant/customer-list.vue?d6ed", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/attendant/customer-list.vue?6131", "uni-app:///pagesA/nurse/attendant/customer-list.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/attendant/customer-list.vue?1729", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/attendant/customer-list.vue?8efa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "options", "styleIsolation", "data", "colors", "topWrapHeight", "attendantId", "<PERSON><PERSON><PERSON>", "_mapContext", "latitude", "longitude", "mapMarkers", "customerList", "showDetail", "selIndex", "onShow", "onReady", "onLoad", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "detail", "methods", "getHeight", "markTap", "init", "obj", "enableDefaultStyle", "zoomOnClick", "gridSize", "complete", "console", "clusterId", "markerIds", "center", "iconPath", "width", "height", "label", "content", "fontSize", "color", "bgColor", "borderRadius", "textAlign", "anchorX", "anchorY", "markers", "clear", "addMarkers", "Object", "id", "joinCluster", "callout", "display", "fontWeight", "padding", "getList", "communityId", "module", "index", "type", "name", "imgHead", "address", "customerName", "lat", "lng"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAysB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC2C7tB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAGA;EACAC,aACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAEAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MAAAX;IAAA;EACA;EACAY;IACA;EAEA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;gBACA;cACA;cAEA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;MACA;MACA;IACA;EAAA,EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IAEAC;MAAA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACAC;UAAA;QAAA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACAC;QACA;MACA;MACA;QACAA;QACA;QACA;UACA;YAAAC;YAAAC;UACA,uCACAC;YACAC;YACAC;YACAC;YACAL;YAAA;YACAM;cACAC;cACAC;cACAC;cACAL;cACAC;cACAK;cACAC;cACAC;cACAC;cACAC;YACA;UAAA;QAEA;QACA;UACAC;UACAC;UACAlB;YACAC;UACA;QACA;MACA;MACA;QACAA;MACA;MACA;IACA;IAEA;IACAkB;MACA;MACA;QACA;QACA;QACA;QACAF,aACAG;UACAC;UACA5C;UACAC;UACA2B;UACAC;UACAC;UACAe;UAAA;UACAC;YACAX;YACAD;YACAF;YACAe;YACAd;YACAe;YACAC;YACAZ;YACAD;UACA;QACA,GACA;MACA;MACA;QACAI;QACAC;QACAlB;UACAC;QACA;MACA;IACA;IAEA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;kBACAvD;gBACA;cAAA;gBAAA;gBAJAH;gBAMA;gBACA;gBACA2D;gBACA;kBACA;kBACA;oBACAT;oBACAU;oBACAC;oBACAvD;oBACAC;oBACAuD;oBACAC;kBACA;kBACAJ;gBACA;gBACA;kBACA3D;oBACA;sBACA;wBACAkD;wBACAU;wBACAC;wBACAvD;wBACAC;wBACAyD;wBACAF;wBACAC;sBACA;sBACAJ;oBACA;kBACA;gBACA;gBACA;kBACA;oBACAT;oBACAU;oBACAC;oBACAI;oBACAC;kBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtQA;AAAA;AAAA;AAAA;AAAw1C,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACA52C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/nurse/attendant/customer-list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/nurse/attendant/customer-list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./customer-list.vue?vue&type=template&id=61aa0235&scoped=true&\"\nvar renderjs\nimport script from \"./customer-list.vue?vue&type=script&lang=js&\"\nexport * from \"./customer-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./customer-list.vue?vue&type=style&index=0&id=61aa0235&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61aa0235\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/nurse/attendant/customer-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer-list.vue?vue&type=template&id=61aa0235&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uLazyLoad: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-lazy-load/u-lazy-load\" */ \"@/components/uview-ui/components/u-lazy-load/u-lazy-load.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !!_vm.detail.imgHead ? _vm.$tools.showImg(_vm.detail.imgHead) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showDetail = false\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$tools.previewImage([_vm.detail.imgHead], 0, 800)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer-list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ut-page>\r\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\r\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" :title=\"attendantName+'护理员客户'\" navbarType=\"1\"></f-navbar>\r\n\t\t</ut-top>\r\n\t\t<view class=\"content\" :style=\"{height:'calc(100vh - '+topWrapHeight+'px)'}\">\r\n\t\t\t<map id=\"map\" :latitude=\"latitude\" :longitude=\"longitude\" :show-location=\"false\"\r\n\t\t\t\t:style=\"{ width: '100%', height:  '100%' }\" :scale=\"12\" @markertap=\"markTap\" @labeltap=\"markTap\"></map>\r\n\t\t</view>\r\n\t\t<u-popup :show=\"showDetail\" mode=\"bottom\" round=\"10\" :closeable=\"true\" :safe-area-inset-bottom=\"false\"\r\n\t\t\t\t :mask-close-able=\"true\" close-icon-pos=\"top-left\" :z-index=\"998\" :overlay-style=\"{zIndex:998}\" @close=\"showDetail=false\">\r\n\t\t\r\n\t\t\t<view class=\"pop-content\">\r\n\t\t\t\t<view class=\"item-box\">\r\n\t\t\t\t\t\t<view class=\"head-image\">\r\n\t\t\t\t\t\t\t<text v-if=\"!detail.imgHead\" class=\"cuIcon-people\" />\r\n\t\t\t\t\t\t\t<u-lazy-load v-else :image=\"$tools.showImg(detail.imgHead)\" width=\"120\" height=\"160\" border-radius=\"4\" @click=\"$tools.previewImage([detail.imgHead],0,800)\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"content text-content\">\r\n\t\t\t\t\t\t\t<view class=\"flex justify-between \">\r\n\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"flex justify-start align-center\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-xl text-bold\" >{{detail.name}}</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-df text-blue margin-left-lg\" v-if=\"detail.sex==1\">男<text class=\"cuIcon-male\" /></view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"text-df text-pink margin-left-lg\" v-if=\"detail.sex==2\">女<text class=\"cuIcon-female\" /></view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<view><text v-if=\"detail.phone\" class=\"cuIcon-phone\" />{{detail.phone}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"text-xs text-gray address\">{{detail.address}}</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</ut-page>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp()\r\nimport {mapState} from 'vuex'\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t},\r\n\toptions: {\r\n\t\tstyleIsolation: 'shared',\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcolors: '',\n\t\t\ttopWrapHeight: 0,\r\n\t\t\t\r\n\t\t\tattendantId:'',\n\t\t\tattendantName:'',\n\t\t\t\r\n\t\t\t_mapContext: null,\r\n\t\t\tlatitude: 0,\r\n\t\t\tlongitude: 0,\r\n\t\t\t\r\n\t\t\tmapMarkers:[],\r\n\t\t\tcustomerList:[],\r\n\t\t\tshowDetail:false,\r\n\t\t\tselIndex:0,\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.setData({ colors: app.globalData.newColor })\r\n\t},\r\n\tonReady() {\r\n\t\tthis._mapContext = uni.createMapContext(\"map\", this);\r\n\t\t\r\n\t},\r\n\tonLoad: async function (options) {\r\n\t\tawait this.$onLaunched\r\n\t\t\r\n\t\tif (options.attendantId) {\r\n\t\t\tthis.attendantId = options.attendantId\r\n\t\t}\r\n\t\t\r\n\t\tthis.getList()\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState({\r\n\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t\ttoken: state => state.user.token,\r\n\t\t\tuserInfo: state => state.user.info,\r\n\t\t\tcommunity: state => state.init.community,\r\n\t\t}),\r\n\t\tdetail(){\r\n\t\t\tif(this.selIndex<=0) return {}\r\n\t\t\tif(!this.mapMarkers || !this.mapMarkers.length) return {}\r\n\t\t\treturn this.mapMarkers[this.selIndex-1]\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetHeight(h) {\r\n\t\t\tthis.topWrapHeight = h\r\n\t\t},\r\n\t\tmarkTap(e) {\r\n\t\t\tthis.selIndex=e.markerId\r\n\t\t\tthis.showDetail=true\r\n\t\t},\r\n\r\n\t\tinit() {\r\n\t\t\tlet obj=this.mapMarkers.find(u=>u.type=='attendant')\r\n\t\t\tif(obj){\r\n\t\t\t\tthis.latitude=obj.latitude\r\n\t\t\t\tthis.longitude=obj.longitude\r\n\t\t\t}else{\r\n\t\t\t\tobj=this.mapMarkers.find(u=>u.type=='new')\r\n\t\t\t\tif(obj){\r\n\t\t\t\t\tthis.latitude=obj.latitude\r\n\t\t\t\t\tthis.longitude=obj.longitude\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.latitude=this.mapMarkers[0].latitude\r\n\t\t\t\t\tthis.longitude=this.mapMarkers[0].longitude\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 仅调用初始化，才会触发 on.(\"markerClusterCreate\", (e) => {})\r\n\t\t\tthis._mapContext.initMarkerCluster({\r\n\t\t\t\tenableDefaultStyle: false, // 是否使用默认样式\r\n\t\t\t\tzoomOnClick: true, // 点击聚合的点，是否改变地图的缩放级别\r\n\t\t\t\tgridSize: 30, // 聚合计算时网格的像素大小，默认60\r\n\t\t\t\tcomplete(res) {\r\n\t\t\t\t\tconsole.log('initMarkerCluster', res)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tthis._mapContext.on(\"markerClusterCreate\", (res) => {\r\n\t\t\t\tconsole.log(\"markerClusterCreate\", res);\r\n\t\t\t\tconst clusters = res.clusters\r\n\t\t\t\tconst markers = clusters.map(cluster => {\r\n\t\t\t\t\tconst {\tcenter,\tclusterId,\tmarkerIds} = cluster\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t...center,\r\n\t\t\t\t\t\ticonPath: '/static/imgs/map/none.png',\r\n\t\t\t\t\t\twidth: 1,\r\n\t\t\t\t\t\theight: 1,\r\n\t\t\t\t\t\tclusterId, // 必须\r\n\t\t\t\t\t\tlabel: {\r\n\t\t\t\t\t\t\tcontent: markerIds.length + '',\r\n\t\t\t\t\t\t\tfontSize: 16,\r\n\t\t\t\t\t\t\tcolor: '#ffffff',\r\n\t\t\t\t\t\t\twidth: 40,\r\n\t\t\t\t\t\t\theight: 40,\r\n\t\t\t\t\t\t\tbgColor: '#00A3FA',\r\n\t\t\t\t\t\t\tborderRadius: 20,\r\n\t\t\t\t\t\t\ttextAlign: 'center',\r\n\t\t\t\t\t\t\tanchorX: 0,\r\n\t\t\t\t\t\t\tanchorY: -30,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tthis._mapContext.addMarkers({\r\n\t\t\t\t\tmarkers,\r\n\t\t\t\t\tclear: false,\r\n\t\t\t\t\tcomplete(res) {\r\n\t\t\t\t\t\tconsole.log('clusterCreate addMarkers', res)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t});\r\n\t\t\tthis._mapContext.on('markerClusterClick', (res) => {\r\n\t\t\t\tconsole.log('markerClusterClick', res)\r\n\t\t\t})\r\n\t\t\tthis.addMarkers();\r\n\t\t},\r\n\r\n\t\t// 添加标记点\r\n\t\taddMarkers() {\r\n\t\t\tconst markers = []\r\n\t\t\tthis.mapMarkers.forEach((p, i) => {\r\n\t\t\t\tvar path='/static/imgs/map/marker_blue.png'\r\n\t\t\t\tif(p.type=='new') path='/static/imgs/map/small.png'\r\n\t\t\t\tif(p.type=='customer')path='/static/imgs/map/big.png'\r\n\t\t\t\tmarkers.push(\r\n\t\t\t\t\tObject.assign({}, {\r\n\t\t\t\t\t\tid: p.id,\r\n\t\t\t\t\t\tlatitude:p.latitude,\r\n\t\t\t\t\t\tlongitude:p.longitude,\r\n\t\t\t\t\t\ticonPath: path,\r\n\t\t\t\t\t\twidth: 28,\r\n\t\t\t\t\t\theight: 36,\r\n\t\t\t\t\t\tjoinCluster: true, // 指定了该参数才会参与聚合\r\n\t\t\t\t\t\tcallout: {\r\n\t\t\t\t\t\t\tbgColor: \"#5AC2EB\",\r\n\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\tcontent: `${p.name}`,\r\n\t\t\t\t\t\t\tdisplay: \"ALWAYS\",\r\n\t\t\t\t\t\t\tfontSize: \"13\",\r\n\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\tpadding: 4,\r\n\t\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\t\tborderRadius:2,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t)\r\n\t\t\t})\r\n\t\t\tthis._mapContext.addMarkers({\r\n\t\t\t\tmarkers,\r\n\t\t\t\tclear: false,\r\n\t\t\t\tcomplete(res) {\r\n\t\t\t\t\tconsole.log('addMarkers', res)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\r\n\t\tasync getList(){\r\n\t\t\tconst {data} = await this.$ut.api('mang/nurse/attendant/customer/locationAll', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tmodule:'long',\r\n\t\t\t\tattendantId:this.attendantId,\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\tthis.customerList=data.customers || []\r\n\t\t\tthis.mapMarkers=[]\r\n\t\t\tvar index=1\r\n\t\t\tif(data){\r\n\t\t\t\tthis.attendantName=data.name\r\n\t\t\t\tthis.mapMarkers.push({\r\n\t\t\t\t\tid:index,\r\n\t\t\t\t\ttype:'attendant',\r\n\t\t\t\t\tname:data.name,\r\n\t\t\t\t\tlatitude:data.latitude,\r\n\t\t\t\t\tlongitude:data.longitude,\r\n\t\t\t\t\timgHead:data.imgHead?data.imgHead:'',\r\n\t\t\t\t\taddress:data.address?data.address:'',\r\n\t\t\t\t})\r\n\t\t\t\tindex++\r\n\t\t\t}\r\n\t\t\tif(data && data.customers){\r\n\t\t\t\tdata.customers.forEach(item=>{\r\n\t\t\t\t\tif(item.longitude && item.latitude){\r\n\t\t\t\t\t\tthis.mapMarkers.push({\r\n\t\t\t\t\t\t\tid:index,\r\n\t\t\t\t\t\t\ttype:'customer',\r\n\t\t\t\t\t\t\tname:item.name,\r\n\t\t\t\t\t\t\tlatitude:item.latitude,\r\n\t\t\t\t\t\t\tlongitude:item.longitude,\r\n\t\t\t\t\t\t\tcustomerName:item.name,\r\n\t\t\t\t\t\t\timgHead:item.imgHead,\r\n\t\t\t\t\t\t\taddress:item.address,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tindex++\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tif(data && data.newCustomer){\r\n\t\t\t  this.mapMarkers.push({\r\n\t\t\t\t  id:index,\r\n\t\t\t\t  type:'new',\r\n\t\t\t\t  name:'新客户',\r\n\t\t\t\t  lat:data.newCustomer.latitude,\r\n\t\t\t\t  lng:data.newCustomer.longitude,\r\n\t\t\t  })\r\n\t\t\t}\r\n\t\t\tthis.init();\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.logo {\r\n\t\theight: 200rpx;\r\n\t\twidth: 200rpx;\r\n\t\tmargin-top: 200rpx;\r\n\t\tmargin-left: auto;\r\n\t\tmargin-right: auto;\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.text-area {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #8f8f94;\r\n\t}\r\n\t\r\n\t.pop-content{\r\n\t\tmargin-top: 60rpx;\r\n\t}\r\n\t\r\n\t.item-box{\r\n\t\tpadding: 20rpx 30rpx; \r\n\t\tbackground-color: white; \r\n\t\tmargin-bottom: 10rpx;\r\n\t\tdisplay: flex;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\t.content{\r\n\t\t\tflex: 1;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t}\r\n\t\t\r\n\t}\r\n\t\r\n\t.block-btn {\r\n\t\twidth: 190rpx;\r\n\t\theight: 60rpx;\r\n\t\tcolor:#fff;\r\n\t\tbackground: linear-gradient(90deg, var(--colors), var(--colors));\r\n\t\tborder: 2rpx solid rgba(230, 184, 115, 0.3);\r\n\t\tborder-radius: 80rpx;\r\n\t\tpadding: 8rpx 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: 700;\r\n\t}\r\n\t\r\n\t.cuIcon-people{\r\n\t\tfont-size: 116rpx;\r\n\t\tcolor: gray;\r\n\t\tborder: 1rpx solid #ccc;\r\n\t\twidth: 116rpx;\r\n\t\theight: 156rpx;\r\n\t\tline-height: 156rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.address{\r\n\t\theight: 36rpx;\r\n\t\toverflow: hidden; /* 隐藏溢出的内容 */\r\n\t\ttext-overflow: ellipsis; /* 使用省略号表示溢出的文本 */\r\n\t\t-o-text-overflow: ellipsis;\r\n\t\tdisplay: -webkit-box;\r\n\t\t-webkit-line-clamp: 1;\r\n\t\t-webkit-box-orient: vertical;\r\n\t}\r\n\t\r\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer-list.vue?vue&type=style&index=0&id=61aa0235&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./customer-list.vue?vue&type=style&index=0&id=61aa0235&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673508\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}