{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/second.vue?5c4e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/second.vue?cabc", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/second.vue?f330", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/second.vue?91ba", "uni-app:///pagesA/nurse/assign-second/second.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/second.vue?d849", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/nurse/assign-second/second.vue?f704"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "CustomerInfo", "HhGrid", "PickDate", "options", "styleIsolation", "data", "colors", "secondId", "customer", "show", "attendants", "selAttendant", "fileData", "pageReq", "pagesize", "pageindex", "key", "isEdit", "form", "validBegin", "validEnd", "remark", "rules", "required", "message", "trigger", "menus", "onShow", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "jump<PERSON><PERSON><PERSON>", "onLoad", "methods", "getCustomerInfo", "communityId", "setStar", "loadInfo", "getSecondStateInfo", "goSubmit", "go", "uni", "id", "title", "icon", "setTimeout", "validBeginSelect", "validEndSelect"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,wPAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAksB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuEttB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAMA;EACAC;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;QACAC;MACA;MACAC;MAEAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;UACAI;UACAC;UACAC;QACA;QACAL;UACAG;UACAC;UACAC;QACA;MACA;MAEAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EACA;EACAC;IACA;MAAArB;IAAA;EACA;EACAsB,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QAAA1B;QAAAU;MAAA;IACA;EAAA,EACA;EACAiB;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAEA;gBACA;cACA;cAEA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACA9B;gBACA;cAAA;gBAAA;gBAHAF;gBAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAiC;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACAH;kBACA9B;gBACA;cAAA;gBAAA;gBAHAF;gBAIA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAoC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OACA,kDACA;kBACAA;kBACAC;kBACA;gBACA;cAAA;gBAAA,IACAD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;kBACAL;kBACAO;gBAAA,GACA,aACA;kBACAD;oBACAE;oBACAC;kBACA;kBAEAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACxQA;AAAA;AAAA;AAAA;AAA2/B,CAAgB,i4BAAG,EAAC,C;;;;;;;;;;;ACA/gC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/nurse/assign-second/second.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/nurse/assign-second/second.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./second.vue?vue&type=template&id=9a789012&\"\nvar renderjs\nimport script from \"./second.vue?vue&type=script&lang=js&\"\nexport * from \"./second.vue?vue&type=script&lang=js&\"\nimport style0 from \"./second.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/nurse/assign-second/second.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./second.vue?vue&type=template&id=9a789012&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    \"u-Form\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--form/u--form\" */ \"@/components/uview-ui/components/u--form/u--form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-form-item/u-form-item\" */ \"@/components/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--input/u--input\" */ \"@/components/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    \"u-Textarea\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--textarea/u--textarea\" */ \"@/components/uview-ui/components/u--textarea/u--textarea.vue\"\n      )\n    },\n    utFixed: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-fixed/ut-fixed\" */ \"@/components/ut/ut-fixed/ut-fixed.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.menus.length\n  var g1 = !(g0 > 4) ? _vm.menus.length : null\n  var g2 = new Date().getFullYear()\n  var g3 = new Date().getFullYear()\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$refs.dateValidBeginPicker.show()\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$refs.dateValidEndPicker.show()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./second.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./second.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<ut-page>\r\n\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"复评\" navbarType=\"2\"></f-navbar>\r\n\t\t<view class=\"cu-card margin-sm radius padding-sm shadow s-gray bg-white\">\r\n\t\t\t<customer-info :detail=\"customer\" :show-all=\"false\" :colors=\"colors\" :file-data=\"fileData\" />\r\n\t\t\t<view class=\"flex align-center\">\r\n\t\t\t\t<text v-if=\"customer.groupName\" class=\"text-sm\" :style=\"{color:colors}\">({{customer.groupName}})</text>\r\n\t\t\t\t<text v-if=\"customer.attendantName\">护理员：</text>\r\n\t\t\t\t<text v-if=\"customer.attendantName\" class=\"text-lg text-bold\">{{customer.attendantName}}</text>\r\n\t\t\t\t<text v-else class=\"text-red text-bold\">未指定护理员</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"cu-card margin-sm radius shadow padding-sm s-gray bg-white\">\r\n\t\t\t<u--form ref=\"form\" labelPostion=\"left\" :model=\"form\" :rules=\"rules\" labelWidth=\"auto\" :labelStyle=\"{color:'#a0a0a0'}\">\r\n\t\t\t\t<view class=\"date text-df\">\r\n\t\t\t\t\t<u-form-item label=\"有效时间(起)\" prop=\"validBegin\" borderBottom>\r\n\t\t\t\t\t\t<view class=\"full-width\" @click=\"$refs.dateValidBeginPicker.show()\" >\r\n\t\t\t\t\t\t\t<u--input v-model.trim=\"form.validBegin\" border=\"none\" disabled disabledColor=\"#ffffff\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请选择开始有效时间\" inputAlign='center' style=\"pointer-events:none\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</u-form-item>\r\n\t\t\r\n\t\t\t\t\t<u-form-item label=\"有效时间(止)\" prop=\"validEnd\" borderBottom>\r\n\t\t\t\t\t\t<view class=\"full-width\" @click=\"$refs.dateValidEndPicker.show()\" >\r\n\t\t\t\t\t\t\t<u--input v-model.trim=\"form.validEnd\" border=\"none\" disabled disabledColor=\"#ffffff\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"请选择截止有效时间\" inputAlign='center' style=\"pointer-events:none\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</u-form-item>\r\n\t\t\t\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</u--form>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"cu-card margin-sm radius shadow padding-tb s-gray bg-white\">\r\n\t\t\t<hh-grid  :colors=\"colors\" :list=\"menus\" :count=\"menus.length>4?4:menus.length\" :cell-color=\"true\" :jump-param=\"jumpParam\" />\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"cu-card margin-sm radius shadow padding-sm s-gray bg-white\">\r\n\t\t\t<view class=\"text-content\">\r\n\t\t\t\t<view>备注：</view>\r\n\t\t\t\t<u--textarea v-model=\"form.remark\" placeholder=\"请输入内容\" :maxlength=\"500\" count confirmType=\"done\"></u--textarea>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"padding-tb-xl margin-lg\"></view>\r\n\t\t\r\n\t\t<ut-fixed safe-area-inset position=\"bottom\" background=\"#fff\">\r\n\t\t\t<view class=\"padding-tb-sm padding-lr-lg\">\r\n\t\t\t\t<u-button v-if=\"isEdit==true\" type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"提交复评\" size=\"normal\"  @click=\"goSubmit\"></u-button>\r\n\t\t\t\t<u-button v-else shape=\"circle\" :disabled=\"true\" :color=\"colors\" text=\"已经提交,无需重复操作\" size=\"normal\"></u-button>\r\n\t\t\t</view>\r\n\t\t</ut-fixed>\r\n\t\t\r\n\t\t<pick-date ref=\"dateValidBeginPicker\" :start-year=\"new Date().getFullYear()\"\r\n\t\t\t:time-init=\"0\"\r\n\t\t\t:time-hide=\"[true, true, true, false, false, false]\" \r\n\t\t\t:time-label=\"['年', '月', '日', '时', '分', '秒']\"\r\n\t\t\t@submit=\"validBeginSelect\" />\r\n\t\t\t\r\n\t\t<pick-date ref=\"dateValidEndPicker\" :start-year=\"new Date().getFullYear()\" \r\n\t\t\t:time-init=\"0\"\r\n\t\t\t:time-hide=\"[true, true, true, false, false, false]\" \r\n\t\t\t:time-label=\"['年', '月', '日', '时', '分', '秒']\"\r\n\t\t\t@submit=\"validEndSelect\" />\r\n\r\n\t</ut-page>\r\n</template>\r\n\r\n<script>\r\nvar app = getApp()\r\nimport { mapState } from 'vuex'\r\nimport CustomerInfo from '@/pagesA/components/customer-info.vue'\r\nimport HhGrid from '@/pagesA/components/hh-grid.vue'\r\nimport PickDate from '@/pagesA/components/pickDate.vue'\r\n\r\nexport default {\r\n\tmixins: [], \r\n\tcomponents: {\r\n\t\tCustomerInfo,\r\n\t\tHhGrid,\r\n\t\tPickDate,\r\n\t},\r\n\toptions: {\r\n\t\tstyleIsolation: 'shared',\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcolors: '',\r\n\t\t\t\r\n\t\t\tsecondId:'',\r\n\t\t\tcustomer:{},\r\n\t\t\tshow:false,\r\n\t\t\tattendants:[],\r\n\t\t\tselAttendant:{},\r\n\t\t\tfileData:[],\r\n\t\t\t\r\n\t\t\tpageReq: {\r\n\t\t\t\tpagesize: 20,\r\n\t\t\t\tpageindex: 1,\r\n\t\t\t\tkey: '',\r\n\t\t\t},\n\t\t\tisEdit:false,\r\n\t\t\t\r\n\t\t\tform:{\r\n\t\t\t\tvalidBegin:'',\r\n\t\t\t\tvalidEnd:'',\r\n\t\t\t\tremark:'',\r\n\t\t\t},\r\n\t\t\trules: {\r\n\t\t\t\tvalidBegin: [{\r\n\t\t\t\t\trequired: true,\r\n\t\t\t\t\tmessage: '请选择开始时间',\r\n\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t}],\r\n\t\t\t\tvalidEnd: [{\r\n\t\t\t\t\trequired: true,\r\n\t\t\t\t\tmessage: '请选择结束时间',\r\n\t\t\t\t\ttrigger: ['blur', 'change']\r\n\t\t\t\t}],\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tmenus:[\r\n\t\t\t\t// {\r\n\t\t\t\t// \t'name': '现场定位',\r\n\t\t\t\t// \t'url': '/pagesA/nurse/assign-second/position',\r\n\t\t\t\t// \t'image': 'https://oss.afjy.net/api/file/preview?file=e0K3xVj',\r\n\t\t\t\t// \t'code':'location',\r\n\t\t\t\t// \t'num': 0,\r\n\t\t\t\t// \t'star':false,\r\n\t\t\t\t// },\r\n\t\t\t\t{\r\n\t\t\t\t\t'name': '现场照片',\r\n\t\t\t\t\t'url': '/pagesA/nurse/assign-second/scene-photo',\r\n\t\t\t\t\t'image': 'https://oss.afjy.net/api/file/preview?file=e0K7iQQ',\r\n\t\t\t\t\t'code':'photo',\r\n\t\t\t\t\t'num': 0,\r\n\t\t\t\t\t'star':false,\r\n\t\t\t\t},{\r\n\t\t\t\t\t'name': '文件资料',\r\n\t\t\t\t\t'url': '/pagesA/nurse/assign-second/file-data',\r\n\t\t\t\t\t'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',\r\n\t\t\t\t\t'code':'data',\r\n\t\t\t\t\t'num': 0,\r\n\t\t\t\t\t'star':false,\r\n\t\t\t\t},{\r\n\t\t\t\t\t'name': '项目评定',\r\n\t\t\t\t\t'url': '/pagesA/nurse/assign-second/project',\r\n\t\t\t\t\t'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',\r\n\t\t\t\t\t'code':'project',\r\n\t\t\t\t\t'num': 0,\r\n\t\t\t\t\t'star':false,\r\n\t\t\t\t},{\r\n\t\t\t\t\t'name': '头像修改',\r\n\t\t\t\t\t'url': '/pagesA/nurse/assign-second/img-head',\r\n\t\t\t\t\t'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',\r\n\t\t\t\t\t'code':'imgHead',\r\n\t\t\t\t\t'num': 0,\r\n\t\t\t\t},{\r\n\t\t\t\t\t'name': '信息修改',\r\n\t\t\t\t\t'url': '/pagesA/nurse/assign-second/save',\r\n\t\t\t\t\t'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',\r\n\t\t\t\t\t'code':'info',\r\n\t\t\t\t\t'num': 0,\r\n\t\t\t\t},{\r\n\t\t\t\t\t'name': '位置重定位',\r\n\t\t\t\t\t'url': '/pagesA/nurse/assign-second/location',\r\n\t\t\t\t\t'image': 'https://oss.afjy.net/api/file/preview?file=e0K9koe',\r\n\t\t\t\t\t'code':'reLocation',\r\n\t\t\t\t\t'num': 0,\r\n\t\t\t\t},\r\n\t\t\t],\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.setData({ colors: app.globalData.newColor })\r\n\t},\r\n\tcomputed: {\r\n\t\t...mapState({\r\n\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t\ttoken: state => state.user.token,\r\n\t\t\tuserInfo: state => state.user.info,\r\n\t\t\tcommunity: state => state.init.community,\r\n\t\t}),\r\n\t\tjumpParam(){\r\n\t\t\treturn {secondId:this.secondId,isEdit:this.isEdit}\r\n\t\t}\r\n\t},\r\n\tonLoad: async function (options) {\r\n\t\tawait this.$onLaunched\r\n\t\t\r\n\t\tif (options.secondId) {\r\n\t\t\tthis.secondId = options.secondId\r\n\t\t}\r\n\t\t\r\n\t\tthis.loadInfo()\r\n\t},\r\n\tmethods: {\r\n\t\tasync getCustomerInfo(){\r\n\t\t\tif(!this.secondId) return\r\n\t\t\tconst {data} = await this.$ut.api('mang/nurse/second/info', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tsecondId:this.secondId,\r\n\t\t\t})\r\n\t\t\tthis.customer=data ||{}\r\n\t\t},\r\n\t\tsetStar(code,v){\r\n\t\t\tlet obj=this.menus.find(u=>u.code==code)\r\n\t\t\tif(obj)this.$set(obj,'star',v===true)\r\n\t\t},\r\n\t\tloadInfo(){\r\n\t\t\tthis.getCustomerInfo()\r\n\t\t\tthis.getSecondStateInfo()\r\n\t\t},\r\n\t\tasync getSecondStateInfo(){\r\n\t\t\tif(!this.secondId) return\r\n\t\t\tconst {data} = await this.$ut.api('mang/nurse/second/state_info', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tsecondId:this.secondId,\r\n\t\t\t})\r\n\t\t\tthis.isEdit=data.isEdit\r\n\t\t\tthis.setStar('location',data.needLocate)\r\n\t\t\tthis.setStar('photo',data.needPhoto)\r\n\t\t\tthis.setStar('data',data.needData)\r\n\t\t\tthis.setStar('project',data.needProject)\r\n\t\t\tthis.setStar('imgHead',data.needImgHead)\r\n\t\t\t\r\n\t\t\tthis.form.validBegin=data.validBegin\r\n\t\t\tthis.form.validEnd=data.validEnd\r\n\t\t\tthis.form.remark=data.remark\r\n\t\t},\r\n\t\tasync goSubmit(){\r\n\t\t\tlet go=true\r\n\t\t\tawait this.$refs.form.validate().then(res => {\r\n\t\t\t}).catch(errors => {\r\n\t\t\t\tgo=false\r\n\t\t\t\tuni.$u.toast('请检查必须填写的内容！')\r\n\t\t\t\treturn\r\n\t\t\t})\r\n\t\t\tif(!go) return\r\n\t\t\t\r\n\t\t\tthis.$ut.api('mang/nurse/second/submit', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tid:this.secondId,\r\n\t\t\t\t...this.form,\r\n\t\t\t}).then(()=>{\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle:'提交成功',\r\n\t\t\t\t\ticon:'none',\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.$tools.back('downCallback()')\r\n\t\t\t\t},1500)\r\n\t\t\t})\r\n\t\t},\r\n\t\tvalidBeginSelect(e){\r\n\t\t\tthis.form.validBegin=`${e.year}-${e.month}-${e.day}`\r\n\t\t},\r\n\t\tvalidEndSelect(e){\r\n\t\t\tthis.form.validEnd=`${e.year}-${e.month}-${e.day}`\r\n\t\t},\r\n\r\n\t},\r\n}\r\n</script>\r\n\r\n<style>\r\n.card-box {\r\n\tmargin: 30rpx 20rpx;\r\n\tpadding: 40rpx 30rpx 20rpx 30rpx;\r\n\tbackground-size: 100% 100%;\r\n\tborder-radius: 10rpx;\r\n\tbackground-color: #fff;\r\n\toverflow: hidden;\r\n\tbottom: 15rpx;\r\n}\r\n\r\n.scroll-box {\r\n\tpadding-top: 60rpx;\r\n\tpadding-bottom: 10rpx;\r\n\tpadding-left: 20rpx;\r\n\tpadding-right: 20rpx;\r\n\tmin-height: 30%;\r\n\tmax-height: 80%;\r\n}\r\n\r\n\r\n.pop-title {\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tpadding: 15rpx;\r\n\tmargin: auto;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\ttext-align: center;\r\n}\r\n\r\n.clear{\r\n\tposition: absolute;\r\n\tright: 0;\r\n\tpadding: 15rpx 30rpx 15rpx 15rpx;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\ttext-align: center;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./second.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./second.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360670255\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}