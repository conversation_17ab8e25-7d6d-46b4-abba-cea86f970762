{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/ut-person-upload/up-person-upload.vue?0c3e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/ut-person-upload/up-person-upload.vue?83cb", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/ut-person-upload/up-person-upload.vue?17e0", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/ut-person-upload/up-person-upload.vue?7e3f", "uni-app:///pagesA/components/ut-person-upload/up-person-upload.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/ut-person-upload/up-person-upload.vue?59ef", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/ut-person-upload/up-person-upload.vue?2eef"], "names": ["components", "DiyPersonCamera", "props", "value", "type", "default", "disabled", "remove", "name", "front", "quality", "width", "colors", "height", "mode", "borderRadius", "action", "headers", "formData", "previewImageWidth", "uploadSuccess", "success", "url", "data", "systemInfo", "statusBarHeight", "navBarH", "titleBarHeight", "showCamera", "imageUrl", "uploadLists", "imageLoaded", "item", "computed", "watch", "id", "state", "progress", "methods", "imageLoadingWidth", "imgPreviewClick", "uni", "urls", "current", "loop", "imageLoad", "chooseFile", "confirmPhoto", "console", "leftClick", "imgUpload", "title", "tempFilePaths", "filePath", "fileType", "header", "fail", "reject", "complete", "task", "uploadImgs", "Promise", "then", "catch", "uniCloudUpload", "uniCloud", "cloudPath", "resolve", "fileList", "res", "getFileType", "result", "guid", "v", "triggerMonitor", "uploadTask", "mergerProgress", "tasks", "setUpMonitor", "load", "obj", "showImg", "str", "checkLoad", "imgDel", "content"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA4sB,CAAgB,8oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCwDhuB;EAEAA;IACAC;EACA;EACAC;IACAC;MAAA;MACAC;MACAC;IACA;IACAC;MAAA;MACAF;MACAC;IACA;IACAE;MAAA;MACAH;MACAC;IACA;IACAG;MAAA;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MAAA;MACAZ;MACAC;IACA;IACAY;MAAA;MACAb;MACAC;IACA;IACAa;MAAA;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACAe;MACAf;QACA;UACAgB;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACAC;QAMAC;QAAA;QACAC;MAEA;;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,WAEA;EACAC;IACA/B;MACA;MACA;QACAgC;QACAb;QACAc;QACAC;MACA;IAEA;EAEA;EACAC;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MACAT;QACAU;MACA;MAEA;MACAC;QACA;UACAjB;UACAC;UACAC;UACAf;UACAC;QACA;QACA;MACA;MAGA;MAEA;QACA;UACAD;UAAA;UACA+B;UACA7C;UACA8C;UACApC;UACAqC;UACAlC;YACA;YACA;YACA;YACA;YACA;UACA;UACAmC;YACAC;YACA;UACA;UACAC;QACA;QACAC;QACAA;UACA,yBACA;YACA;YACA;UACA;QACA;QACAC;MACA;MACAC;MAAA,CACAC;QACArB;MACA,GACAsB;QACAtB;QACA;MACA;IAEA;IACAuB;MAAA;MACAvB;QACAU;MACA;MACA;MACAC;QACAQ;UAEAK;YACAZ;YACAa;YACA7C;cACA;gBACA8C;cACA;YACA;YACAX;cACAC;YACA;YACAC;UACA;QAEA;MACA;MACAG;MAAA,CACAC;QACArB;QAEAwB;UACAG;UACA/C;YACAgD;cACA;;cAEA;cACA;YAMA;UACA;UACAb;UACAE;QACA;MACA,GACAK;QACAtB;MACA;IACA;IACA6B;MAAA;;MAOA;MAEA;QAAA;QACAC;MACA;MAGA;IACA;IACAC;MACA;QACA;UACAC;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACAC;QACA;UACAxC;QACA;UACAA;QACA;MACA;MACA;QACAA;QACAwC;MACA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAC;MACA;QACAC;MAAA,GACAC,IACA;IACA;IAEAC;MAAA;MAAA;MACA;MACA;QACA;QACAC;QACAA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA3C;QACAU;QACAkC;QACAhE;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvaA;AAAA;AAAA;AAAA;AAA21C,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACA/2C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/components/ut-person-upload/up-person-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./up-person-upload.vue?vue&type=template&id=41044386&scoped=true&\"\nvar renderjs\nimport script from \"./up-person-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./up-person-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./up-person-upload.vue?vue&type=style&index=0&id=41044386&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"41044386\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/components/ut-person-upload/up-person-upload.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./up-person-upload.vue?vue&type=template&id=41044386&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.imageUrl ? _vm.showImg(_vm.imageUrl) : null\n  var m1 = _vm.imageUrl ? _vm.checkLoad(0) : null\n  var m2 = _vm.imageUrl && !m1 ? _vm.imageLoadingWidth() : null\n  var m3 = _vm.imageUrl && !m1 ? _vm.imageLoadingWidth() : null\n  var m4 = _vm.imageUrl\n    ? !_vm.disabled &&\n      _vm.remove &&\n      _vm.item.progress == 100 &&\n      _vm.checkLoad(0)\n    : null\n  var m5 = _vm.imageUrl && m4 ? _vm.checkLoad(0) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./up-person-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./up-person-upload.vue?vue&type=script&lang=js&\"", "<template>\n\t<!--\n\t\t@devicePosition: 摄像头位置\t前置或后置摄像头，值为front, bac\n\t\t@quality: 成像质量，值为high（高质量）、normal（普通质量）、low（低质量）\n\t-->\r\n\t<view class=\"ut-image-upload-list\">\r\n\t\t<view v-if=\"imageUrl\"  class=\"ut-image-upload-Item\"\r\n\t\t:style=\"{width:width+'rpx',height:height+'rpx',borderRadius:borderRadius+'rpx',paddingTop:showCamera?systemInfo.statusBarHeight+'px':0}\"\r\n\t\t>\r\n\t\t\t<view class=\"image-loading-box\" >\r\n\t\t\t\t<image :lazy-load=\"true\" class=\"image-real\" :src=\"showImg(imageUrl)\"\r\n\t\t\t\t\t@click=\"imgPreviewClick\" :style=\"{borderRadius:borderRadius+'rpx'}\" :mode=\"mode\"\r\n\t\t\t\t\t@load=\"imageLoad(0)\"></image>\r\n\t\t\t\t<view class=\"image-loading\" v-if=\"!checkLoad(0)\">\r\n\t\t\t\t\t<view class=\"loader\">\r\n\t\t\t\t\t\t<view class=\"loaders\">\r\n\t\t\t\t\t\t\t<view class=\"loader2\"\r\n\t\t\t\t\t\t\t\t:style=\"{width:imageLoadingWidth()+'rpx',height:imageLoadingWidth()+'rpx'}\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t加载中\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"ut-image-upload-progress-text\" v-if=\"item.progress!=100\" :style=\"{lineHeight:height+'rpx'}\">{{item.progress}}%</view>\r\n\t\t\t\t<view class=\"ut-image-upload-progress\" v-if=\"item.progress!=100\" @click=\"imgPreviewClick\" :style=\"{lineHeight:height+'rpx',opacity:1-item.progress/100}\"></view>\r\n\t\t\t\t<view class=\"ut-image-upload-Item-del\" :class=\"{'image-show':checkLoad(0)}\"\r\n\t\t\t\t\tv-if=\"!disabled && remove && item.progress==100 && checkLoad(0)\" @click=\"imgDel\">×</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-else class=\"ut-image-upload-Item ut-image-upload-Item-add\"\r\n\t\t\t:style=\"{width:width+'rpx',height:height+'rpx',borderRadius:borderRadius+'rpx',lineHeight:height+'rpx'}\"\r\n\t\t\t@click=\"chooseFile\">\r\n\t\t\t<text v-if=\"!$slots.default\">+</text>\r\n\t\t\t<slot name=\"default\" v-else></slot>\r\n\t\t</view>\r\n\t\t<view v-if=\"showCamera\">\r\n\t\t\t\r\n\t\t\t<view style=\"position: fixed;z-index: 1000;left: 40rpx;top: 20rpx;\" :style=\"{paddingTop:systemInfo.statusBarHeight+'px'}\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"u-flex left\" hover-class=\"left--hover\" hover-start-time=\"150\">\r\n\t\t\t\t    <view class=\"u-flex u-home-arrow-left\" :style=\"{borderColor:'rgba(255,255,255,.5)'}\">\r\n\t\t\t\t       <view @click=\"leftClick\">\r\n\t\t\t\t            <u-icon name=\"arrow-left\" size=\"20\" color=\"#fff\"></u-icon>\r\n\t\t\t\t        </view>\r\n\t\t\t\t    </view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\n\t\t\t<diy-person-camera\n\t\t\t\t:devicePosition=\"front\"\n\t\t\t\t:quality=\"quality\"\n\t\t\t\t@confirmPhoto=\"confirmPhoto\" />\r\n\t\t</view>\r\n\t</view>\n</template>\n\n<script>\n\timport DiyPersonCamera from '../ut-person-camera/ut-person-camera.vue';\n\texport default {\r\n\t\t\r\n\t\tcomponents: {\r\n\t\t    DiyPersonCamera\r\n\t\t},\r\n\t\tprops:{\r\n\t\t\tvalue: { //受控图片列表\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\tdisabled: { //是否禁用\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\tremove: { //是否展示删除按钮\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true,\r\n\t\t\t},\r\n\t\t\tname: { //发到后台的文件参数名\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'file',\r\n\t\t\t},\r\n\t\t\tfront:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\tquality: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'high'\r\n\t\t\t},\r\n\t\t\twidth: {\r\n\t\t\t\ttype: [Number,String],\r\n\t\t\t\tdefault: 120,\r\n\t\t\t},\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: [Number,String],\r\n\t\t\t\tdefault: 120,\r\n\t\t\t},\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'aspectFill',\r\n\t\t\t},\r\n\t\t\tborderRadius: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 8,\r\n\t\t\t},\r\n\t\t\taction: { //上传服务器\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '',\r\n\t\t\t},\r\n\t\t\theaders: { //上传的请求头部\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tformData: { //HTTP 请求中其他额外的 form data\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {},\r\n\t\t\t},\r\n\t\t\tpreviewImageWidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 750,\r\n\t\t\t},\r\n\t\t\tuploadSuccess: {\r\n\t\t\t\tdefault: (res) => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tsuccess: false,\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\r\n\t\t\t\tsystemInfo:{\r\n\t\t\t\t    statusBarHeight:uni.getSystemInfoSync().statusBarHeight,\r\n\t\t\t\t    // #ifdef MP-ALIPAY\r\n\t\t\t\t    navBarH: uni.getSystemInfoSync().statusBarHeight + uni.getSystemInfoSync().titleBarHeight, //菜单栏总高度--单位px\r\n\t\t\t\t    titleBarHeight: uni.getSystemInfoSync().titleBarHeight, //标题栏高度--单位px\r\n\t\t\t\t    // #endif\r\n\t\t\t\t    // #ifndef MP-ALIPAY\r\n\t\t\t\t    navBarH: uni.getSystemInfoSync().statusBarHeight + 44, //菜单栏总高度--单位px\r\n\t\t\t\t    titleBarHeight: 44, //标题栏高度--单位px\r\n\t\t\t\t    // #endif\r\n\t\t\t\t},\r\n\t\t\t\tshowCamera:false,\r\n\t\t\t\timageUrl:'',\r\n\t\t\t\tuploadLists: [],\r\n\t\t\t\timageLoaded: {},\r\n\t\t\t\titem:{},\n\t\t\t}\n\t\t}, \r\n\t\tcomputed:{\r\n\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue(val, oldVal) {\r\n\t\t\t\tthis.imageUrl=val\r\n\t\t\t\tthis.item={\r\n\t\t\t\t\tid: this.guid(),\r\n\t\t\t\t\turl: val,\r\n\t\t\t\t\tstate:6,\r\n\t\t\t\t\tprogress: 100,\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\r\n\t\t},\n\t\tmethods: {\r\n\t\t\timageLoadingWidth() {\r\n\t\t\t\tif (this.width > this.height) {\r\n\t\t\t\t\treturn this.height > 40 ? this.height / 3 : 40\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn this.width > 40 ? this.width / 3 : 40\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timgPreviewClick() {\r\n\t\t\t\tif (this.imageUrl) {\r\n\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\turls: [this.imageUrl],\r\n\t\t\t\t\t\tcurrent: 0,\r\n\t\t\t\t\t\tloop: true,\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timageLoad(index) {\r\n\t\t\t\tthis.$set(this.imageLoaded, 'l_' + index, true)\r\n\t\t\t},\r\n\t\t\tchooseFile(){\r\n\t\t\t\tthis.showCamera=true\r\n\t\t\t},\n\t\t\tconfirmPhoto(fileUrl){\r\n\t\t\t\tconsole.log('fff',fileUrl)\r\n\t\t\t\tthis.showCamera=false\r\n\t\t\t\tthis.imageUrl=fileUrl\r\n\t\t\t\tthis.imgUpload([fileUrl])\r\n\t\t\t\tthis.$emit('confirmPhoto')\n\t\t\t},\r\n\t\t\tleftClick(){\r\n\t\t\t\tthis.showCamera=false\r\n\t\t\t},\r\n\t\t\timgUpload(tempFilePaths, type) {\r\n\t\t\t\tif (this.action == 'uniCloud') {\r\n\t\t\t\t\tthis.uniCloudUpload(tempFilePaths, type)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '上传中'\r\n\t\t\t\t});\r\n\t\t\t\r\n\t\t\t\tlet that = this\t\t\t\t\r\n\t\t\t\ttempFilePaths.forEach(item=>{\r\n\t\t\t\t\tlet obj = {\r\n\t\t\t\t\t\tid: that.guid(),\r\n\t\t\t\t\t\tstate:0,\r\n\t\t\t\t\t\tprogress: 0,\r\n\t\t\t\t\t\turl: item,\r\n\t\t\t\t\t\tdata: []\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.item=obj\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\r\n\t\t\t\tlet uploadImgs = []\r\n\r\n\t\t\t\tconst promise = new Promise((resolve, reject) => {\r\n\t\t\t\t\tlet task = uni.uploadFile({\r\n\t\t\t\t\t\turl: this.action, //仅为示例，非真实的接口地址\r\n\t\t\t\t\t\tfilePath: this.item.url,\r\n\t\t\t\t\t\tname: this.name,\r\n\t\t\t\t\t\tfileType: 'image',\r\n\t\t\t\t\t\tformData: this.formData,\r\n\t\t\t\t\t\theader: this.headers,\r\n\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\tconst _res = JSON.parse(uploadFileRes.data);\r\n\t\t\t\t\t\t\tthis.$set(this.item, 'progress', 100)\r\n\t\t\t\t\t\t\tthis.$set(this.item, 'data', _res[0])\r\n\t\t\t\t\t\t\tthis.$set(this.item, 'state', 2)\r\n\t\t\t\t\t\t\tthis.$emit(\"uploadSuccess\", this.item)\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\tthis.$emit(\"uploadFail\", err);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tcomplete: () => {}\r\n\t\t\t\t\t})\r\n\t\t\t\t\ttask.id=this.item.id\r\n\t\t\t\t\ttask.onProgressUpdate((res) => {\r\n\t\t\t\t\t\tif(res.progress!=100)\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tthis.$set(this.item, 'progress', res.progress)\r\n\t\t\t\t\t\t\tthis.$set(this.item, 'state', 1)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tuploadImgs.push(promise)\r\n\t\t\t\t})\r\n\t\t\t\tPromise.all(uploadImgs) //执行所有需请求的接口\r\n\t\t\t\t\t.then((results) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((res, object) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$emit(\"uploadFail\", res);\r\n\t\t\t\t\t});\r\n\t\t\t\r\n\t\t\t},\r\n\t\t\tuniCloudUpload(tempFilePaths, type) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '上传中'\r\n\t\t\t\t});\r\n\t\t\t\tlet uploadImgs = [];\r\n\t\t\t\ttempFilePaths.forEach((item, index) => {\r\n\t\t\t\t\tuploadImgs.push(new Promise((resolve, reject) => {\r\n\t\t\t\r\n\t\t\t\t\t\tuniCloud.uploadFile({\r\n\t\t\t\t\t\t\tfilePath: item,\r\n\t\t\t\t\t\t\tcloudPath: this.guid() + '.' + this.getFileType(item, type),\r\n\t\t\t\t\t\t\tsuccess(uploadFileRes) {\r\n\t\t\t\t\t\t\t\tif (uploadFileRes.success) {\r\n\t\t\t\t\t\t\t\t\tresolve(uploadFileRes.fileID);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete() {}\r\n\t\t\t\t\t\t});\r\n\t\t\t\r\n\t\t\t\t\t}))\r\n\t\t\t\t})\r\n\t\t\t\tPromise.all(uploadImgs) //执行所有需请求的接口\r\n\t\t\t\t\t.then((results) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\r\n\t\t\t\t\t\tuniCloud.getTempFileURL({\r\n\t\t\t\t\t\t\tfileList: results,\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tres.fileList.forEach(item => {\r\n\t\t\t\t\t\t\t\t\t//this.value.push(item.tempFileURL)\r\n\t\t\t\t\t\t\t\t\t// #ifndef VUE3\r\n\t\t\t\t\t\t\t\t\tthis.value.push(item.tempFileURL)\r\n\t\t\t\t\t\t\t\t\tthis.$emit(\"input\", this.value);\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t// #ifdef VUE3\r\n\t\t\t\t\t\t\t\t\tthis.modelValue.push(item.tempFileURL)\r\n\t\t\t\t\t\t\t\t\tthis.$emit(\"update:modelValue\", this.modelValue);\r\n\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail() {},\r\n\t\t\t\t\t\t\tcomplete() {}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((res, object) => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetFileType(path, type) { //手机端默认图片为jpg 视频为mp4\r\n\t\t\t\t// #ifdef H5 \r\n\t\t\t\tvar result = type == 0 ? 'jpg' : 'mp4';\r\n\t\t\t\t// #endif\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tvar result = path.split('.').pop().toLowerCase();\r\n\t\t\t\t// #ifdef MP \r\n\t\t\t\tif (this.compress) { //微信小程序压缩完没有后缀\r\n\t\t\t\t\tresult = type == 0 ? 'jpg' : 'mp4';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn result;\r\n\t\t\t},\r\n\t\t\tguid() {\r\n\t\t\t\treturn 'xxxxxxxx-date-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n\t\t\t\t\tvar r = Math.random() * 16 | 0,\r\n\t\t\t\t\t\tv = c == 'x' ? r : (r & 0x3 | 0x8);\r\n\t\t\t\t\treturn v.toString(16);\r\n\t\t\t\t}).replace(/date/g, function(c) {\r\n\t\t\t\t\treturn Date.parse(new Date());\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 触发监听\r\n\t\t\t * @param {Number} key 当前任务的下标\r\n\t\t\t * @param {Object} uploadTask uni.uploadFile 的返回值\r\n\t\t\t */\r\n\t\t\ttriggerMonitor(key, uploadTask) {\r\n\t\t\t\tuploadTask.onProgressUpdate(res => {\r\n\t\t\t\t\t// 触发父组件/页面的监听事件\r\n\t\t\t\t\tthis.uploadTaskProgress[key] = res;\r\n\t\t\t\t\t// 合并所有任务的列表用于父组件/页面的监听\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t//\t// 当前的进度\r\n\t\t\t\t\t// \tprogress: 0,\r\n\t\t\t\t\t//\t// 当前的所有进度 保存每条任务的进度等等\r\n\t\t\t\t\t// \ttasks: []\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tthis.uploadTask.onProgressUpdate(this.mergerProgress(this.uploadTaskProgress));\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 合并进度\r\n\t\t\t * @param {Array} tasks 所有的任务\r\n\t\t\t */\r\n\t\t\tmergerProgress(tasks) {\r\n\t\t\t\tvar progress = 0;\r\n\t\t\t\ttasks.forEach((value, key) => {\r\n\t\t\t\t\tif (value) {\r\n\t\t\t\t\t\tprogress += value.progress;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tprogress += 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn {\r\n\t\t\t\t\tprogress: progress / tasks.length,\r\n\t\t\t\t\ttasks\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置父组件/页面的监听事件\r\n\t\t\t * onProgressBegin 开始监听触发事件\r\n\t\t\t * onProgressUpdate 进度变化事件\r\n\t\t\t * onProgressEnd 结束监听事件\r\n\t\t\t * [req 1,req 2...]\r\n\t\t\t * @param {Object} obj {onProgressUpdate,onProgressBegin,onProgressEnd}\r\n\t\t\t */\r\n\t\t\tsetUpMonitor(obj) {\r\n\t\t\t\tthis.uploadTask = {\r\n\t\t\t\t\tload: true,\r\n\t\t\t\t\t...obj\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tshowImg(url, size = 200, quality = 70) {\r\n\t\t\t\t// let bWidthSize=this.width>this.height?this.width*2:this.height*2\r\n\t\t\t\tif (url.indexOf('oss.') > 0) {\r\n\t\t\t\t\tlet str = url\r\n\t\t\t\t\tstr += \"&width=\" + this.width\r\n\t\t\t\t\tstr += \"&height=\" + this.height\r\n\t\t\t\t\tif (quality) str += \"&quality=\" + quality\r\n\t\t\t\t\treturn str\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn url\r\n\t\t\t\t}\r\n\t\t\t},\n\t\t\tcheckLoad(index) {\r\n\t\t\t\tif (this.imageLoaded['l_' + index]) return true\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\timgDel() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: '您确定要删除么?',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tthis.item={}\r\n\t\t\t\t\t\t\tthis.imageUrl=''\r\n\t\t\t\t\t\t\tthis.$emit(\"imgDelete\");\r\n\t\t\t\t\t\t} else if (res.cancel) {}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\r\n\t.u-home-arrow-left{\r\n\t    justify-content: space-between;\r\n\t    border-radius: 100rpx;\r\n\t    padding: 8rpx;\r\n\t    opacity: .8;\r\n\t    border: 1rpx solid #dadbde;\r\n\t}\r\n\t.ut-image-upload-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n.ut-image-upload-Item {\r\n\t/* \t\twidth: 160rpx;\r\n\theight: 160rpx; */\r\n\t/* border-radius: 8rpx; */\r\n\tmargin-right: 10rpx;\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\toverflow: hidden;\r\n}\r\n\r\n\t.image-loading-box {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: relative;\r\n\t}\r\n\t\r\n\t.ut-image-upload-Item-add {\r\n\t\tfont-size: 105rpx;\r\n\t\t/* line-height: 160rpx; */\r\n\t\ttext-align: center;\r\n\t\tborder: 1px dashed #d9d9d9;\r\n\t\tcolor: #d9d9d9;\r\n\t\t/* line-height: 160rpx; */\r\n\t}\r\n\t\r\n\t.image-loading {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\ttext-align: center;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t\r\n\t.image-loading .loader {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\t\r\n\t.loaders {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 16rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\t\r\n\t.loader2 {\r\n\t\tborder: 4rpx solid #f3f3f3;\r\n\t\tborder-radius: 50%;\r\n\t\tborder-top: 4rpx solid var(--colors);\r\n\t\r\n\t\tanimation: spin2 1s linear infinite;\r\n\t}\r\n\t\r\n\t@keyframes spin2 {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\t\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.ut-image-upload-Item-del {\r\n\t\tbackground-color: var(--colors);\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: absolute;\r\n\t\twidth: 35rpx;\r\n\t\theight: 35rpx;\r\n\t\tline-height: 35rpx;\r\n\t\ttext-align: center;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 9;\r\n\t\tcolor: #fff;\r\n\t\topacity: 0.7;\r\n\t\r\n\t}\r\n\t\r\n\t\r\n\t\r\n\t.ut-image-upload-Item-del-cover {\r\n\t\tbackground-color: var(--colors);\r\n\t\tfont-size: 24rpx;\r\n\t\tposition: absolute;\r\n\t\twidth: 35rpx;\r\n\t\theight: 35rpx;\r\n\t\ttext-align: center;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\tcolor: #fff;\r\n\t\t/* #ifdef APP-PLUS */\r\n\t\tline-height: 25rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-PLUS */\r\n\t\tline-height: 35rpx;\r\n\t\t/* #endif */\r\n\t\tz-index: 2;\r\n\t\topacity: 0.7;\r\n\t}\r\n\t\r\n\t.ut-image-upload-progress, .ut-image-upload-progress-text{\r\n\t\tposition: absolute;\r\n\t\tleft:0;\r\n\t\ttop: 0;\r\n\t\tcolor: var(--colors);\r\n\t\twidth: 100%;\r\n\t\ttext-align: center;\r\n\t\t\r\n\t}\r\n\t\r\n\t.ut-image-upload-progress{\r\n\t\tbackground-color: #fff;\r\n\t}\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./up-person-upload.vue?vue&type=style&index=0&id=41044386&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./up-person-upload.vue?vue&type=style&index=0&id=41044386&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360667231\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}