{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-list.vue?05ee", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-list.vue?3334", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-list.vue?c674", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-list.vue?e2af", "uni-app:///pagesA/components/proofread/proofread-list.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-list.vue?fed2", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-list.vue?b0b5"], "names": ["name", "props", "dataList", "type", "default", "checkPagePath", "required", "options", "styleIsolation", "methods", "getStatusText", "onItemClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA0sB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCqD9tB;EACAA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAG;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACA;QAAA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAAy1C,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA72C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/components/proofread/proofread-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./proofread-list.vue?vue&type=template&id=596459db&scoped=true&\"\nvar renderjs\nimport script from \"./proofread-list.vue?vue&type=script&lang=js&\"\nexport * from \"./proofread-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./proofread-list.vue?vue&type=style&index=0&id=596459db&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"596459db\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/components/proofread/proofread-list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-list.vue?vue&type=template&id=596459db&scoped=true&\"", "var components\ntry {\n  components = {\n    uLazyLoad: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-lazy-load/u-lazy-load\" */ \"@/components/uview-ui/components/u-lazy-load/u-lazy-load.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.dataList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.imgHead ? _vm.$tools.showImg(item.imgHead) : null\n    var m0 = _vm.getStatusText(item)\n    return {\n      $orig: $orig,\n      g0: g0,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-list.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <view v-for=\"(item, index) in dataList\" :key=\"index\" @click=\"onItemClick(item)\">\n            <view class=\"item-box\">\n                <view class=\"head-image\">\n                    <u-lazy-load v-if=\"item.imgHead\"\n                        :image=\"$tools.showImg(item.imgHead)\"\n                        width=\"120\"\n                        height=\"160\"\n                        border-radius=\"4\" />\n                    <text v-else class=\"cuIcon-people\"></text>\n                </view>\n                <view class=\"content text-content\">\n                    <view class=\"flex justify-between align-center\">\n                        <view>\n                            <view class=\"flex justify-start align-center flex-wrap\">\n                                <view class=\"text-df text-bold margin-right-xs\">{{ item.name }}</view>\n                                <view :class=\"{\n                                    'cu-tag sm radius light': true,\n                                    'bg-orange': item.proofreadError,\n                                    'bg-green': !item.proofreadError && item.isManual,\n                                    'bg-blue': !item.proofreadError && !item.isManual,\n                                }\">\n                                    {{ getStatusText(item) }}\n                                </view>\n                            </view>\n\n                            <view class=\"text-sm text-gray margin-top-xs\">\n                                手机号：{{ item.phone }}\n                            </view>\n                            <view class=\"text-sm text-gray margin-top-xs\">\n                                护理日期：{{ item.workDate }}\n                            </view>\n                        </view>\n                    </view>\n                    <view class=\"text-sm text-gray margin-top-xs\">\n                        护理员：{{ item.attendantName }}（{{ item.groupName }}）\n                    </view>\n                    <template v-if=\"item.isManual\">\n                        <view class=\"text-sm text-gray margin-top-xs\">\n                            校对人：{{ item.lastManualUserName }}\n                        </view>\n                        <view class=\"text-sm text-gray margin-top-xs\">\n                            校对时间：{{ item.lastManualTime }}\n                        </view>\n                    </template>\n                </view>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nexport default {\n    name: 'ProofreadList',\n    props: {\n        dataList: {\n            type: Array,\n            default: () => [],\n        },\n        checkPagePath: {\n            type: String,\n            required: true,\n        },\n    },\n    options: {\n        styleIsolation: 'shared',\n    },\n    methods: {\n        getStatusText(item) {\n            if (item.proofreadError) {\n                return item.proofreadErrorRemark ?? '异常'\n            }\n            return item.isManual ? '已校对' : '待校对'\n        },\n        onItemClick(item) {\n            this.$emit('item-click', item)\n        },\n    },\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.item-box {\n  padding: 20rpx 30rpx;\n  background-color: white;\n  margin-bottom: 10rpx;\n  display: flex;\n  position: relative;\n  overflow: hidden;\n\n  .content {\n    flex: 1;\n    padding: 0 20rpx;\n  }\n\n  &:active {\n    transform: scale(0.98);\n    transition: all 0.3s ease;\n  }\n}\n\n.cuIcon-people {\n  font-size: 116rpx;\n  color: gray;\n  border: 1rpx solid #ccc;\n  width: 116rpx;\n  height: 156rpx;\n  line-height: 156rpx;\n  border-radius: 6rpx;\n  display: block;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-list.vue?vue&type=style&index=0&id=596459db&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-list.vue?vue&type=style&index=0&id=596459db&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754361499080\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}