{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/project.vue?cc46", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/project.vue?3360", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/project.vue?f11e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/project.vue?6bad", "uni-app:///pagesA/care/scheduling/declare/project.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/project.vue?0896", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/project.vue?8aba"], "names": ["mixins", "components", "ProjectItem", "props", "projectData", "type", "default", "options", "styleIsolation", "data", "colors", "topWrapHeight", "upOption", "noMoreSize", "empty", "icon", "tip", "pageReq", "pagesize", "pageindex", "key", "firstLoad", "workId", "showProject", "checkboxValue1", "checked", "onShow", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "onLoad", "mounted", "methods", "getHeight", "downCallback", "handleSave"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACyL;AACzL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2VAEN;AACP,KAAK;AACL;AACA,aAAa,uTAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAktB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsCtuB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,gBAIA;EACAA;EAAA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;UACAC;UACAC;QACA;MACA;;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MAAAhB;IAAA;EACA;EACAiB,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IAAA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA,YACAC;MACA;MACA;MAEA;IACA;IAGAC;MAAA;MACA;MACA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;IACA;EAGA;AACA;AAAA,4B;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAA62C,CAAgB,4pCAAG,EAAC,C;;;;;;;;;;;ACAj4C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/care/scheduling/declare/project.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./project.vue?vue&type=template&id=44189233&scoped=true&\"\nvar renderjs\nimport script from \"./project.vue?vue&type=script&lang=js&\"\nexport * from \"./project.vue?vue&type=script&lang=js&\"\nimport style1 from \"./project.vue?vue&type=style&index=1&id=44189233&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"44189233\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/care/scheduling/declare/project.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=template&id=44189233&scoped=true&\"", "var components\ntry {\n  components = {\n    uCheckboxGroup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox-group/u-checkbox-group\" */ \"@/components/uview-ui/components/u-checkbox-group/u-checkbox-group.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/components/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.checkboxValue1.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=script&lang=js&\"", "<template>\n\t<view >\r\n\t\t<scroll-view scroll-y=\"true\" class=\"scroll-box\" @touchmove.stop.prevent=\"() => {}\" style=\"max-height: 60vh;\">\r\n\t\t\t<u-checkbox-group v-model=\"checkboxValue1\"  placement=\"column\">\r\n\t\t\t\t<template v-for=\"(item,index) in projectData\">\r\n\t\t\t\t\t<view class=\"flex justify-between padding-lr-xl padding-tb-sm\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view class=\"text-df text-bold\">\r\n\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t<text class=\"text-no text-sm text-gray\">(编号：{{item.govCode}})</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text-xs text-gray\">\r\n\t\t\t\t\t\t\t\t<text>{{item.projectTypeName}}</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.minDuration\" class=\"margin-left-lg\">{{item.minDuration}}--</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.maxDuration\">{{item.maxDuration }}</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.minDuration || item.maxDuration\">分钟</text>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<u-checkbox v-model=\"item.checked\" :activeColor=\"colors\" size=\"big\" :name=\"item.id\"></u-checkbox>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t</u-checkbox-group>\r\n\t\t</scroll-view>\r\n\t\t<view >\r\n\t\t\t<view class=\"margin\">\r\n\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"选择选中服务项目\" size=\"normal\" :disabled=\"!checkboxValue1.length\"  @click=\"handleSave\"></u-button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t\n\t</view>\n</template>\n\n<script>\nvar app = getApp()\nimport { mapState } from 'vuex'\r\nimport ProjectItem from '@/pagesA/components/project-item.vue'\n\nexport default {\n\tmixins: [], // 使用mixin\n\tcomponents: {\r\n\t\tProjectItem,\n\t},\r\n\tprops:{\r\n\t\tprojectData:{\r\n\t\t\ttype:Array,\r\n\t\t\tdefault:()=>[]\r\n\t\t},\r\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\n\t\t\ttopWrapHeight: 0,\n\t\t\tupOption: {\n\t\t\t\tnoMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5\n\t\t\t\tempty: {\n\t\t\t\t\ticon: require('@/pagesA/components/image/nodata.png'),\n\t\t\t\t\ttip: '~ 没有数据 ~', // 提示\n\t\t\t\t},\n\t\t\t},\n\t\t\tpageReq: {\n\t\t\t\tpagesize: 100,\n\t\t\t\tpageindex: 1,\n\t\t\t\tkey: '',\n\t\t\t},\r\n\t\t\tfirstLoad:true,\n\t\t\tworkId:'',\r\n\t\t\tshowProject:false,\r\n\t\t\tcheckboxValue1:[],\r\n\t\t\tchecked:false,\n\t\t}\n\t},\n\tonShow() {\n\t\tthis.setData({ colors: app.globalData.newColor })\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t}),\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\t},\r\n\tmounted() {\r\n\t\tthis.checkboxValue1=[]\r\n\t\tif(this.projectData && this.projectData.length){\r\n\t\t\tthis.projectData.forEach(item=>{\r\n\t\t\t\tif(item.checked) this.checkboxValue1.push(item.id)\r\n\t\t\t})\r\n\t\t}\r\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\n\t\t\tthis.topWrapHeight = h\n\t\t},\n\t\t/*下拉刷新的回调 */\n\t\tdownCallback() {\n\t\t\tthis.pageReq.pageindex = 1\n\t\t\tthis.mescroll.resetUpScroll()\r\n\t\t\t\r\n\t\t\tthis.$refs['swipeUserList'].closeAll()\n\t\t},\n\r\n\t\t\r\n\t\thandleSave(){\r\n\t\t\tif(!this.checkboxValue1 || !this.checkboxValue1.length)return\r\n\t\t\tlet arr=[]\r\n\t\t\tthis.checkboxValue1.forEach(projectId=>{\r\n\t\t\t\tlet obj=this.projectData.find(u=>u.id==projectId)\r\n\t\t\t\tif(obj) arr.push((obj))\r\n\t\t\t})\r\n\t\t\tthis.$emit('selectProject',arr)\r\n\t\t},\r\n\r\n\n\t},\n}\n</script>\n<style>\n\n\n</style>\n<style lang=\"scss\" scoped>\r\n\r\n.page {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n\r\n.pop-title {\r\n\tpadding-top: 20rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n/deep/.u-checkbox__icon-wrap{\r\n\twidth:48rpx;\r\n\theight:48rpx;\r\n\tspan{\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n}\r\n\r\n</style>\n", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=style&index=1&id=44189233&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./project.vue?vue&type=style&index=1&id=44189233&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360671243\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}