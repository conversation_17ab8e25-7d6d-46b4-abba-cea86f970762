{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/index.vue?2b98", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/index.vue?a27a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/index.vue?670e", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/index.vue?983f", "uni-app:///pagesA/care/scheduling/declare/index.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/index.vue?c02f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/care/scheduling/declare/index.vue?7829"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "MescrollBody", "options", "styleIsolation", "data", "colors", "topWrapHeight", "upOption", "noMoreSize", "empty", "icon", "tip", "pageReq", "pagesize", "pageindex", "key", "firstLoad", "dataList", "auditState", "showDeclareCancel", "selectItem", "onShow", "computed", "comm<PERSON>ey", "token", "userInfo", "community", "onLoad", "methods", "getHeight", "downCallback", "upCallback", "communityId", "setTimeout", "longpress", "console", "emptyClick", "jumpCustomer", "getActionOption", "text", "code", "style", "backgroundColor", "actionOp", "uni", "title", "content", "success", "auditOK", "ids", "resolve", "reject", "select", "declareId", "itemClick", "declareCancel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyL;AACzL,gBAAgB,iLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,qLAEN;AACP,KAAK;AACL;AACA,aAAa,yLAEN;AACP,KAAK;AACL;AACA,aAAa,+UAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,qSAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAgtB,CAAgB,moBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmIpuB;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAMA;EACAC;EAAA;EACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;UACAC;UACAC;QACA;MACA;;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EACAC;IACA;MAAAhB;IAAA;EACA;EACAiB,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CAEA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAC;IACAC;MACA;IACA;IACA,YACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAEA;kBACAC;gBAAA,GACA,eACA;kBAAA;kBACAC;oBACA;kBACA;kBACA;kBAEA;kBACA;kBACA;gBAEA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACAC;IACA;IACAC,mCACA;IACAC;MACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;UACAC;QACA;MACA;MACA;MAEA;QACAtC;MACA;MAEA;IACA;IACAuC;MAAA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;UACA;YACA;cACA;gBACA;kBAAA;gBAAA;gBACA;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACAhB;UACAiB;UACA/B;QACA;UACAgC;QACA;UACAC;QACA;MACA;IACA;IACAC;MACA;QAAAC;MAAA;IACA;IACAC;MACA;MACA;QACA;MACA;;MAEA;IACA;IACAC;MAAA;MACAX;QACAC;MACA;MACA;QACAb;QACAiB;MACA;QACA;QACA;MACA;QAAAL;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtSA;AAAA;AAAA;AAAA;AAA22C,CAAgB,0pCAAG,EAAC,C;;;;;;;;;;;ACA/3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesA/care/scheduling/declare/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesA/care/scheduling/declare/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=f8837828&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=f8837828&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f8837828\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesA/care/scheduling/declare/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=f8837828&scoped=true&\"", "var components\ntry {\n  components = {\n    utPage: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-page/ut-page\" */ \"@/components/ut/ut-page/ut-page.vue\"\n      )\n    },\n    utTop: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-top/ut-top\" */ \"@/components/ut/ut-top/ut-top.vue\"\n      )\n    },\n    fNavbar: function () {\n      return import(\n        /* webpackChunkName: \"components/f-navbar/f-navbar\" */ \"@/components/f-navbar/f-navbar.vue\"\n      )\n    },\n    uSwipeAction: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-swipe-action/u-swipe-action\" */ \"@/components/uview-ui/components/u-swipe-action/u-swipe-action.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-button/u-button\" */ \"@/components/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-popup/u-popup\" */ \"@/components/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    utLoginModal: function () {\n      return import(\n        /* webpackChunkName: \"components/ut/ut-login-modal/ut-login-modal\" */ \"@/components/ut/ut-login-modal/ut-login-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.dataList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 =\n      item.projectTotalMaxDuration >= item.schedulingDuration &&\n      item.projectTotalMinDuration <= item.schedulingDuration\n        ? item.projects.length\n        : null\n    var g1 = !(\n      item.projectTotalMaxDuration >= item.schedulingDuration &&\n      item.projectTotalMinDuration <= item.schedulingDuration\n    )\n      ? item.projects && item.projects.length\n      : null\n    var g2 =\n      !(\n        item.projectTotalMaxDuration >= item.schedulingDuration &&\n        item.projectTotalMinDuration <= item.schedulingDuration\n      ) && g1\n        ? item.projects.length\n        : null\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showDeclareCancel = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<ut-page>\n\t\t<ut-top class=\"top-warp\" bg-color=\"#fff\" @topHeight=\"getHeight\">\n\t\t\t<f-navbar fontColor=\"#fff\" :bgColor=\"colors\" title=\"打卡异常\" navbarType=\"2\"></f-navbar>\n\t\t</ut-top>\n\t\t<mescroll-body\r\n\t\t\tref=\"mescrollRef\"\n\t\t\t:top=\"topWrapHeight+'px'\"\n\t\t\t:top-margin=\"-topWrapHeight+'px'\"\n\t\t\tbottom=\"0\"\n\t\t\t:up=\"upOption\"\r\n\t\t\t:safearea=\"true\"\n\t\t\t@init=\"mescrollInit\"\n\t\t\t@down=\"downCallback\"\n\t\t\t@up=\"upCallback\"\n\t\t\t@emptyclick=\"emptyClick\"\n\t\t>\n\t\t\t<u-swipe-action ref=\"swipeUserList\">\n\t\t\t<template v-for=\"(item,index) in dataList\">\t\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content\" @click=\"itemClick(item)\">\r\n<!-- \t\t\t\t\t<u-swipe-action-item\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t:name=\"item.id\"\r\n\t\t\t\t\t\t:options=\"getActionOption(item)\"\r\n\t\t\t\t\t\t@longpress=\"longpress(item)\"\r\n\t\t\t\t\t\t@click=\"actionOp\"> -->\r\n<!-- \t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text>护理员：</text>\r\n\t\t\t\t\t\t<text class=\"text-df text-bold\">{{item.attendantName}}</text>\r\n\t\t\t\t\t\t<text class=\"margin-left-lg\">({{item.attendantPhone}})</text>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text>客户：</text>\r\n\t\t\t\t\t\t<text class=\"text-df text-bold\">{{item.name}}</text>\r\n\t\t\t\t\t\t<text v-if=\"item.sex && item.sex==1\" class=\"margin-left-xs\">男</text>\r\n\t\t\t\t\t\t<text v-else-if=\"item.sex && item.sex==2\" class=\"margin-left-xs\">女</text>\r\n\t\t\t\t\t\t<text class=\"margin-left-lg\">({{item.phone}})</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"item.idcard\">\r\n\t\t\t\t\t\t<text>证件：</text>\r\n\t\t\t\t\t\t<text class=\"\">{{item.idcard}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<text>排班计划：</text>\r\n\t\t\t\t\t\t<text class=\"text-df text-bold margin-left\">{{item.schedulingDate}}</text>\r\n\t\t\t\t\t\t<text class=\"text-df text-bold\">{{item.schedulingBegin}}</text>\r\n\t\t\t\t\t\t<text class=\"margin-lr-xs\">-</text>\r\n\t\t\t\t\t\t<text class=\"text-df text-bold\">{{item.schedulingEnd}}</text>\r\n\t\t\t\t\t\t<text class=\"margin-left text-xs\">(</text>\r\n\t\t\t\t\t\t<text class=\"text-xs\">{{item.schedulingDuration}}</text>\r\n\t\t\t\t\t\t<text class=\"text-xs\">分钟)</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"flex justify-between\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text>签到时间：</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.checkInTime\" :style=\"{color:colors}\">{{item.checkInTime}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else class=\"text-red\">未签到</text>\r\n\t\t\t\t\t\t\t</view>\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text>签退时间：</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.checkOutTime\" :style=\"{color:colors}\">{{item.checkOutTime}}</text>\r\n\t\t\t\t\t\t\t\t<text v-else class=\"text-red\">未签退</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"flex\">\r\n\t\t\t\t\t\t\t\t<text>服务项目：</text>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.projectTotalMaxDuration>=item.schedulingDuration && item.projectTotalMinDuration<=item.schedulingDuration\">\r\n\t\t\t\t\t\t\t\t\t<text :style=\"{color:colors}\">共{{item.projects.length}}项</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view v-else class=\"flex\">\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.projects && item.projects.length\" class=\"text-red\">\r\n\t\t\t\t\t\t\t\t\t\t<text>共{{item.projects.length}}项</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"margin-left-xs\">(时长不符)</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<text v-else class=\"text-red\">还未选择</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t<text>护理资料：</text>\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.uploadData\" :style=\"{color:colors}\">已上传</text>\r\n\t\t\t\t\t\t\t\t<text v-else class=\"text-red\">未上传护理资料</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"!item.isApplyError\">\r\n\t\t\t\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"异常申报\" size=\"mini\" @click=\"select(item)\"></u-button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view v-if=\"item.isApplyError\">\r\n\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t<text>审核状态：</text>\r\n\t\t\t\t\t\t\t<text v-if=\"item.applyErrorAuditState==0\">未审核</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.applyErrorAuditState==1\">审核通过</text>\r\n\t\t\t\t\t\t\t<text v-else-if=\"item.applyErrorAuditState==2\">审核未通过</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- </u-swipe-action-item> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t\n\t\t\t</template>\r\n\t\t\t\r\n\t\t\t</u-swipe-action>\n\t\t</mescroll-body>\r\n\t\t\r\n\t\t<u-popup :show=\"showDeclareCancel\" mode=\"bottom\" round=\"10\" :closeable=\"true\" :safe-area-inset-bottom=\"false\"\r\n\t\t\t\t :mask-close-able=\"true\" close-icon-pos=\"top-left\" :z-index=\"998\" :overlay-style=\"{zIndex:998}\" @close=\"showDeclareCancel=false\">\r\n\t\t\t<view class=\"pop-title\">审核情况</view>\r\n\t\t\t<view style=\"max-height: 75vh;min-height: 30vh;\" class=\"padding-sm\">\r\n\t\t\t\t<view class=\"text-lg text-center text-bold margin-top\">{{selectItem.name}}</view>\r\n\t\t\t\t<view v-if=\"selectItem.submitTime\" class=\"text-lg text-center\">提示时间:{{selectItem.submitTime}}</view>\r\n\t\t\t\t<view class=\"text-lg  text-center margin-top-xl\">\r\n\t\t\t\t\t<text>审核状态：</text>\r\n\t\t\t\t\t<text v-if=\"selectItem.applyErrorAuditState==0\">未审核</text>\r\n\t\t\t\t\t<text v-else-if=\"selectItem.applyErrorAuditState==1\">审核通过</text>\r\n\t\t\t\t\t<text v-else-if=\"selectItem.applyErrorAuditState==2\">审核未通过</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"text-lg text-center margin-top-xl padding-xl\">\r\n\t\t\t\t\t<u-button type=\"primary\" shape=\"circle\" :color=\"colors\" text=\"取消申报\"  @click=\"declareCancel\"></u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\n\t\t\n\t\t<ut-login-modal :colors=\"colors\"></ut-login-modal>\n\t</ut-page>\n</template>\n\n<script>\nvar app = getApp()\nimport { mapState } from 'vuex'\nimport MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'\nimport MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'\r\n\n\nexport default {\n\tmixins: [MescrollMixin], // 使用mixin\n\tcomponents: {\n\t\tMescrollBody,\n\t},\n\toptions: {\n\t\tstyleIsolation: 'shared',\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcolors: '',\r\n\t\t\ttopWrapHeight: 0,\r\n\t\t\tupOption: {\r\n\t\t\t\tnoMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5\r\n\t\t\t\tempty: {\r\n\t\t\t\t\ticon: require('@/pagesA/components/image/nodata.png'),\r\n\t\t\t\t\ttip: '~ 没有数据 ~', // 提示\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tpageReq: {\r\n\t\t\t\tpagesize: 20,\r\n\t\t\t\tpageindex: 1,\r\n\t\t\t\tkey: '',\r\n\t\t\t},\r\n\t\t\tfirstLoad:true,\r\n\t\t\tdataList:[],\r\n\t\t\tauditState:0,\t\t\t\r\n\t\t\t\r\n\t\t\tshowDeclareCancel:false,\r\n\t\t\tselectItem:{},\n\t\t}\n\t},\n\tonShow() {\n\t\tthis.setData({ colors: app.globalData.newColor })\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tcommKey: state => state.init.template.commKey,\n\t\t\ttoken: state => state.user.token,\n\t\t\tuserInfo: state => state.user.info,\n\t\t\tcommunity: state => state.init.community,\n\t\t}),\n\t},\n\tonLoad: async function (options) {\n\t\tawait this.$onLaunched\n\n\t},\n\tmethods: {\n\t\tgetHeight(h) {\n\t\t\tthis.topWrapHeight = h\n\t\t},\n\t\t/*下拉刷新的回调 */\n\t\tdownCallback() {\n\t\t\tthis.pageReq.pageindex = 1\n\t\t\tthis.mescroll.resetUpScroll()\n\t\t},\n\t\tasync upCallback(page) {\n\t\t\tthis.isShow = false\n\n\t\t\tthis.$ut.api('mang/care/customer/scheduling/declare/listpg', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\t...this.pageReq,\r\n\t\t\t}).then(({data}) => {\t\t\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.mescroll.endBySize(data.info.length, data.record)\r\n\t\t\t\t},this.firstLoad?0:500)\r\n\t\t\t\tthis.firstLoad=false\r\n\t\t\t\t\n\t\t\t\tif (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表\r\n\t\t\t\tthis.pageReq.pageindex++\n\t\t\t\tthis.dataList = this.dataList.concat(data.info)\t\n\n\t\t\t}).catch(e => {\n\t\t\t\tthis.pageReq.pageindex--\n\t\t\t\tthis.mescroll.endErr()\n\t\t\t})\n\t\t},\n\r\n\t\tlongpress(item) {\r\n\t\t\tconsole.log(item)\r\n\t\t},\n\t\temptyClick() {\n\t\t},\r\n\t\tjumpCustomer(){\t\t\t\r\n\t\t\tthis.$tools.routerTo('/pagesA/care/customer/index', {})\r\n\t\t},\r\n\t\tgetActionOption(item) {\r\n\t\t\tconst btnApplyCancel = {\r\n\t\t\t\ttext: '审核通过',\r\n\t\t\t\tcode:'auditOK',\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tbackgroundColor: '#ffaa7f'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tlet data = []\r\n\t\t\t\r\n\t\t\tif (item.auditState!=1) {\r\n\t\t\t\tdata.push(btnApplyCancel)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn data\r\n\t\t},\n\t\tactionOp(data) {\r\n\t\t\tlet content = ''\r\n\t\t\tif (data.code == 'auditOK') content = '确认审核通过吗？'\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '操作提示',\r\n\t\t\t\tcontent: content,\r\n\t\t\t\tsuccess: res=> {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tif (data.code == 'auditOK') {\r\n\t\t\t\t\t\t\tthis.auditOK(data.name).then(()=>{\r\n\t\t\t\t\t\t\t\tlet objIndex = this.dataList.findIndex(u => u.id == data.name)\r\n\t\t\t\t\t\t\t\tthis.dataList.splice(objIndex, 1)\r\n\t\t\t\t\t\t\t})\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$refs['swipeUserList'].closeAll()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tauditOK(id){\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tthis.$ut.api('mang/care/customer/scheduling/audit/audit', {\r\n\t\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\t\tids:[id],\r\n\t\t\t\t\tauditState:1,\r\n\t\t\t\t}).then(res=>{\r\n\t\t\t\t\tresolve(res)\r\n\t\t\t\t}).catch(err=>{\r\n\t\t\t\t\treject(err)\r\n\t\t\t\t})\r\n\t\t\t})\r\n\t\t},\r\n\t\tselect(item) {\r\n\t\t\tthis.$tools.routerTo('/pagesA/care/scheduling/declare/apply', { declareId: item.id })\r\n\t\t},\r\n\t\titemClick(item){\r\n\t\t\tthis.selectItem=item\r\n\t\t\tif(this.selectItem.applyErrorAuditState==0 && this.selectItem.isApplyError){\r\n\t\t\t\tthis.showDeclareCancel=true\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t//this.showDeclareCancel=true\r\n\t\t},\r\n\t\tdeclareCancel(){\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle:'请等待...'\r\n\t\t\t})\r\n\t\t\tthis.$ut.api('mang/care/customer/scheduling/declare/applyCancel', {\r\n\t\t\t\tcommunityId:this.community.id,\r\n\t\t\t\tids:[this.selectItem.id],\r\n\t\t\t}).then(()=>{\r\n\t\t\t\tthis.showDeclareCancel=false\r\n\t\t\t\tthis.downCallback()\r\n\t\t\t}).finally(()=>{uni.hideLoading()})\r\n\t\t}\n\t},\n}\n</script>\n<style>\n\n\n</style>\n<style lang=\"scss\" scoped>\r\n\r\n.tip{\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\twidth:100%;\r\n}\r\n\r\n.pop-title {\r\n\tpadding-top: 20rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n/deep/.u-checkbox__icon-wrap{\r\n\twidth:48rpx;\r\n\theight:48rpx;\r\n\tspan{\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n}\r\n\r\n</style>\n", "import mod from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=f8837828&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=f8837828&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360666549\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}