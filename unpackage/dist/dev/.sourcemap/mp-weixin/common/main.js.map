{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/common/push/h5_push.vue?2e84", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/common/push/h5_push.vue?9c2d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/common/push/h5_push.vue?9322", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/common/push/h5_push.vue?db5e", "uni-app:///common/push/h5_push.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/common/push/h5_push.vue?e995", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/common/push/h5_push.vue?4f7a", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/App.vue?af3f", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/App.vue?6edb", "uni-app:///App.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/App.vue?c5ab", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/App.vue?2a5d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "appPush", "mpShare", "require", "mixin", "prototype", "$ut", "http", "$store", "store", "$tools", "tools", "$shaken", "shaken", "$wxsdk", "wxsdk", "$platform", "platform", "$audio", "audio", "$onLaunched", "Promise", "resolve", "$isResolve", "config", "productionTip", "onShow", "methods", "setData", "obj", "callback", "that", "handleData", "tepData", "tepKey", "<PERSON><PERSON><PERSON>", "split", "for<PERSON>ach", "item", "undefined", "reg", "test", "isFn", "value", "Object", "keys", "key", "val", "replace", "front", "after", "index_after", "lastIndexOf", "slice", "$data", "defineProperty", "get", "set", "newValue", "enumerable", "configurable", "$set", "$nextTick", "App", "mpType", "app", "i18n", "$mount", "data", "show", "closeTime", "inApp", "voice", "vibration", "messageType", "messageTitle", "messageTime", "messageContent", "messageImage", "top", "left", "cur", "x", "y", "pageX", "pageY", "computed", "style", "created", "setTimeout", "touchstart", "console", "touch", "touchmove", "touchend", "nowColor", "globalData", "nfc", "userInfo", "header", "statusHeight", "toBar", "newColor", "onLaunch", "uni", "complete", "selectedColor", "tabList", "img", "index", "selected<PERSON><PERSON><PERSON><PERSON>", "fristColor", "id", "nickname", "phone", "headimg", "gender", "websocket", "comm<PERSON>ey", "realAuthUrl", "token", "joinRoom"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AACA;AAGA;AAIA;AAGA;AAA2C;AAAA;AAxB3C;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAiB1DC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;;AAEd;;AAEAF,YAAG,CAACC,GAAG,CAACE,aAAO,CAAC;AAKhB;AACA,IAAMC,OAAO,GAAGC,mBAAO,CAAC,sDAA6C,CAAC;AACtEL,YAAG,CAACM,KAAK,CAACF,OAAO,CAAC;AAGlBJ,YAAG,CAACO,SAAS,CAACC,GAAG,GAAGC,gBAAI;AACxBT,YAAG,CAACO,SAAS,CAACG,MAAM,GAAGC,cAAK;AAC5BX,YAAG,CAACO,SAAS,CAACK,MAAM,GAAGC,cAAK;AAC5Bb,YAAG,CAACO,SAAS,CAACO,OAAO,GAAGC,eAAM;AAC9Bf,YAAG,CAACO,SAAS,CAACS,MAAM,GAAGC,YAAK;AAC5BjB,YAAG,CAACO,SAAS,CAACW,SAAS,GAAGC,iBAAQ;AAClCnB,YAAG,CAACO,SAAS,CAACa,MAAM,GAAGC,cAAK;AAE5BrB,YAAG,CAACO,SAAS,CAACe,WAAW,GAAG,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;EAClDxB,YAAG,CAACO,SAAS,CAACkB,UAAU,GAAGD,OAAO;AACnC,CAAC,CAAC;;AAEF;AACA;;AAEAxB,YAAG,CAAC0B,MAAM,CAACC,aAAa,GAAG,KAAK;AAChC3B,YAAG,CAACM,KAAK,CAAC;EACTsB,MAAM,oBAAG,CAIT,CAAC;EACDC,OAAO,EAAE;IACRC,OAAO,EAAE,iBAASC,GAAG,EAAEC,QAAQ,EAAE;MAChC,IAAIC,IAAI,GAAG,IAAI;MACf,IAAMC,UAAU,GAAG,SAAbA,UAAU,CAAIC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAK;QACjDD,MAAM,GAAGA,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;QAC1BF,MAAM,CAACG,OAAO,CAAC,UAAAC,IAAI,EAAI;UACtB,IAAIL,OAAO,CAACK,IAAI,CAAC,KAAK,IAAI,IAAIL,OAAO,CAACK,IAAI,CAAC,KAAKC,SAAS,EAAE;YAC1D,IAAIC,GAAG,GAAG,UAAU;YACpBP,OAAO,CAACK,IAAI,CAAC,GAAGE,GAAG,CAACC,IAAI,CAACN,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC5CF,OAAO,GAAGA,OAAO,CAACK,IAAI,CAAC;UACxB,CAAC,MAAM;YACNL,OAAO,GAAGA,OAAO,CAACK,IAAI,CAAC;UACxB;QACD,CAAC,CAAC;QACF,OAAOL,OAAO;MACf,CAAC;MACD,IAAMS,IAAI,GAAG,SAAPA,IAAI,CAAYC,KAAK,EAAE;QAC5B,OAAO,OAAOA,KAAK,IAAI,UAAU,IAAI,KAAK;MAC3C,CAAC;MACDC,MAAM,CAACC,IAAI,CAAChB,GAAG,CAAC,CAACQ,OAAO,CAAC,UAASS,GAAG,EAAE;QACtC,IAAIC,GAAG,GAAGlB,GAAG,CAACiB,GAAG,CAAC;QAClBA,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;QAChD,IAAIC,KAAK,EAAEC,KAAK;QAChB,IAAIC,WAAW,GAAGL,GAAG,CAACM,WAAW,CAAC,GAAG,CAAC;QACtC,IAAID,WAAW,IAAI,CAAC,CAAC,EAAE;UACtBD,KAAK,GAAGJ,GAAG,CAACO,KAAK,CAACF,WAAW,GAAG,CAAC,CAAC;UAClCF,KAAK,GAAGjB,UAAU,CAACD,IAAI,EAAEe,GAAG,CAACO,KAAK,CAAC,CAAC,EAAEF,WAAW,CAAC,EAAED,KAAK,CAAC;QAC3D,CAAC,MAAM;UACNA,KAAK,GAAGJ,GAAG;UACXG,KAAK,GAAGlB,IAAI;QACb;QACA,IAAIkB,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACJ,KAAK,CAAC,KAAKX,SAAS,EAAE;UACpDK,MAAM,CAACW,cAAc,CAACN,KAAK,EAAEC,KAAK,EAAE;YACnCM,GAAG,iBAAG;cACL,OAAOP,KAAK,CAACK,KAAK,CAACJ,KAAK,CAAC;YAC1B,CAAC;YACDO,GAAG,eAACC,QAAQ,EAAE;cACbT,KAAK,CAACK,KAAK,CAACJ,KAAK,CAAC,GAAGQ,QAAQ;cAC7B;YACD,CAAC;;YACDC,UAAU,EAAE,IAAI;YAChBC,YAAY,EAAE;UACf,CAAC,CAAC;UACFX,KAAK,CAACC,KAAK,CAAC,GAAGH,GAAG;QACnB,CAAC,MAAM;UACNhB,IAAI,CAAC8B,IAAI,CAACZ,KAAK,EAAEC,KAAK,EAAEH,GAAG,CAAC;QAC7B;MACD,CAAC,CAAC;MACFL,IAAI,CAACZ,QAAQ,CAAC,IAAI,IAAI,CAACgC,SAAS,CAAChC,QAAQ,CAAC;IAC3C;EACD;AACD,CAAC,CAAC;AACFiC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAInE,YAAG;EAClBoE,IAAI,EAAJA,aAAI;EACJzD,KAAK,EAALA;AAAK,GACFsD,YAAG,EACL;AAQF,UAAAE,GAAG,EAACE,MAAM,EAAE,CAAC,CAAC,uB;;;;;;;;;;;;;ACxHd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACmL;AACnL,gBAAgB,iLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAorB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwBxsB;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MAEAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACAC;MACA;IACA;EACA;EACA9D;IACA+D;MACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACAD;IACA;IACAE;MACA;MACA;QACAD;MACA;QACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAT;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;IACAW;MACAH;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAD;MACA;MACA;MACA;MACA;QAAA;QACA;MACA;QAAA;QACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjIA;AAAA;AAAA;AAAA;AAAuzC,CAAgB,4pCAAG,EAAC,C;;;;;;;;;;;ACA30C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AAC6K;AAC7K,gBAAgB,iLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAkpB,CAAgB,ioBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACCtqB;AACA;AACA;AACA;AAKA;AAIA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;EACAI;AACA;AAAA,eAEA;EACA;;EAEAC;IACAC;IACAC;IACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IACA;EACA;;EACAC;IAAA;MAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAEAxE,aACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cAAA;cAAA,OAEAyE;gBAAA;gBACAC;kBACAd;kBACA5D;kBACA;oBACAtB;kBACA;oBACAA;kBACA;kBACAsB;kBAEAA;kBAYAA;gBACA;cACA;YAAA;cAEA;cACAP;cACA;gBACA;gBACAgF;kBACAE;gBACA;gBACAC;gBACA;kBACA;oBACAC;oBACAJ;sBACA;sBACAK;sBACAC;oBACA;kBACA;gBACA;cACA;gBACA;gBACAC;gBACAA;gBACAA;gBACA;gBACAP;kBACAE;gBACA;cACA;;cAEA;cACA;cAEA;gBACA;cAAA,CAWA;;cAGA;cACA;;cAEA;cAAA,KACA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;gBACA;cACA;YAAA;cAGA;cACA;cACA;gBACA3E;kBACA;oBACAiF;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;kBACArF;gBACA;cACA;cACA;cACA;gBACA;kBACA;oBACA;sBACAwC;sBAAA;sBACAC;sBAAA;sBACAC;sBAAA;sBACAC;sBACAC;sBACAE;sBACAC;oBACA;oBACAa;oBACA5D;oBACA;kBACA;oBACAA;oBACA;gBAAA;gBAGA4D;cACA;cACA;gBACA;kBACA;oBACA5D;oBACA;oBACA;gBAAA;cAEA;;cAEA;cACA;cACA;;cAEA;cACA;AACA;AACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA,CACA;IAAA;MAAA;IAAA;IAAA;EAAA;EACAuD,4BACA;IACAY;MAAA;IAAA;IACAmB;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GACA;EACA7F,yCACA;IACA8F;MACA;MACA1F;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC9NA;AAAA;AAAA;AAAA;AAAquC,CAAgB,goCAAG,EAAC,C;;;;;;;;;;;ACAzvC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue';\r\nimport App from './App';\r\nimport i18n from '@/common/i18n'\r\nimport http from '@/common/request'\r\nimport store from '@/common/store'\r\nimport platform from '@/common/platform'\r\nimport {\r\n\trouter,\r\n\tRouterMount\r\n} from '@/common/router'\r\nimport tools from '@/common/utils/tools.js'\r\n//微信sdk\r\nimport wxsdk from '@/common/wechat/sdk';\r\nimport shaken from '@/common/shaken'\r\n\r\n//uview\r\nimport uView from '@/components/uview-ui'\r\nVue.use(uView)\r\n\r\n//消息推送\r\nimport appPush from '@/common/push'\r\nVue.use(appPush)\r\n\r\nimport audio from '@/common/utils/audio.js'\r\n\r\n\r\n// 引入uView对小程序分享的mixin封装\r\nconst mpShare = require('@/components/uview-ui/libs/mixin/mpShare.js')\r\nVue.mixin(mpShare)\r\n\r\n\r\nVue.prototype.$ut = http\r\nVue.prototype.$store = store\r\nVue.prototype.$tools = tools\r\nVue.prototype.$shaken = shaken\r\nVue.prototype.$wxsdk = wxsdk\r\nVue.prototype.$platform = platform\r\nVue.prototype.$audio = audio\r\n\r\nVue.prototype.$onLaunched = new Promise(resolve => {\r\n\tVue.prototype.$isResolve = resolve\r\n})\r\n\r\n// import nodata from \"@/components/public/nodata\"\r\n// Vue.component(\"nodata\", nodata);\r\n\r\nVue.config.productionTip = false;\r\nVue.mixin({\r\n\tonShow() {\r\n\r\n\r\n\r\n\t},\r\n\tmethods: {\r\n\t\tsetData: function(obj, callback) {\r\n\t\t\tlet that = this;\r\n\t\t\tconst handleData = (tepData, tepKey, afterKey) => {\r\n\t\t\t\ttepKey = tepKey.split('.');\r\n\t\t\t\ttepKey.forEach(item => {\r\n\t\t\t\t\tif (tepData[item] === null || tepData[item] === undefined) {\r\n\t\t\t\t\t\tlet reg = /^[0-9]+$/;\r\n\t\t\t\t\t\ttepData[item] = reg.test(afterKey) ? [] : {};\r\n\t\t\t\t\t\ttepData = tepData[item];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\ttepData = tepData[item];\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\treturn tepData;\r\n\t\t\t};\r\n\t\t\tconst isFn = function(value) {\r\n\t\t\t\treturn typeof value == 'function' || false;\r\n\t\t\t};\r\n\t\t\tObject.keys(obj).forEach(function(key) {\r\n\t\t\t\tlet val = obj[key];\r\n\t\t\t\tkey = key.replace(/\\]/g, '').replace(/\\[/g, '.');\r\n\t\t\t\tlet front, after;\r\n\t\t\t\tlet index_after = key.lastIndexOf('.');\r\n\t\t\t\tif (index_after != -1) {\r\n\t\t\t\t\tafter = key.slice(index_after + 1);\r\n\t\t\t\t\tfront = handleData(that, key.slice(0, index_after), after);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tafter = key;\r\n\t\t\t\t\tfront = that;\r\n\t\t\t\t}\r\n\t\t\t\tif (front.$data && front.$data[after] === undefined) {\r\n\t\t\t\t\tObject.defineProperty(front, after, {\r\n\t\t\t\t\t\tget() {\r\n\t\t\t\t\t\t\treturn front.$data[after];\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tset(newValue) {\r\n\t\t\t\t\t\t\tfront.$data[after] = newValue;\r\n\t\t\t\t\t\t\t// that.$forceUpdate();\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tenumerable: true,\r\n\t\t\t\t\t\tconfigurable: true\r\n\t\t\t\t\t});\r\n\t\t\t\t\tfront[after] = val;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.$set(front, after, val);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tisFn(callback) && this.$nextTick(callback);\r\n\t\t}\r\n\t}\r\n});\r\nApp.mpType = 'app';\r\nconst app = new Vue({\r\n\ti18n,\r\n\tstore,\r\n\t...App\r\n});\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\napp.$mount(); //为了兼容小程序及app端必须这样写才有效果", "import { render, staticRenderFns, recyclableRender, components } from \"./h5_push.vue?vue&type=template&id=067d5f5d&scoped=true&\"\nvar renderjs\nimport script from \"./h5_push.vue?vue&type=script&lang=js&\"\nexport * from \"./h5_push.vue?vue&type=script&lang=js&\"\nimport style0 from \"./h5_push.vue?vue&type=style&index=0&id=067d5f5d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"067d5f5d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"common/push/h5_push.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./h5_push.vue?vue&type=template&id=067d5f5d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./h5_push.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./h5_push.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<transition name=\"slide-fade\">\r\n\t\t\t<view class=\"h5_push\" @touchstart.stop.prevent=\"touchstart\" @touchmove.stop.prevent=\"touchmove\" @touchend.stop.prevent=\"touchend\" v-if=\"show\" :style=\"style\">\r\n\t\t\t\t<view class=\"push-title\">\r\n\t\t\t\t\t<view class=\"push-type\">\r\n\t\t\t\t\t\t<image src=\"/static/push/message-icon.png\"></image>\r\n\t\t\t\t\t\t{{messageType}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t{{messageTime}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"push-body\">\r\n\t\t\t\t\t<view class=\"push-content\">\r\n\t\t\t\t\t\t<view class=\"push-content-title\">{{messageTitle}}</view>\r\n\t\t\t\t\t\t<view class=\"push-content-text\">{{messageContent}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image :src=\"messageImage\" class=\"push-img\" mode=\"aspectFill\" v-if=\"messageImage\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</transition>\r\n\t</view>\r\n</template>\n\n<script>\r\n\texport default{\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: false,\r\n\t\t\t\t// 关闭时间\r\n\t\t\t\tcloseTime: 3000,\r\n\t\t\t\t// app内横幅提醒\r\n\t\t\t\tinApp: false,\r\n\t\t\t\t// 声音提醒\r\n\t\t\t\tvoice: true,\r\n\t\t\t\t// 振动提醒\r\n\t\t\t\tvibration: false,\r\n\t\t\t\t// 消息分类\r\n\t\t\t\tmessageType: '',\r\n\t\t\t\t// 通知标题\r\n\t\t\t\tmessageTitle: '',\r\n\t\t\t\t// 时间\r\n\t\t\t\tmessageTime: '现在',\r\n\t\t\t\t// 通知文案\r\n\t\t\t\tmessageContent: '',\r\n\t\t\t\t// 缩略图\r\n\t\t\t\tmessageImage: '',\r\n\t\t\t\t\r\n\t\t\t\ttop: 20,\r\n\t\t\t\tleft: 20,\r\n\t\t\t\tcur: {\r\n\t\t\t\t\tx: 0,\r\n\t\t\t\t\ty: 0,\r\n\t\t\t\t\tpageX: 0,\r\n\t\t\t\t\tpageY: 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed:{\r\n\t\t\tstyle() {\r\n\t\t\t\tlet system = uni.getSystemInfoSync()\r\n\t\t\t\tlet statusBarHeight = system.statusBarHeight\r\n\t\t\t\treturn `top: calc(${statusBarHeight}px + ${this.top}rpx);left: ${this.left}rpx`\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.show = false\r\n\t\t\t}, this.closeTime)\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\ttouchstart(event) {\r\n\t\t\t\tconsole.log(event)\r\n\t\t\t\tvar touch;  \r\n\t\t\t\tif (event.touches) {\r\n\t\t\t\t\ttouch = event.touches[0];  \r\n\t\t\t\t} else {  \r\n\t\t\t\t\ttouch = event;  \r\n\t\t\t\t}  \r\n\t\t\t\tthis.cur.x = touch.clientX;  \r\n\t\t\t\tthis.cur.y = touch.clientY;\r\n\t\t\t\tthis.cur.pageX = touch.pageX;\r\n\t\t\t\tthis.cur.pageY = touch.pageY;  \r\n\t\t\t\tconsole.log(this.cur)\r\n\t\t\t},\r\n\t\t\ttouchmove(event) {\r\n\t\t\t\tvar touch;\r\n\t\t\t\tif (event.touches) {  \r\n\t\t\t\t\ttouch = event.touches[0];  \r\n\t\t\t\t} else {  \r\n\t\t\t\t\ttouch = event;\r\n\t\t\t\t}  \r\n\t\t\t\tlet moveX = touch.pageX - this.cur.x;   \r\n\t\t\t\tlet moveY = touch.pageY - this.cur.y;   \r\n\t\t\t\tlet x = moveX;  \r\n\t\t\t\tlet y = moveY;\r\n\t\t\t\t// console.log(x, y)\r\n\t\t\t\t// console.log(this.cur, touch)\r\n\t\t\t\tlet system = uni.getSystemInfoSync()\r\n\t\t\t\tif (y >= (uni.upx2px(20) + system.statusBarHeight)) {  \r\n\t\t\t\t\ty = (uni.upx2px(20) + system.statusBarHeight);  \r\n\t\t\t\t}\r\n\t\t\t\tthis.top = y / (uni.upx2px(y) / y)\r\n\t\t\t\tthis.left = x / (uni.upx2px(x) / x)\r\n\t\t\t\t\r\n\t\t\t\t// this.body.setStyle({  \r\n\t\t\t\t// \ttop: y + 'px',  \r\n\t\t\t\t// \tleft: x + 'px'  \r\n\t\t\t\t// });\r\n\t\t\t},\r\n\t\t\ttouchend(event) {\r\n\t\t\t\tconsole.log(event)\r\n\t\t\t\tvar touch;\r\n\t\t\t\tif (event.touches.length) {  \r\n\t\t\t\t\ttouch = event.touches[0];  \r\n\t\t\t\t} else {  \r\n\t\t\t\t\ttouch = event.changedTouches[0];  \r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(this.cur, touch)\r\n\t\t\t\tlet differX = Math.abs(this.cur.pageX) - Math.abs(touch.pageX)\r\n\t\t\t\tlet differY = Math.abs(this.cur.pageY) - Math.abs(touch.pageY)\r\n\t\t\t\t// console.log(differX, differY)\r\n\t\t\t\tif(Math.abs(differX) > 5 || Math.abs(differY) > 5) {\t// 上下移动或左右移动超过5px则关闭弹窗\r\n\t\t\t\t\tthis.show = false\r\n\t\t\t\t} else {\t// 否则当作单击事件\r\n\t\t\t\t\tconsole.log('-------------------')\r\n\t\t\t\t\tthis.show = false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\r\n\t.h5_push{\r\n\t\twidth: 710rpx;\r\n\t\theight: 192rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0px 3rpx 18rpx 0px rgba(54, 58, 68, 0.08);\r\n\t\tborder-radius: 20rpx;\r\n\t\tposition: fixed;\r\n\t\tz-index: 9999999;\r\n\t\t.push-title{\r\n\t\t\tpadding: 30rpx 30rpx 15rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #4F555B;\r\n\t\t\t.push-type{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #4F555B;\r\n\t\t\t\timage{\r\n\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.push-body{\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 0 24rpx 0 30rpx;\r\n\t\t\t.push-content{\r\n\t\t\t\twidth: calc(100% - 150rpx);\r\n\t\t\t\t.push-content-title{\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #202123;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\t\t\t\t.push-content-text{\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #4F555B;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.push-img{\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tbackground: #F2F2F3;\r\n\t\t\t\tborder: 0.5px solid #E9E9E9;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\tmargin-left: 50rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.slide-fade-enter-active {\r\n\t  transition: all .3s ease;\r\n\t}\r\n\t.slide-fade-leave-active {\r\n\t  transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);\r\n\t}\r\n\t.slide-fade-enter, .slide-fade-leave-to\r\n\t/* .slide-fade-leave-active for below version 2.1.8 */ {\r\n\t  transform: translateX(10px);\r\n\t  opacity: 0;\r\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./h5_push.vue?vue&type=style&index=0&id=067d5f5d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./h5_push.vue?vue&type=style&index=0&id=067d5f5d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673698\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport request from '@/common/request'\r\n\timport store from '@/common/store/index.js'\r\n\timport platform from '@/common/platform'\r\n\timport {\r\n\t\tmapMutations,\r\n\t\tmapActions,\r\n\t\tmapState\r\n\t} from 'vuex';\r\n\timport {\r\n\t\tsetConfig,\r\n\t\tgetConfig\r\n\t} from '@/common/store/modules/initInfo.js'\r\n\timport {COMMKEY,INICOLOR} from '@/config'\r\n\t// import myconfig from \"./common/thems/index.js\";\r\n\t// import pageAnimation from './components/page-animation'\r\n\tlet colors = getConfig();\r\n\tlet nowColor = ''\r\n\tif (colors) {\r\n\t\tnowColor = colors.color\r\n\t}\r\n\r\n\texport default {\r\n\t\t// mixins: [notify],\r\n\t\t\r\n\t\tglobalData: {\r\n\t\t\tnfc:null,\r\n\t\t\tuserInfo: null,\r\n\t\t\theader:64,\r\n\t\t\tstatusHeight: 0, //状态栏高度\r\n\t\t\ttoBar: 44, //标题栏高度\r\n\t\t\tnewColor: nowColor || INICOLOR, //小程序主题颜色\r\n\t\t\t// config: myconfig.themeList //主题图标\r\n\t\t},\r\n\t\tonLaunch: async function() {\r\n\t\t\t\r\n\t\t\tlet that = this\r\n\t\t\t// // #ifdef MP-WEIXIN\r\n\t\t\t// uni.getSetting({\r\n\t\t\t// \tsuccess: res => {\r\n\t\t\t// \t\tif (res.authSetting['scope.userInfo']) {\r\n\t\t\t// \t\t\t// 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框\r\n\t\t\t// \t\t\tuni.getUserInfo({\r\n\t\t\t// \t\t\t\tsuccess: res => {\r\n\t\t\t// \t\t\t\t\t// 可以将 res 发送给后台解码出 unionId\r\n\t\t\t// \t\t\t\t\tthis.globalData.userInfo = res.userInfo; // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回\r\n\t\t\t// \t\t\t\t\t// 所以此处加入 callback 以防止这种情况\r\n\t\t\t// \t\t\t\t\tif (this.userInfoReadyCallback) {\r\n\t\t\t// \t\t\t\t\t\tthis.userInfoReadyCallback(res);\r\n\t\t\t// \t\t\t\t\t}\r\n\t\t\t// \t\t\t\t}\r\n\t\t\t// \t\t\t});\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\t\t\t// });\r\n\t\t\t// // #endif\r\n\r\n\t\t\tawait uni.getSystemInfo({ // 获取手机状态栏高度\r\n\t\t\t\tcomplete: res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthat.globalData.statusHeight = res.statusBarHeight;\r\n\t\t\t\t\tif (res.osName === \"ios\") {\r\n\t\t\t\t\t\tstore.commit('statusHeight', res.statusBarHeight);\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tstore.commit('statusHeight', res.statusBarHeight+2);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.globalData.toBar = 44;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthat.globalData.statusHeight=5\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\tvar ua = window.navigator.userAgent.toLowerCase();\r\n\r\n\t\t\t\t\tif (ua.match(/micromessenger/i) == 'micromessenger') {\r\n\t\t\t\t\t\tif (res.osName === \"ios\") {\r\n\t\t\t\t\t\t\tthat.globalData.toBar = 48;\r\n\t\t\t\t\t\t} else if (res.osName === \"android\") {\r\n\t\t\t\t\t\t\tthat.globalData.toBar = 46;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthat.globalData.header =parseInt(that.globalData.statusHeight)+parseInt(that.globalData.toBar)\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\t// 设置默认主题颜色\r\n\t\t\tlet config = getConfig();\r\n\t\t\tif (config && config.color !== '') {\r\n\t\t\t\t//如果存在设置的主题 使用设置的主题\r\n\t\t\t\tuni.setTabBarStyle({\r\n\t\t\t\t\tselectedColor: config.color\r\n\t\t\t\t});\r\n\t\t\t\tlet tabList = config.tabList;\r\n\t\t\t\tif (config.tabList) {\r\n\t\t\t\t\tfor (var i = 0; i < tabList.length; i++) {\r\n\t\t\t\t\t\tlet img = tabList[i];\r\n\t\t\t\t\t\tuni.setTabBarItem({\r\n\t\t\t\t\t\t\t//设置tabBar 首页图标\r\n\t\t\t\t\t\t\tindex: i,\r\n\t\t\t\t\t\t\tselectedIconPath: img\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t//如果不存在主题 使用默认主题\r\n\t\t\t\tlet fristColor = {};\r\n\t\t\t\tfristColor.color = INICOLOR;\r\n\t\t\t\tfristColor.name = \"olive\";\r\n\t\t\t\tsetConfig(fristColor);\r\n\t\t\t\tuni.setTabBarStyle({\r\n\t\t\t\t\tselectedColor: INICOLOR\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t//设置公共key(很重要！！！)\r\n\t\t\tthis.setCommKey(COMMKEY)\r\n\t\t\t\r\n\t\t\tthis.getAppInfo().then(r=>{\r\n\t\t\t\t//加载微信浏览器配置\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif(platform.get() === 'wxOfficialAccount') {\r\n\t\t\t\t\t\r\n\t\t\t\t\t//加载微信相关\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.getWeChat()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.$wxsdk.initJssdk(this.commKey, this.realAuthUrl)\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t//加载OSS地址\r\n\t\t\tthis.getUpload()\r\n\t\t\t\r\n\t\t\t//加载我的信息\r\n\t\t\tif(this.token){\r\n\t\t\t\tawait this.getMyInfo().catch(()=>{\r\n\t\t\t\t\tthis.$isResolve()\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t//ws通信准备\r\n\t\t\tthis.socketInit('/ws/ChatHub')\r\n\t\t\tif (that.userInfo) {\r\n\t\t\t\tthat.websocket.on('connected', function(res) {\r\n\t\t\t\t\tlet param = {\r\n\t\t\t\t\t\tid: that.userInfo.id,\r\n\t\t\t\t\t\tnickname: that.userInfo.nickname,\r\n\t\t\t\t\t\tphone: that.userInfo.phone,\r\n\t\t\t\t\t\theadimg: that.userInfo.headimgurl,\r\n\t\t\t\t\t\tgender: that.userInfo.gender,\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.websocket.invoke('setMyInfo', param)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t//接收信息绑定\r\n\t\t\tthis.websocket.on('message', function(res) {\r\n\t\t\t\tswitch (res.type) {\r\n\t\t\t\t\tcase \"notify\":\r\n\t\t\t\t\t\tlet params = {\r\n\t\t\t\t\t\t\tinApp: false, // app内横幅提醒\r\n\t\t\t\t\t\t\tvoice: true, // 声音提醒\r\n\t\t\t\t\t\t\tvibration: true, // 振动提醒\r\n\t\t\t\t\t\t\tmessageType: res.context.type,\r\n\t\t\t\t\t\t\tmessageTitle: res.context.title,\r\n\t\t\t\t\t\t\tmessageContent: res.context.content,\r\n\t\t\t\t\t\t\tmessageImage: res.context.image\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconsole.log(that.$appPush)\r\n\t\t\t\t\t\tthat.$appPush(params)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"notify_audio\":\r\n\t\t\t\t\t\tthat.$audio.play(res.context.url)\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t});\r\n\t\t\tthis.websocket.on('logMessage', function(res) {\r\n\t\t\t\tswitch (res.type) {\r\n\t\t\t\t\tcase 'setMyInfo':\r\n\t\t\t\t\t\tthat.websocket.invoke('joinRoom', 'room')\r\n\t\t\t\t\t\t// that.joinRoom()\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// await this.getTemplate().then().catch(res=>{\r\n\t\t\t// \tconsole.log(res)\r\n\t\t\t// })\r\n\r\n\t\t\tthis.$isResolve()\r\n\t\t\t/**\r\n\t\t\t * 模拟获取购物车的数量 getCart\r\n\t\t\t */\r\n\t\t\t// let cart = getCart()\r\n\t\t\t// let length = ''\r\n\t\t\t// if (cart && getToken()) {\r\n\t\t\t// \tlength = cart.length\r\n\t\t\t// \tuni.setTabBarBadge({\r\n\t\t\t// \t\t//给tabBar添加角标\r\n\t\t\t// \t\tindex: 2,\r\n\t\t\t// \t\ttext: String(length)\r\n\t\t\t// \t});\r\n\t\t\t// }\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState({\r\n\t\t\t\tuserInfo: state => state.user.info,\r\n\t\t\t\twebsocket: state => state.websocket.socketTask,\r\n\t\t\t\tcommKey: state => state.init.template.commKey,\r\n\t\t\t\trealAuthUrl: state => state.init.realAuthUrl,\r\n\t\t\t\ttoken: state => state.user.token,\r\n\t\t\t}),\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['getTemplate','getAppInfo', 'getMyInfo', 'setCommKey', 'getWeChat', 'socketInit', 'setOss','getUpload']),\r\n\t\t\tjoinRoom() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.websocket.invoke('joinRoom', 'room')\r\n\t\t\t},\r\n\t\t}\r\n\t};\r\n\r\n\r\n\t// #ifdef H5\r\n\t//设置公众号的字段不受微信影响\r\n\t(function() {\r\n\t\tif (typeof WeixinJSBridge == \"object\" && typeof WeixinJSBridge.invoke == \"function\") {\r\n\t\t\thandleFontSize();\r\n\t\t} else {\r\n\t\t\tif (document.addEventListener) {\r\n\t\t\t\tdocument.addEventListener(\"WeixinJSBridgeReady\", handleFontSize, false);\r\n\t\t\t} else if (document.attachEvent) {\r\n\t\t\t\tdocument.attachEvent(\"WeixinJSBridgeReady\", handleFontSize);\r\n\t\t\t\tdocument.attachEvent(\"onWeixinJSBridgeReady\", handleFontSize);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tfunction handleFontSize() {\r\n\t\t\t// 设置网页字体为默认大小\r\n\t\t\tWeixinJSBridge.invoke('setFontSizeCallback', {\r\n\t\t\t\t'fontSize': 0\r\n\t\t\t});\r\n\t\t\t// 重写设置网页字体大小的事件\r\n\t\t\tWeixinJSBridge.on('menu:setfont', function() {\r\n\t\t\t\tWeixinJSBridge.invoke('setFontSizeCallback', {\r\n\t\t\t\t\t'fontSize': 0\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\t})();\r\n\t// #endif\r\n</script>\r\n<style lang=\"scss\">\r\n\t//不要使用scope，整个程序尽可能使用公共样式\r\n\t@import \"@/components/uview-ui/index.scss\";\r\n\t@import \"@/common/css/app.scss\";\r\n\t@import \"@/common/css/ut.scss\";\r\n\tbody {\r\n\t\t/* IOS禁止微信调整字体大小 */\r\n\t\t-webkit-text-size-adjust: 100% !important;\r\n\t\ttext-size-adjust: 100% !important;\r\n\t\t-moz-text-size-adjust: 100% !important;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673891\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}