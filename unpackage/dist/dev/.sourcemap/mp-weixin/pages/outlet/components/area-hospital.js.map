{"version": 3, "sources": ["webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pages/outlet/components/area-hospital.vue?2a93", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pages/outlet/components/area-hospital.vue?bd64", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pages/outlet/components/area-hospital.vue?b47d", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pages/outlet/components/area-hospital.vue?50c2", "uni-app:///pages/outlet/components/area-hospital.vue", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pages/outlet/components/area-hospital.vue?7254", "webpack:///D:/svn/ynyt/源代码/前端框架/Project/先施康养/pages/outlet/components/area-hospital.vue?d4a0"], "names": ["options", "styleIsolation", "props", "colors", "type", "title", "my", "default", "hot", "list", "province", "data", "isLoading", "keyword", "isSearch", "focus", "showCity", "provinceIndex", "computed", "city", "locationAddress", "cityData", "watch", "deep", "handler", "methods", "scan", "result", "inputClick", "overlayClick", "areaClick", "provinceClick", "cityClick", "communityClick", "setTimeout", "searchClick", "key", "getLocation"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsL;AACtL,gBAAgB,iLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mUAEN;AACP,KAAK;AACL;AACA,aAAa,+RAEN;AACP,KAAK;AACL;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,aAAa,6TAEN;AACP,KAAK;AACL;AACA,aAAa,iTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAysB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACsJ7tB;AAIA;AAAA;AAAA,gBACA;EACAA;IAAA;IACAC;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACAD;IACA;IACA;IACA;IACA;IACA;IACAE;MACAF;MACAG;QAAA;MAAA;IACA;IACAC;MACAJ;MACAG;QAAA;MAAA;IACA;IACAE;MACAL;MACAG;QAAA;MAAA;IACA;IACAG;MACAN;MACAG;QAAA;MAAA;IACA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;IAEA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACAd;MACAe;MACAC;QACA;MACA;IACA;IACAf;MACAc;MACAC;QACA;MACA;IACA;EACA;EACAC,yCACA;IACAC;MAAA;MACA;QACA;UACA;YACAC;YACAvB;UACA;QACA;MACA;IACA;IACAwB;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MAEA;MACA;QACA;QAEA;MACA;QACA;QACA;MACA;IAEA;IACAC;MACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QAEA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAC;QACAjB;MACA;IACA;IACAkB;MAAA;MACA;MACA;QACA;QACA;QACA;MACA;QAEA;MACA;IACA;EAAA;AAEA;AAAA,4B;;;;;;;;;;;;AC1TA;AAAA;AAAA;AAAA;AAAw1C,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACA52C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/outlet/components/area-hospital.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./area-hospital.vue?vue&type=template&id=732f4d1a&scoped=true&\"\nvar renderjs\nimport script from \"./area-hospital.vue?vue&type=script&lang=js&\"\nexport * from \"./area-hospital.vue?vue&type=script&lang=js&\"\nimport style0 from \"./area-hospital.vue?vue&type=style&index=0&id=732f4d1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"732f4d1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/outlet/components/area-hospital.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./area-hospital.vue?vue&type=template&id=732f4d1a&scoped=true&\"", "var components\ntry {\n  components = {\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-transition/u-transition\" */ \"@/components/uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-icon/u-icon\" */ \"@/components/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u--input/u--input\" */ \"@/components/uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uLazyLoad: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-lazy-load/u-lazy-load\" */ \"@/components/uview-ui/components/u-lazy-load/u-lazy-load.vue\"\n      )\n    },\n    uOverlay: function () {\n      return import(\n        /* webpackChunkName: \"components/uview-ui/components/u-overlay/u-overlay\" */ \"@/components/uview-ui/components/u-overlay/u-overlay.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.showCity && _vm.focus && !_vm.isSearch ? _vm.hot.length : null\n  var l0 =\n    !_vm.showCity && _vm.focus && !_vm.isSearch && g0\n      ? _vm.__map(_vm.hot, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g1 =\n            index < 20\n              ? _vm.$tools.showImg(\n                  item.imgHead ? item.imgHead : \"/static/imgs/ut-logo.png\",\n                  200\n                )\n              : null\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var l1 = _vm.isSearch\n    ? _vm.__map(_vm.list, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g2 = _vm.$tools.showImg(\n          item.imgHead ? item.imgHead : \"/static/imgs/ut-logo.png\",\n          200\n        )\n        return {\n          $orig: $orig,\n          g2: g2,\n        }\n      })\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n    _vm.e3 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      return _vm.$tools.previewImage(\n        [item.imgHead ? item.imgHead : \"/static/imgs/ut-logo.png\"],\n        0,\n        800\n      )\n    }\n    _vm.e4 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n    _vm.e5 = function ($event, item) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item = _temp4.item\n      var _temp3, _temp4\n      return _vm.$tools.previewImage(\n        [item.imgHead ? item.imgHead : \"/static/imgs/ut-logo.png\"],\n        0,\n        800\n      )\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./area-hospital.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./area-hospital.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"area-community\" :class=\"{'full':focus}\">\r\n\t\t\t<view class=\"top-nav\" :class=\"{'full':focus}\">\r\n\t\t\t\t<view class=\"content\" :class=\"{'full':focus}\" :style=\"{borderColor:colors}\">\r\n\r\n\t\t\t<!-- \t\t<view class=\"scan\" v-if=\"!focus\" @click=\"scan()\">\r\n\t\t\t\t\t\t<u-icon name=\"scan\" :color=\"colors\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class=\"scan area\" @click=\"areaClick()\">\r\n\t\t\t\t\t\t<u-transition :show=\"true\" mode=\"fade\">\r\n\t\t\t\t\t\t\t<view class=\"area-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"city\">{{city.name||'全国'}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"area-arrow\" :class=\"{'open':showCity}\">\r\n\t\t\t\t\t\t\t\t\t<u-icon name=\"arrow-down-fill\" :color=\"colors\" size=\"12\"></u-icon>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</u-transition>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"community\" @click=\"inputClick\">\r\n\t\t\t\t\t\t<u--input border=\"none\" confirm-type=\"search\" @confirm=\"searchClick\" placeholder=\"搜索\" v-model=\"keyword\"></u--input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-transition :show=\"true\" mode=\"fade\">\r\n\t\t\t\t\t\t<view v-if=\"focus\" class=\"search\" :style=\"{background:colors}\" @click=\"searchClick\">\r\n\t\t\t\t\t\t\t<u-icon name=\"search\" color=\"#fff\" size=\"18\"></u-icon>\r\n\t\t\t\t\t\t\t搜索\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else><u-icon name=\"search\" :color=\"colors\" size=\"24\" @click=\"inputClick\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</u-transition>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"title\">{{title}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"city-box\" v-if=\"showCity && focus\">\r\n\t\t\t\t<view class=\"cur-location\">\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t当前位置：\r\n\t\t\t\t\t\t<text class=\"curr-city\" v-if=\"locationAddress.city\">{{locationAddress.city.name}}</text>\r\n\t\t\t\t\t\t<text class=\"curr-city\" v-else @click=\"getLocation\">重新获取</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click.stop=\"cityClick({})\">\r\n\t\t\t\t\t\t全国\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"province-city-box\">\r\n\t\t\t\t\t<view class=\"province\">\r\n\t\t\t\t\t\t<scroll-view scroll-y=\"true\" class=\"scroll-box\" @touchmove.stop.prevent=\"() => {}\">\r\n\t\t\t\t\t\t\t<template v-for=\"(item,index) in province\">\r\n\t\t\t\t\t\t\t\t<view style=\"background-color: #fff;\">\r\n\t\t\t\t\t\t\t\t\t<view :key=\"index\" class=\"province-item\" @click=\"provinceClick(item,index)\"\r\n\t\t\t\t\t\t\t\t\t\t:class=\"{'active':index==provinceIndex,'active-prev':index==provinceIndex-1,'active-next':index==provinceIndex+1}\">\r\n\t\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</template>\r\n\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"city\">\r\n\t\t\t\t\t\t<scroll-view scroll-y=\"true\" class=\"scroll-box\" @touchmove.stop.prevent=\"() => {}\">\r\n\t\t\t\t\t\t\t<template v-for=\"(item,index) in cityData\">\r\n\t\t\t\t\t\t\t\t<view :key=\"index\" class=\"city-item\" :class=\"{'active':item.code==city.code?city.code:''}\"\r\n\t\t\t\t\t\t\t\t\t@click=\"cityClick(item,index)\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"name\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.num || ''}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</template>\r\n\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"community-box\" v-if=\"!showCity && focus && !isSearch\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"scroll-box\" @touchmove.stop.prevent=\"() => {}\">\r\n\t\t\t\t\t<view class=\"host-community\" v-if=\"hot.length\">\r\n\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t<u-icon name=\"attach\" labelSize=\"10\" label=\"热门医院\" :color=\"colors\" size=\"12\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"hot-community-wrap\">\r\n\t\t\t\t\t\t\t<template v-for=\"(item,index) in hot\" v-if=\"index<20\">\r\n\t\t\t\t\t\t\t\t<view class=\"hot-community-item\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item-image\">\r\n\t\t\t\t\t\t\t\t\t\t<u-lazy-load height=\"80\" width=\"80\" border-radius=\"4\" img-mode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t\t\t\t@click=\"$tools.previewImage([item.imgHead?item.imgHead:'/static/imgs/ut-logo.png'],0,800)\"\r\n\t\t\t\t\t\t\t\t\t\t\t:image=\"$tools.showImg(item.imgHead?item.imgHead:'/static/imgs/ut-logo.png',200)\"></u-lazy-load>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"name\">\r\n\t\t\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- <text class=\"city\">({{item.city}})</text> -->\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"addr\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"item-op\" @click=\"communityClick(item)\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"btn\" :style=\"{color:colors,background:colors+'30'}\">选择</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search-box-wrap\" v-if=\"isSearch\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" class=\"scroll-box\" @touchmove.stop.prevent=\"() => {}\">\r\n\t\t\t\t\t<template v-for=\"(item,index) in list\">\r\n\t\t\t\t\t\t<view class=\"community-item\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"item-image\">\r\n\t\t\t\t\t\t\t\t<u-lazy-load height=\"100\" width=\"100\" border-radius=\"4\" img-mode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t\t@click=\"$tools.previewImage([item.imgHead?item.imgHead:'/static/imgs/ut-logo.png'],0,800)\"\r\n\t\t\t\t\t\t\t\t\t:image=\"$tools.showImg(item.imgHead?item.imgHead:'/static/imgs/ut-logo.png',200)\"></u-lazy-load>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"name\">\r\n\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t\t<!-- <text class=\"city\">({{item.city}})</text> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"addr\">{{item.address}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-op\" @click=\"communityClick(item)\">\r\n\t\t\t\t\t\t\t\t<view class=\"btn\" :style=\"{color:colors,background:colors+'30'}\">选择</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"loading\" v-if=\"isLoading\">\r\n\t\t\t\t<view class=\"sk-roller\" :style=\"{color:colors}\">\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t\t<div></div>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\r\n\t\t<u-overlay :show=\"focus\" @click=\"overlayClick\" :z-index=\"100\">\r\n\r\n\t\t</u-overlay>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapMutations,\r\n\t\tmapActions,\r\n\t\tmapState\r\n\t} from 'vuex'\r\n\texport default {\r\n\t\toptions: { //小程序样式穿透\r\n\t\t\tstyleIsolation: 'shared'\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String\r\n\t\t\t},\r\n\t\t\t// city: {\r\n\t\t\t// \ttype: Object,\r\n\t\t\t// \tdefault: () => {}\r\n\t\t\t// },\r\n\t\t\tmy: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t\thot: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t\tprovince: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: () => []\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tisSearch: false,\r\n\t\t\t\t// cityTemp: {},\r\n\t\t\t\tfocus: false,\r\n\t\t\t\tshowCity: false,\r\n\t\t\t\tprovinceIndex: 0,\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState({\r\n\t\t\t\tcity: state => state.init.city,\r\n\t\t\t\tlocationAddress: state => state.init.locationAddress,\r\n\t\t\t}),\r\n\t\t\tcityData() {\r\n\t\t\t\tif (!this.province) return []\r\n\t\t\t\tif (!this.province.length) return []\r\n\t\t\t\tlet obj = this.province[this.provinceIndex]\r\n\t\t\t\tif (!obj) return []\r\n\t\t\t\tif (!obj.citys) return []\r\n\t\t\t\tif (!obj.citys.length) return []\r\n\t\t\t\treturn obj.citys\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// city: {\r\n\t\t\t// \tdeep: true,\r\n\t\t\t// \thandler(v) {\r\n\t\t\t// \t\tthis.cityTemp = v\r\n\t\t\t// \t}\r\n\t\t\t// },\r\n\t\t\thot: {\r\n\t\t\t\tdeep: true,\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.isLoading = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlist: {\r\n\t\t\t\tdeep: true,\r\n\t\t\t\thandler(v) {\r\n\t\t\t\t\tthis.isLoading = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t...mapActions(['setCity','setCommunity','setLocationAddress']),\r\n\t\t\tscan() {\r\n\t\t\t\tthis.$wxsdk.scanQRCode().then(res => {\r\n\t\t\t\t\tif (res.errMsg == 'scanCode:ok') {\r\n\t\t\t\t\t\tthis.$emit('scan', {\r\n\t\t\t\t\t\t\tresult: res.result,\r\n\t\t\t\t\t\t\ttype: res.scanType\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tinputClick() {\r\n\t\t\t\tthis.focus = true\r\n\t\t\t\t// this.showCity = false\r\n\t\t\t},\r\n\t\t\toverlayClick() {\r\n\t\t\t\tthis.keyword = ''\r\n\t\t\t\tthis.isSearch = false\r\n\t\t\t\tthis.focus = false\r\n\t\t\t\tthis.showCity = false\r\n\t\t\t},\r\n\t\t\tareaClick() {\r\n\r\n\t\t\t\tthis.isSearch = false\r\n\t\t\t\tif(this.showCity){\r\n\t\t\t\t\tthis.showCity=false\r\n\t\t\t\t\t\r\n\t\t\t\t\t\tthis.focus = false\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.showCity = true\r\n\t\t\t\t\tthis.focus = true\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tprovinceClick(item, index) {\r\n\t\t\t\tthis.provinceIndex = index\r\n\r\n\t\t\t},\r\n\t\t\tcityClick(item, index) {\r\n\t\t\t\tif (this.city.code == item.code) return\r\n\t\t\t\tthis.setCity(item)\r\n\t\t\t\tthis.showCity = false\r\n\r\n\t\t\t\tthis.isSearch = false\r\n\t\t\t\tthis.focus=false\r\n\t\t\t\tthis.$emit('citySelect', item)\r\n\t\t\t\t// this.isLoading = true\r\n\t\t\t},\r\n\t\t\tcommunityClick(item) {\r\n\t\t\t\tthis.keyword = ''\r\n\t\t\t\tthis.isSearch = false\r\n\t\t\t\tthis.setCommunity(item)\r\n\t\t\t\tsetTimeout(() => {\r\n\r\n\t\t\t\t\tthis.focus = false\r\n\t\t\t\t\tthis.$emit('communitySelect', item)\r\n\t\t\t\t}, 80)\r\n\t\t\t},\r\n\t\t\tsearchClick() {\r\n\t\t\t\tthis.isSearch = true\r\n\t\t\t\tthis.showCity=false\r\n\t\t\t\tthis.isLoading = true\r\n\t\t\t\tthis.$emit('search', {\r\n\t\t\t\t\tkey: this.keyword,\r\n\t\t\t\t\tcity: this.cityTemp\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetLocation() {\r\n\t\t\t\tthis.isLoading=true\r\n\t\t\t\tthis.$wxsdk.getFuzzyLocationToAddress().then(res => {\r\n\t\t\t\t\tthis.setLocationAddress(res)\r\n\t\t\t\t\tthis.isLoading=false\r\n\t\t\t\t\t//console.log(res)\r\n\t\t\t\t}).catch(err=>{\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.isLoading=false\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.area-community {\r\n\r\n\t\tposition: relative;\r\n\t\t// padding: 10rpx 12rpx;\r\n\t\tz-index: 101;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t&.full {\r\n\t\t\tborder-radius: 0 0 10rpx 10rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\t.top-nav {\r\n\t\t\tpadding: 10rpx 12rpx;\r\n\r\n\t\t\t&.full {\r\n\t\t\t\t/* #ifdef MP */\r\n\t\t\t\tpadding-right: 180rpx;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.community {\r\n\t\t\t// background-color: #c3c3c3;\r\n\t\t\t// border-radius: 40rpx;\r\n\t\t\tflex: 1;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\tpadding-left: 30rpx;\r\n\t\t\tpadding-right: 30rpx;\r\n\t\t}\r\n\r\n\t\t.scan {\r\n\t\t\theight: 50rpx;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t\tposition: relative;\r\n\t\t}\r\n\r\n\t\t.scan::before {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 110%;\r\n\t\t\ttransform: translateY(15%);\r\n\t\t\t// /top: 0;\r\n\t\t\twidth: 4rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.15);\r\n\t\t\tcontent: '';\r\n\r\n\t\t}\r\n\r\n\t\t.area {\r\n\t\t\tpadding-left: 20rpx;\r\n\t\t\tpadding-right: 20rpx;\r\n\r\n\t\t\t.area-arrow {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\ttransform-origin: center;\r\n\t\t\t\ttransform: rotateZ(0deg);\r\n\r\n\t\t\t\t&.open {\r\n\t\t\t\t\ttransition: 0.3s;\r\n\t\t\t\t\ttransform-origin: center;\r\n\t\t\t\t\ttransform: rotateZ(180deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.area-content {\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: auto;\r\n\t\t\tflex-direction: row;\r\n\t\t\tposition: relative;\r\n\t\t\tmin-width: 60rpx;\r\n\t\t\tmax-width: 360rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\r\n\t\t.search {\r\n\t\t\ttext-align: center;\r\n\t\t\twidth: 120rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\ttransition: 0.4s;\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\t}\r\n\r\n\t.content {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 4rpx;\r\n\t\tborder-width: 3rpx;\r\n\t\tborder-radius: 80rpx;\r\n\t\tborder-style: solid;\r\n\t\twidth: 320rpx;\r\n\t\tz-index: 2;\r\n\t\tbackground: #fff;\r\n\t\ttransition: 0.3s;\r\n\r\n\t\t&.full {\r\n\t\t\twidth: 100%;\r\n\r\n\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.title {\r\n\t\twidth: 750rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tright: 0;\r\n\t\ttext-align: center;\r\n\t\twidth: 100%;\r\n\t\tline-height: 80rpx;\r\n\t\tz-index: -1;\r\n\t\tpadding-left: 80rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tletter-spacing: 2rpx;\r\n\t}\r\n\r\n\t.city {\r\n\t\tpadding-right: 6rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.city-box {\r\n\t\t.curr-city {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #000;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\r\n\t\t.cur-location {\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tcolor: #777;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding-right: 30rpx;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.province-city-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\theight: 660rpx;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.province {\r\n\t\t\theight: 100%;\r\n\t\t\tmax-width: 200rpx;\r\n\t\t\tmin-width: 120rpx;\r\n\t\t\tbackground-color: #eaeaea;\r\n\t\t}\r\n\r\n\t\t.city {\r\n\t\t\tflex: 1;\r\n\t\t\tpadding: 10rpx 20rpx;\r\n\t\t}\r\n\r\n\r\n\t}\r\n\t.province-item {\r\n\t\theight: 80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #eaeaea;\r\n\t\tcolor: #777;\r\n\r\n\t\t&.active {\r\n\t\t\tbackground: #fff;\r\n\t\t\tcolor: #000;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t&.active-prev {\r\n\t\t\tborder-radius: 0 0 20rpx 0;\r\n\t\t}\r\n\r\n\t\t&.active-next {\r\n\t\t\tborder-radius: 0 20rpx 0 0;\r\n\t\t}\r\n\t}\r\n\r\n\t.city-item {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #777;\r\n\r\n\t\t&.active {\r\n\t\t\tcolor: #000;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n\r\n\t.community-box {\r\n\t\theight: 700rpx;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\r\n\r\n\r\n\t.my-community,\r\n\t.host-community {\r\n\t\tcolor: #777;\r\n\r\n\t\t.label {\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tpadding: 0 0 15rpx 0;\r\n\t\t}\r\n\r\n\t\t.community-wrap {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t}\r\n\t}\r\n\r\n\t.community-name {\r\n\t\twidth: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\tjustify-items: flex-start;\r\n\t\tpadding: 5rpx 10rpx;\r\n\r\n\t\t.name-content {\r\n\t\t\tborder: 1px #eaeaea solid;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: 5rpx 10rpx;\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t.addr {\r\n\t\t\tfont-size: 18rpx;\r\n\t\t\tcolor: #aaa;\r\n\t\t}\r\n\t}\r\n\r\n\t.scroll-box {\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.hot-community-wrap {\r\n\t\t.hot-community-item {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 8rpx 10rpx;\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t\t.item-image {\r\n\t\t\t\tpadding: 8rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\r\n\t\t\t.item-op {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tpadding: 4rpx;\r\n\t\t\t\t\tborder-width: 1rpx;\r\n\t\t\t\t\tborder-radius: 80rpx;\r\n\t\t\t\t\tborder-style: solid;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\topacity: 0.6;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.item-content {\r\n\r\n\t\t\t\tpadding-left: 20rpx;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: space-around;\r\n\t\t\t\t.name{\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.addr {\r\n\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\tcolor: #aaa;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.city {\r\n\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\tcolor: #ccc;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.loading {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttop: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t\tz-index: 102;\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\r\n\t\t.sk-roller {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\tz-index: 102;\r\n\t\t}\r\n\t}\r\n\r\n\t.search-box-wrap {\r\n\t\theight: 680rpx;\r\n\t\tpadding: 0 30rpx 10rpx 30rpx;\r\n\r\n\t\t.community-item {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 8rpx 10rpx;\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.item-image {\r\n\t\t\t\tpadding: 8rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\r\n\t\t\t.item-op {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tpadding: 4rpx;\r\n\t\t\t\t\tborder-width: 1rpx;\r\n\t\t\t\t\tborder-radius: 80rpx;\r\n\t\t\t\t\tborder-style: solid;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\topacity: 0.6;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.item-content {\r\n\r\n\t\t\t\tpadding-left: 20rpx;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tjustify-content: space-around;\r\n\t\t\t\tfont-size: 26rpx;\r\n\r\n\t\t\t\t.addr {\r\n\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\tcolor: #aaa;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.city {\r\n\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\tcolor: #ccc;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./area-hospital.vue?vue&type=style&index=0&id=732f4d1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./area-hospital.vue?vue&type=style&index=0&id=732f4d1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754360673671\n      var cssReload = require(\"D:/opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}