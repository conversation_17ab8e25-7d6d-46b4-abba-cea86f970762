(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["components/ut/ut-image-upload/ut-image-upload"],{

/***/ 1149:
/*!*******************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue ***!
  \*******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ut-image-upload.vue?vue&type=template&id=fe64dc84& */ 1150);
/* harmony import */ var _ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ut-image-upload.vue?vue&type=script&lang=js& */ 1152);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ut-image-upload.vue?vue&type=style&index=0&lang=css& */ 1163);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 60);

var renderjs





/* normalize component */

var component = Object(_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["render"],
  _ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "components/ut/ut-image-upload/ut-image-upload.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1150:
/*!**************************************************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?vue&type=template&id=fe64dc84& ***!
  \**************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=template&id=fe64dc84& */ 1151);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_template_id_fe64dc84___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1151:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?vue&type=template&id=fe64dc84& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var l0 = _vm.__map(_vm.uploadLists, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var g0 = /.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url)
    var m0 = !g0 ? _vm.showImg(item.url) : null
    var m1 = !g0 ? _vm.checkLoad(index) : null
    var m2 = !g0 && !m1 ? _vm.imageLoadingWidth() : null
    var m3 = !g0 && !m1 ? _vm.imageLoadingWidth() : null
    var m4 = !g0
      ? !_vm.disabled &&
        _vm.remove &&
        item.progress == 100 &&
        _vm.checkLoad(index)
      : null
    var m5 = !g0 && m4 ? _vm.checkLoad(index) : null
    return {
      $orig: $orig,
      g0: g0,
      m0: m0,
      m1: m1,
      m2: m2,
      m3: m3,
      m4: m4,
      m5: m5,
    }
  })
  var g1 = _vm.uploadLists.length < _vm.max && _vm.add
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        l0: l0,
        g1: g1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1152:
/*!********************************************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=script&lang=js& */ 1153);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1153:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni, uniCloud) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default2 = {
  name: 'ut-image-upload',
  props: {
    width: {
      type: [Number, String],
      default: 120
    },
    colors: {
      type: String,
      default: ''
    },
    height: {
      type: [Number, String],
      default: 120
    },
    borderRadius: {
      type: Number,
      default: 8
    },
    dataType: {
      //v-model的数据结构类型
      type: Number,
      default: 0 // 0: ['http://xxxx.jpg','http://xxxx.jpg'] 1:[{type:0,url:'http://xxxx.jpg'}]  type 0 图片 1 视频 url 文件地址 此类型是为了给没有文件后缀的状况使用的
    },

    max: {
      //展示图片最大值
      type: Number,
      default: 1
    },
    chooseNum: {
      //选择图片数
      type: Number,
      default: 9
    },
    name: {
      //发到后台的文件参数名
      type: String,
      default: 'file'
    },
    remove: {
      //是否展示删除按钮
      type: Boolean,
      default: true
    },
    add: {
      //是否展示添加按钮
      type: Boolean,
      default: true
    },
    disabled: {
      //是否禁用
      type: Boolean,
      default: false
    },
    sourceType: {
      //选择照片来源 【ps：H5就别费劲了，设置了也没用。不是我说的，官方文档就这样！！！】
      type: Array,
      default: function _default() {
        return ['album', 'camera'];
      }
    },
    action: {
      //上传服务器
      type: String,
      default: ''
    },
    headers: {
      //上传的请求头部
      type: Object,
      default: function _default() {}
    },
    formData: {
      //HTTP 请求中其他额外的 form data
      type: Object,
      default: function _default() {}
    },
    compress: {
      //是否需要压缩
      type: Boolean,
      default: true
    },
    quality: {
      //压缩质量，范围0～100
      type: Number,
      default: 100
    },
    value: {
      //受控图片列表
      type: Array,
      default: function _default() {
        return [];
      }
    },
    uploadSuccess: {
      default: function _default(res) {
        return {
          success: false,
          url: ''
        };
      }
    },
    mediaType: {
      //文件类型 image/video/all
      type: String,
      default: 'image'
    },
    maxDuration: {
      //拍摄视频最长拍摄时间，单位秒。最长支持 60 秒。 (只针对拍摄视频有用)
      type: Number,
      default: 60
    },
    camera: {
      //'front'、'back'，默认'back'(只针对拍摄视频有用)
      type: String,
      default: 'back'
    },
    mode: {
      type: String,
      default: 'aspectFill'
    },
    longPress: {
      type: Boolean,
      default: false
    },
    // listIndex: {
    // 	type: Number,
    // 	default: -1,
    // },
    previewImageWidth: {
      type: Number,
      default: 750
    },
    spacing: {
      type: Number,
      default: 10
    }
  },
  data: function data() {
    return {
      timer: null,
      islongPress: false,
      imageLoaded: {},
      uploadLists: [],
      mediaTypeData: ['image', 'video', 'all'],
      previewVideoSrc: '',
      // 储存上传文件的请求 用于整理思路
      uploadTasks: [],
      // 保存父组件/页面的监听事件
      uploadTask: {
        // 默认不监听 调用setUpMonitor函数则监听
        load: false
      },
      uploadData: [],
      // 保存当前所有任务的进度 用于返回父组件/页面的监听事件
      uploadTaskProgress: [],
      isLoaded: false
    };
  },
  mounted: function mounted() {
    this.$nextTick(function () {
      var _this = this;
      this.uploadLists = [];
      this.value.forEach(function (item) {
        if ((0, _typeof2.default)(item) === 'object') {
          _this.uploadLists.push({
            id: item.id,
            url: item.url,
            state: 6,
            progress: 100
          });
        } else {
          _this.uploadLists.push({
            id: _this.guid(),
            url: item,
            state: 6,
            progress: 100
          });
        }
      });
      //this.uploadLists = this.value;

      if (this.mediaTypeData.indexOf(this.mediaType) == -1) {
        uni.showModal({
          title: '提示',
          content: 'mediaType参数不正确',
          showCancel: false,
          success: function success(res) {
            if (res.confirm) {} else if (res.cancel) {}
          }
        });
      }
    });
  },
  watch: {
    value: function value(val, oldVal) {
      var _this2 = this;
      if (this.isLoaded) return;
      if (!oldVal.length && !val.length) {
        this.isLoaded = true;
        return;
      }
      this.uploadLists = [];
      val.forEach(function (item) {
        _this2.uploadLists.push({
          id: _this2.guid(),
          url: item,
          state: 6,
          progress: 100
        });
      });
      this.isLoaded = true;
    }
  },
  methods: {
    imageLoadingWidth: function imageLoadingWidth() {
      if (this.width > this.height) {
        return this.height > 40 ? this.height / 3 : 40;
      } else {
        return this.width > 40 ? this.width / 3 : 40;
      }
    },
    imageLoad: function imageLoad(index) {
      this.$set(this.imageLoaded, 'l_' + index, true);
    },
    checkLoad: function checkLoad(index) {
      if (this.imageLoaded['l_' + index]) return true;
      return false;
    },
    isVideo: function isVideo(item) {
      var isPass = false;
      if (!/.(gif|jpg|jpeg|png|gif|jpg|png)$/i.test(item) && this.dataType == 0 || this.dataType == 1 && item.type == 1) {
        isPass = true;
      }
      return isPass;
    },
    getFileUrl: function getFileUrl(item) {
      var url = item;
      if (this.dataType == 1) {
        url = item.url;
      }
      return url;
    },
    previewVideoClick: function previewVideoClick(src) {
      if (!this.islongPress) {
        this.previewVideo(src);
      }
    },
    previewVideo: function previewVideo(src) {
      this.previewVideoSrc = src;
    },
    previewVideoClose: function previewVideoClose() {
      this.previewVideoSrc = '';
    },
    imgDel: function imgDel(item, index) {
      var _this3 = this;
      uni.showModal({
        title: '提示',
        content: '您确定要删除么?',
        success: function success(res) {
          if (res.confirm) {
            var delObj = _this3.uploadLists[index];
            // console.log(index)
            _this3.uploadLists.splice(index, 1);
            var arr = [];
            _this3.uploadLists.forEach(function (item) {
              if (item.state == 6) arr.push(item.url);
            });
            _this3.$emit("input", arr);
            _this3.$emit("imgDelete", {
              url: delObj.url,
              id: delObj.id
            });
          } else if (res.cancel) {}
        }
      });
    },
    delIndex: function delIndex(index) {
      this.uploadLists.splice(index, 1);
    },
    clear: function clear() {
      this.uploadLists = [];
    },
    imgPreviewClick: function imgPreviewClick(item, index) {
      if (!this.islongPress) {
        this.imgPreview(item.url, index);
      }
    },
    imgPreview: function imgPreview(url, index) {
      var _this4 = this;
      var imgData = this.uploadLists.filter(function (item) {
        return !/.(mp4|avi|mkv|asf|wmv|3gp|flv|mov)$/i.test(item.url);
      }); //只预览图片的
      var arr = [];
      imgData.forEach(function (item) {
        if (!item) return true;
        if ((0, _typeof2.default)(item.url) === 'object' && item.url !== null) {
          if (item.url.url.indexOf('oss.') > 0) {
            arr.push(item.url.url + "&width=" + _this4.previewImageWidth + "&quality=70");
          } else {
            arr.push(item.url.url);
          }
        } else {
          if (item.url.indexOf('oss.') > 0) {
            arr.push(item.url + "&width=" + _this4.previewImageWidth + "&quality=70");
          } else {
            arr.push(item.url);
          }
        }
      });
      uni.previewImage({
        urls: arr,
        current: index,
        loop: true
      });
    },
    chooseFile: function chooseFile() {
      var _this5 = this;
      if (this.disabled) {
        return false;
      }
      this.uploadData = [];
      switch (this.mediaTypeData.indexOf(this.mediaType)) {
        case 1:
          //视频
          this.videoAdd();
          break;
        case 2:
          //全部
          uni.showActionSheet({
            itemList: ['相册', '视频'],
            success: function success(res) {
              if (res.tapIndex == 1) {
                _this5.videoAdd();
              } else if (res.tapIndex == 0) {
                _this5.imgAdd();
              }
            },
            fail: function fail(res) {}
          });
          break;
        default:
          //图片
          this.imgAdd();
          break;
      }
    },
    videoAdd: function videoAdd() {
      var _this6 = this;
      var nowNum = Math.abs(this.uploadLists.length - this.max);
      var thisNum = this.chooseNum > nowNum ? nowNum : this.chooseNum; //可选数量
      uni.chooseVideo({
        compressed: this.compress,
        sourceType: this.sourceType,
        camera: this.camera,
        maxDuration: this.maxDuration,
        success: function success(res) {
          _this6.chooseSuccessMethod([res.tempFilePath], 1);
        }
      });
    },
    imgAdd: function imgAdd() {
      var _this7 = this;
      var nowNum = Math.abs(this.uploadLists.length - this.max);
      var thisNum = this.chooseNum > nowNum ? nowNum : this.chooseNum; //可选数量

      uni.chooseImage({
        count: thisNum,
        sizeType: ['original', 'compressed'],
        //可以指定是原图还是压缩图，默认二者都有
        sourceType: this.sourceType,
        success: function success(res) {
          _this7.chooseSuccessMethod(res.tempFilePaths, 0);
        }
      });
    },
    appCamera: function appCamera() {
      var _this8 = this;
      var cmr = plus.camera.getCamera();
      var res = cmr.supportedImageResolutions[0];
      var fmt = cmr.supportedImageFormats[0];
      cmr.captureImage(function (path) {
        _this8.chooseSuccessMethod([path], 0);
      }, function (error) {}, {
        resolution: res,
        format: fmt
      });
    },
    appGallery: function appGallery(maxNum) {
      var _this9 = this;
      plus.gallery.pick(function (res) {
        _this9.chooseSuccessMethod(res.files, 0);
      }, function (e) {}, {
        filter: "image",
        multiple: true,
        maximum: maxNum
      });
    },
    chooseSuccessMethod: function chooseSuccessMethod(filePaths, type) {
      if (this.action == '') {
        //未配置上传路径
        this.$emit("chooseSuccess", filePaths, type); //filePaths 路径 type 0 为图片 1为视频
        return;
      }
      if (type == 1) {
        this.imgUpload(filePaths, type);
      } else {
        if (this.compress) {
          //设置了需要压缩
          this.imgCompress(filePaths, type);
        } else {
          this.imgUpload(filePaths, type);
        }
      }
    },
    imgCompress: function imgCompress(tempFilePaths, type) {
      var _this10 = this;
      uni.showLoading({
        title: '压缩中...'
      });
      var compressImgs = [];
      var results = [];
      tempFilePaths.forEach(function (item, index) {
        compressImgs.push(new Promise(function (resolve, reject) {
          uni.compressImage({
            src: item,
            quality: _this10.quality,
            success: function success(res) {
              results.push(res.tempFilePath);
              resolve(res.tempFilePath);
            },
            fail: function fail(err) {
              reject(err);
            },
            complete: function complete() {
              //uni.hideLoading();
            }
          });
        }));
      });
      Promise.all(compressImgs) //执行所有需请求的接口
      .then(function (results) {
        uni.hideLoading();
        _this10.imgUpload(results, type);
      }).catch(function (res, object) {
        uni.hideLoading();
      });
    },
    imgUpload: function imgUpload(tempFilePaths, type) {
      var _this11 = this;
      if (this.action == 'uniCloud') {
        this.uniCloudUpload(tempFilePaths, type);
        return;
      }
      uni.showLoading({
        title: '上传中'
      });
      var that = this;
      tempFilePaths.forEach(function (item) {
        var obj = {
          id: that.guid(),
          state: 0,
          progress: 0,
          url: item,
          data: []
        };
        // console.log(obj)
        that.uploadLists.push(obj);
        //that.$emit("uploadSuccess", obj)
      });

      var uploadImgs = [];
      var arr = that.uploadLists.filter(function (u) {
        return u.state == 0;
      });
      arr.forEach(function (item, index) {
        var promise = new Promise(function (resolve, reject) {
          var task = uni.uploadFile({
            url: that.action,
            //仅为示例，非真实的接口地址
            filePath: item.url,
            name: that.name,
            fileType: 'image',
            formData: that.formData,
            header: that.headers,
            success: function success(uploadFileRes) {
              var obj = that.uploadLists.find(function (u) {
                return u.id == task.id;
              });
              if (obj) {
                var _res = JSON.parse(uploadFileRes.data);
                that.$set(obj, 'progress', 100);
                that.$set(obj, 'data', _res[0]);
                that.$set(obj, 'state', 2);
                _this11.uploadData.push(obj);
                // that.$emit("uploadSuccess", obj)
              }

              resolve(obj);
            },
            fail: function fail(err) {
              reject(err);
              that.$emit("uploadFail", err);
            },
            complete: function complete() {}
          });
          task.id = item.id;
          task.onProgressUpdate(function (res) {
            if (res.progress != 100) {
              var obj = _this11.uploadLists.find(function (u) {
                return u.id == task.id;
              });
              if (obj) {
                that.$set(obj, 'progress', res.progress);
                that.$set(obj, 'state', 1);
              }
            }
          });
        });
        uploadImgs.push(promise);
      });
      Promise.all(uploadImgs) //执行所有需请求的接口
      .then(function (results) {
        uni.hideLoading();
        //  console.log('uploadList',this.uploadLists)
        //  console.log('uploadData',this.uploadData)
        // // return
        // // uni.hideLoading();
        // // console.log(this.uploadLists,this.uploadData)
        // let arr=[]
        // this.uploadData.forEach(item=>{
        // 	let obj = this.uploadData.find(u=>u.id==item.id)
        // 	if(!obj) arr.push(item)
        // })
        // console.log(arr)
        that.$emit("uploadSuccess", _this11.uploadData);
      }).catch(function (res, object) {
        uni.hideLoading();
        _this11.$emit("uploadFail", res);
      });
    },
    uniCloudUpload: function uniCloudUpload(tempFilePaths, type) {
      var _this12 = this;
      uni.showLoading({
        title: '上传中'
      });
      var uploadImgs = [];
      tempFilePaths.forEach(function (item, index) {
        uploadImgs.push(new Promise(function (resolve, reject) {
          uniCloud.uploadFile({
            filePath: item,
            cloudPath: _this12.guid() + '.' + _this12.getFileType(item, type),
            success: function success(uploadFileRes) {
              if (uploadFileRes.success) {
                resolve(uploadFileRes.fileID);
              }
            },
            fail: function fail(err) {
              console.log(err);
              reject(err);
            },
            complete: function complete() {}
          });
        }));
      });
      Promise.all(uploadImgs) //执行所有需请求的接口
      .then(function (results) {
        uni.hideLoading();
        uniCloud.getTempFileURL({
          fileList: results,
          success: function success(res) {
            res.fileList.forEach(function (item) {
              //this.value.push(item.tempFileURL)

              _this12.value.push(item.tempFileURL);
              _this12.$emit("input", _this12.value);
            });
          },
          fail: function fail() {},
          complete: function complete() {}
        });
      }).catch(function (res, object) {
        uni.hideLoading();
      });
    },
    getFileType: function getFileType(path, type) {
      //手机端默认图片为jpg 视频为mp4

      var result = path.split('.').pop().toLowerCase();
      if (this.compress) {
        //微信小程序压缩完没有后缀
        result = type == 0 ? 'jpg' : 'mp4';
      }
      return result;
    },
    guid: function guid() {
      return 'xxxxxxxx-date-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
          v = c == 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
      }).replace(/date/g, function (c) {
        return Date.parse(new Date());
      });
    },
    /**
     * 触发监听
     * @param {Number} key 当前任务的下标
     * @param {Object} uploadTask uni.uploadFile 的返回值
     */
    triggerMonitor: function triggerMonitor(key, uploadTask) {
      var _this13 = this;
      uploadTask.onProgressUpdate(function (res) {
        // 触发父组件/页面的监听事件
        _this13.uploadTaskProgress[key] = res;
        // 合并所有任务的列表用于父组件/页面的监听
        // {
        //	// 当前的进度
        // 	progress: 0,
        //	// 当前的所有进度 保存每条任务的进度等等
        // 	tasks: []
        // }
        _this13.uploadTask.onProgressUpdate(_this13.mergerProgress(_this13.uploadTaskProgress));
      });
    },
    /**
     * 合并进度
     * @param {Array} tasks 所有的任务
     */
    mergerProgress: function mergerProgress(tasks) {
      var progress = 0;
      tasks.forEach(function (value, key) {
        if (value) {
          progress += value.progress;
        } else {
          progress += 0;
        }
      });
      return {
        progress: progress / tasks.length,
        tasks: tasks
      };
    },
    /**
     * 设置父组件/页面的监听事件
     * onProgressBegin 开始监听触发事件
     * onProgressUpdate 进度变化事件
     * onProgressEnd 结束监听事件
     * [req 1,req 2...]
     * @param {Object} obj {onProgressUpdate,onProgressBegin,onProgressEnd}
     */
    setUpMonitor: function setUpMonitor(obj) {
      this.uploadTask = _objectSpread({
        load: true
      }, obj);
    },
    canvasDataURL: function canvasDataURL(path, obj, callback) {
      var img = new Image();
      img.src = path;
      img.onload = function () {
        var that = this;
        // 默认按比例压缩
        var w = that.width,
          h = that.height,
          scale = w / h;
        w = obj.width || w;
        h = obj.height || w / scale;
        var quality = 0.8; // 默认图片质量为0.8
        //生成canvas
        var canvas = document.createElement('canvas');
        var ctx = canvas.getContext('2d');
        // 创建属性节点
        var anw = document.createAttribute("width");
        anw.nodeValue = w;
        var anh = document.createAttribute("height");
        anh.nodeValue = h;
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);
        ctx.drawImage(that, 0, 0, w, h);
        // 图像质量
        if (obj.quality && obj.quality <= 1 && obj.quality > 0) {
          quality = obj.quality;
        }
        // quality值越小，所绘制出的图像越模糊
        var base64 = canvas.toDataURL('image/jpeg', quality);
        // 回调函数返回base64的值
        callback(base64);
      };
    },
    showImg: function showImg(url) {
      var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 200;
      var quality = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 70;
      var urlTmp;
      if ((0, _typeof2.default)(url) === 'object') {
        urlTmp = url.url;
      } else {
        urlTmp = url;
      }
      if (urlTmp.indexOf('oss.') > 0) {
        var str = urlTmp;
        str += "&width=" + this.width;
        str += "&height=" + this.height;
        if (quality) str += "&quality=" + quality;
        return str;
      } else {
        return urlTmp;
      }
    },
    parseBlob: function parseBlob(base64) {
      var arr = base64.split(',');
      var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      for (var i = 0; i < n; i++) {
        u8arr[i] = bstr.charCodeAt(i);
      }
      var url = URL || webkitURL;
      return url.createObjectURL(new Blob([u8arr], {
        type: mime
      }));
    },
    longpress: function longpress(item, index, type) {
      var itemList = ['预览'];
      if (this.remove) itemList.push('删除');
      var that = this;
      that.islongPress = true;
      uni.showActionSheet({
        itemList: itemList,
        success: function success(res) {
          if (res.tapIndex == 0) {
            switch (type) {
              case 1:
                //视频
                that.previewVideo(item.url);
                break;
              case 0:
                that.imgPreview(item.url, index);
                break;
            }
          } else if (res.tapIndex == 1) {
            that.imgDel(item, index);
          }
        },
        fail: function fail(res) {}
      });
      // uni.showModal({
      // 	title:'长按'+index
      // })
    },
    touchstart: function touchstart(item, index, type) {
      var _this14 = this;
      if (!this.longPress && type != 1) return;
      this.timer = setTimeout(function () {
        _this14.longpress(item, index, type);
      }, 1000);
    },
    touchend: function touchend() {
      var _this15 = this;
      clearTimeout(this.timer);
      setTimeout(function () {
        _this15.islongPress = false;
      }, 200);
    }
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 1154)["uniCloud"]))

/***/ }),

/***/ 1163:
/*!****************************************************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?vue&type=style&index=0&lang=css& ***!
  \****************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ut-image-upload.vue?vue&type=style&index=0&lang=css& */ 1164);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_ut_image_upload_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1164:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/svn/ynyt/源代码/前端框架/Project/先施康养/components/ut/ut-image-upload/ut-image-upload.vue?vue&type=style&index=0&lang=css& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/ut/ut-image-upload/ut-image-upload.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/ut/ut-image-upload/ut-image-upload-create-component',
    {
        'components/ut/ut-image-upload/ut-image-upload-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(1149))
        })
    },
    [['components/ut/ut-image-upload/ut-image-upload-create-component']]
]);
