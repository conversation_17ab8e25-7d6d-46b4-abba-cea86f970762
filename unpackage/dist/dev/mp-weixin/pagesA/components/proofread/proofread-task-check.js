require('../../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesA/components/proofread/proofread-task-check"],{

/***/ 1381:
/*!**********************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue ***!
  \**********************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./proofread-task-check.vue?vue&type=template&id=48492186&scoped=true& */ 1382);
/* harmony import */ var _proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./proofread-task-check.vue?vue&type=script&lang=js& */ 1384);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./proofread-task-check.vue?vue&type=style&index=0&id=48492186&lang=scss&scoped=true& */ 1386);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 60);

var renderjs





/* normalize component */

var component = Object(_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "48492186",
  null,
  false,
  _proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesA/components/proofread/proofread-task-check.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1382:
/*!*****************************************************************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue?vue&type=template&id=48492186&scoped=true& ***!
  \*****************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-task-check.vue?vue&type=template&id=48492186&scoped=true& */ 1383);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_template_id_48492186_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 1383:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue?vue&type=template&id=48492186&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    utPage: function () {
      return Promise.all(/*! import() | components/ut/ut-page/ut-page */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/ut/ut-page/ut-page")]).then(__webpack_require__.bind(null, /*! @/components/ut/ut-page/ut-page.vue */ 981))
    },
    fNavbar: function () {
      return __webpack_require__.e(/*! import() | components/f-navbar/f-navbar */ "components/f-navbar/f-navbar").then(__webpack_require__.bind(null, /*! @/components/f-navbar/f-navbar.vue */ 1142))
    },
    uLazyLoad: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-lazy-load/u-lazy-load */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-lazy-load/u-lazy-load")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-lazy-load/u-lazy-load.vue */ 1233))
    },
    uRadioGroup: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-radio-group/u-radio-group */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-radio-group/u-radio-group")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-radio-group/u-radio-group.vue */ 1412))
    },
    uRadio: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-radio/u-radio */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-radio/u-radio")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-radio/u-radio.vue */ 1420))
    },
    uButton: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-button/u-button */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-button/u-button")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-button/u-button.vue */ 1196))
    },
    uSwipeAction: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-swipe-action/u-swipe-action */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-swipe-action/u-swipe-action")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-swipe-action/u-swipe-action.vue */ 1279))
    },
    uSwipeActionItem: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-swipe-action-item/u-swipe-action-item */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-swipe-action-item/u-swipe-action-item")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-swipe-action-item/u-swipe-action-item.vue */ 1285))
    },
    uPopup: function () {
      return Promise.all(/*! import() | components/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! @/components/uview-ui/components/u-popup/u-popup.vue */ 1120))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.form.imgHead ? _vm.$tools.showImg(_vm.form.imgHead) : null
  var l0 = _vm.__map(_vm.orderProjects, function (item, __i1__) {
    var $orig = _vm.__get_orig(item)
    var m0 = _vm.getProjectActionOptions(item)
    return {
      $orig: $orig,
      m0: m0,
    }
  })
  var g1 = !_vm.form.projects || !_vm.form.projects.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.showProjectPopup = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g1: g1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 1384:
/*!***********************************************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-task-check.vue?vue&type=script&lang=js& */ 1385);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_opt_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1385:
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 30));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 32));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _vuex = __webpack_require__(/*! vuex */ 39);
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var ProjectItem = function ProjectItem() {
  __webpack_require__.e(/*! require.ensure | pagesA/components/project-item */ "pagesA/components/project-item").then((function () {
    return resolve(__webpack_require__(/*! @/pagesA/components/project-item.vue */ 1481));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var FileDataItem = function FileDataItem() {
  __webpack_require__.e(/*! require.ensure | pagesA/components/file-data-item */ "pagesA/components/file-data-item").then((function () {
    return resolve(__webpack_require__(/*! @/pagesA/components/file-data-item.vue */ 1794));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var ProjectSelector = function ProjectSelector() {
  __webpack_require__.e(/*! require.ensure | pagesA/components/proofread/project-selector */ "pagesA/components/proofread/project-selector").then((function () {
    return resolve(__webpack_require__(/*! ./project-selector.vue */ 1523));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var app = getApp();
var _default = {
  components: {
    ProjectItem: ProjectItem,
    FileDataItem: FileDataItem,
    ProjectSelector: ProjectSelector
  },
  props: {
    colors: {
      type: String,
      default: ''
    },
    // 'mang/care' 或 'mang/nurse'
    apiPath: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: '服务项目校对'
    },
    workId: {
      type: String,
      required: true
    },
    month: {
      type: String,
      default: null
    }
  },
  options: {
    styleIsolation: 'shared'
  },
  data: function data() {
    return {
      loading: false,
      form: {
        id: '',
        workDate: '',
        groupName: '',
        attendantId: '',
        attendantName: '',
        customerId: '',
        name: '',
        sex: 0,
        phone: '',
        idcard: '',
        address: '',
        isManual: false,
        manualTime: '',
        manualUserName: '',
        projects: [],
        datas: [],
        suplus: 0,
        proofreadError: false,
        proofreadErrorRemark: ''
      },
      showProjectPopup: false,
      projectData: [],
      initialProjects: [],
      errorTypeOptions: ['截图项目时长', '签字表时间', '签字表项目'],
      errorTypeStates: {}
    };
  },
  computed: _objectSpread(_objectSpread({}, (0, _vuex.mapState)({
    commKey: function commKey(state) {
      return state.init.template.commKey;
    },
    token: function token(state) {
      return state.user.token;
    },
    userInfo: function userInfo(state) {
      return state.user.info;
    },
    community: function community(state) {
      return state.init.community;
    }
  })), {}, {
    orderProjects: function orderProjects() {
      var _this$form$projects$t, _this$form$projects;
      return (_this$form$projects$t = (_this$form$projects = this.form.projects) === null || _this$form$projects === void 0 ? void 0 : _this$form$projects.toSorted(function (a, b) {
        return a.govCode - b.govCode;
      })) !== null && _this$form$projects$t !== void 0 ? _this$form$projects$t : [];
    },
    joinedDatas: function joinedDatas() {
      var _this$form$datas;
      if (!((_this$form$datas = this.form.datas) !== null && _this$form$datas !== void 0 && _this$form$datas.length)) return null;
      var datas = this.form.datas;
      var titles = [];
      var outputDetail = {
        title: '',
        files: []
      };
      var _iterator = _createForOfIteratorHelper(datas),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var d = _step.value;
          titles.push(d.title);
          var _iterator2 = _createForOfIteratorHelper(d.details),
            _step2;
          try {
            for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
              var _outputDetail$files;
              var details = _step2.value;
              (_outputDetail$files = outputDetail.files).push.apply(_outputDetail$files, (0, _toConsumableArray2.default)(details.files));
            }
          } catch (err) {
            _iterator2.e(err);
          } finally {
            _iterator2.f();
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      if (!outputDetail.files.length) return null;
      return {
        createTime: '',
        dataTypeId: '',
        title: titles.join('、'),
        details: [outputDetail]
      };
    }
  }),
  watch: {
    workId: {
      handler: function handler(newVal) {
        if (newVal) {
          this.loadTask();
        }
      },
      immediate: true
    }
  },
  methods: {
    loadTask: function loadTask() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var _yield$_this$$ut$api$, data;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (_this.workId) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _this.loading = true;
                _context.next = 5;
                return _this.$ut.api("".concat(_this.apiPath, "/proofread/task"), {
                  communityId: _this.community.id,
                  workId: _this.workId,
                  month: _this.month
                }).finally(function () {
                  return _this.loading = false;
                });
              case 5:
                _yield$_this$$ut$api$ = _context.sent;
                data = _yield$_this$$ut$api$.data;
                _this.updateFormData(data);
              case 8:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    updateFormData: function updateFormData(data) {
      this.form = _objectSpread(_objectSpread(_objectSpread({}, this.form), data), {}, {
        datas: data.datas || [],
        suplus: data.suplus || 0,
        proofreadErrorRemark: data.proofreadError ? data.proofreadErrorRemark : ''
      });
      if (data.projects && data.projects.length > 0) {
        this.initialProjects = JSON.parse(JSON.stringify(data.projects));
      }
      this.initErrorTypeStates();
    },
    initErrorTypeStates: function initErrorTypeStates() {
      var _this2 = this;
      var errorStates = {};
      this.errorTypeOptions.forEach(function (option) {
        errorStates[option] = false;
      });
      if (this.form.proofreadErrorRemark) {
        var selectedErrors = this.form.proofreadErrorRemark.trim().split('、').map(function (e) {
          return e.replaceAll('有误', '');
        });
        selectedErrors.forEach(function (error) {
          if (_this2.errorTypeOptions.includes(error)) {
            errorStates[error] = true;
          }
        });
        this.errorTypeOptions.forEach(function (option) {
          if (errorStates[option] === null && _this2.form.proofreadErrorRemark) {
            errorStates[option] = false;
          }
        });
      }
      this.errorTypeStates = errorStates;
    },
    updateErrorTypeState: function updateErrorTypeState(option, value) {
      this.$set(this.errorTypeStates, option, value);
      this.syncErrorRemark();
    },
    syncErrorRemark: function syncErrorRemark() {
      var _this3 = this;
      var errorTypes = this.errorTypeOptions.filter(function (option) {
        return _this3.errorTypeStates[option] === true;
      });
      this.form.proofreadErrorRemark = errorTypes.map(function (e) {
        return e + '有误';
      }).join('、');
      this.form.proofreadError = errorTypes.length > 0;
    },
    save: function save() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var params, _yield$_this4$$ut$api, data;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _this4.loading = true;
                params = {
                  communityId: _this4.community.id,
                  workId: _this4.form.id,
                  month: _this4.month,
                  projectIds: _this4.form.projects ? _this4.form.projects.map(function (p) {
                    return p.projectId;
                  }) : [],
                  proofreadError: _this4.form.proofreadError
                };
                if (_this4.form.proofreadError) {
                  params.proofreadErrorRemark = _this4.form.proofreadErrorRemark;
                }
                _context2.next = 5;
                return _this4.$ut.api("".concat(_this4.apiPath, "/proofread/save"), params).finally(function () {
                  return _this4.loading = false;
                });
              case 5:
                _yield$_this4$$ut$api = _context2.sent;
                data = _yield$_this4$$ut$api.data;
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
                if (data && data.id) {
                  _this4.updateFormData(data);
                } else {
                  setTimeout(function () {
                    _this4.goBack();
                  }, 1500);
                }
              case 9:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    resetProjects: function resetProjects() {
      var _this5 = this;
      uni.showModal({
        title: '确认重置',
        content: '确定要将服务项目重置为初始状态吗？',
        success: function success(res) {
          if (res.confirm) {
            var _this5$initialProject;
            if (((_this5$initialProject = _this5.initialProjects) === null || _this5$initialProject === void 0 ? void 0 : _this5$initialProject.length) > 0) {
              _this5.form.projects = JSON.parse(JSON.stringify(_this5.initialProjects));
            } else {
              _this5.form.projects = [];
            }
          }
        }
      });
    },
    addProject: function addProject() {
      this.loadProjectData();
      this.showProjectPopup = true;
    },
    loadProjectData: function loadProjectData() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var _yield$_this6$$ut$api, data;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return _this6.$ut.api('mang/care/work/project/allRuleListpg', {
                  communityId: _this6.community.id,
                  workId: _this6.form.id
                });
              case 2:
                _yield$_this6$$ut$api = _context3.sent;
                data = _yield$_this6$$ut$api.data;
                _this6.projectData = (data === null || data === void 0 ? void 0 : data.info) || [];
              case 5:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    getProjectActionOptions: function getProjectActionOptions() {
      var btnDelete = {
        text: '删除',
        code: 'delete',
        style: {
          backgroundColor: '#f56c6c'
        }
      };
      return [btnDelete];
    },
    projectActionClick: function projectActionClick(data) {
      if (data.code === 'delete') {
        this.removeProject(data.name);
      }
      this.$refs['swipeProjectList'].closeAll();
    },
    removeProject: function removeProject(projectId) {
      if (this.form.projects && this.form.projects.length) {
        var index = this.form.projects.findIndex(function (p) {
          return p.projectId === projectId;
        });
        if (index >= 0) {
          this.form.projects.splice(index, 1);
        }
      }
    },
    selectProject: function selectProject(projects) {
      var _this7 = this;
      this.showProjectPopup = false;
      if (!this.form.projects) {
        this.$set(this.form, 'projects', []);
      }
      if (!projects || !projects.length) return;
      projects.forEach(function (project) {
        var existingProject = _this7.form.projects.find(function (p) {
          return p.projectId === project.id;
        });
        if (!existingProject) {
          _this7.form.projects.push({
            id: project.id,
            projectId: project.id,
            projectName: project.name,
            projectCode: project.code,
            govCode: project.govCode,
            requireMinDuration: project.minDuration,
            requireMaxDuration: project.maxDuration,
            remark: project.remark || ''
          });
        }
      });
    },
    goBack: function goBack() {
      uni.navigateBack();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 1386:
/*!********************************************************************************************************************************************************!*\
  !*** D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue?vue&type=style&index=0&id=48492186&lang=scss&scoped=true& ***!
  \********************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../opt/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./proofread-task-check.vue?vue&type=style&index=0&id=48492186&lang=scss&scoped=true& */ 1387);
/* harmony import */ var _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_opt_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_opt_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_opt_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_opt_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_proofread_task_check_vue_vue_type_style_index_0_id_48492186_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1387:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/svn/ynyt/源代码/前端框架/Project/先施康养/pagesA/components/proofread/proofread-task-check.vue?vue&type=style&index=0&id=48492186&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesA/components/proofread/proofread-task-check.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesA/components/proofread/proofread-task-check-create-component',
    {
        'pagesA/components/proofread/proofread-task-check-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(1381))
        })
    },
    [['pagesA/components/proofread/proofread-task-check-create-component']]
]);
