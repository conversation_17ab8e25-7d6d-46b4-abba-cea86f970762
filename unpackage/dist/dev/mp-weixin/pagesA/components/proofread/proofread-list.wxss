.item-box.data-v-596459db {
  padding: 20rpx 30rpx;
  background-color: white;
  margin-bottom: 10rpx;
  display: flex;
  position: relative;
  overflow: hidden;
}
.item-box .content.data-v-596459db {
  flex: 1;
  padding: 0 20rpx;
}
.item-box.data-v-596459db:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  transition: all 0.3s ease;
}
.cuIcon-people.data-v-596459db {
  font-size: 116rpx;
  color: gray;
  border: 1rpx solid #ccc;
  width: 116rpx;
  height: 156rpx;
  line-height: 156rpx;
  border-radius: 6rpx;
  display: block;
}

