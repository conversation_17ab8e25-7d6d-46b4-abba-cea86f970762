<view class="data-v-596459db"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onItemClick',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e" class="data-v-596459db"><view class="item-box data-v-596459db"><view class="head-image data-v-596459db"><block wx:if="{{item.$orig.imgHead}}"><u-lazy-load vue-id="{{'59b13aea-1-'+index}}" image="{{item.g0}}" width="120" height="160" border-radius="4" class="data-v-596459db" bind:__l="__l"></u-lazy-load></block><block wx:else><text class="cuIcon-people data-v-596459db"></text></block></view><view class="content text-content data-v-596459db"><view class="flex justify-between align-center data-v-596459db"><view class="data-v-596459db"><view class="flex justify-start align-center flex-wrap data-v-596459db"><view class="text-df text-bold margin-right-xs data-v-596459db">{{item.$orig.name}}</view><view class="{{['data-v-596459db',(true)?'cu-tag sm radius light':'',(item.$orig.proofreadError)?'bg-orange':'',(!item.$orig.proofreadError&&item.$orig.isManual)?'bg-green':'',(!item.$orig.proofreadError&&!item.$orig.isManual)?'bg-blue':'']}}">{{''+item.m0+''}}</view></view><view class="text-sm text-gray margin-top-xs data-v-596459db">{{'手机号：'+item.$orig.phone+''}}</view><view class="text-sm text-gray margin-top-xs data-v-596459db">{{'护理日期：'+item.$orig.workDate+''}}</view></view></view><view class="text-sm text-gray margin-top-xs data-v-596459db">{{'护理员：'+item.$orig.attendantName+"（"+item.$orig.groupName+'）'}}</view><block wx:if="{{item.$orig.isManual}}"><view class="text-sm text-gray margin-top-xs data-v-596459db">{{'校对人：'+item.$orig.lastManualUserName+''}}</view><view class="text-sm text-gray margin-top-xs data-v-596459db">{{'校对时间：'+item.$orig.lastManualTime+''}}</view></block></view></view></view></block></view>