<view class="data-v-30c7270a"><f-navbar vue-id="2fc38988-1" fontColor="#fff" bgColor="{{colors}}" title="{{title}}" navbarType="1" class="data-v-30c7270a" bind:__l="__l"></f-navbar><view class="padding-top-sm padding-lr-sm flex align-center data-v-30c7270a" style="{{'background-color:'+('#fff')+';'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="u-border margin-right-sm round padding-tb-sm padding-lr-lg data-v-30c7270a" bindtap="__e"><view class="text-grey text-bold data-v-30c7270a">{{month}}</view></view><u-input bind:input="__e" vue-id="2fc38988-2" prefixIcon="search" placeholder="输入客户或护理员姓名" shape="circle" border="surround" clearable="{{true}}" value="{{searchData.key}}" data-event-opts="{{[['^input',[['__set_model',['$0','key','$event',[]],['searchData']]]]]}}" class="data-v-30c7270a" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix" class="data-v-30c7270a"><u-button vue-id="{{('2fc38988-3')+','+('2fc38988-2')}}" text="搜索" type="success" size="mini" data-event-opts="{{[['^click',[['onSearch']]]]}}" bind:click="__e" class="data-v-30c7270a" bind:__l="__l"></u-button></view></u-input></view><u-datetime-picker vue-id="2fc38988-4" show="{{showMonthPicker}}" mode="year-month" title="选择月份" closeOnClickOverlay="{{true}}" value="{{monthValue}}" data-event-opts="{{[['^confirm',[['onMonthConfirm']]],['^close',[['e1']]],['^cancel',[['e2']]],['^input',[['__set_model',['','monthValue','$event',[]]]]]]}}" bind:confirm="__e" bind:close="__e" bind:cancel="__e" bind:input="__e" class="data-v-30c7270a" bind:__l="__l"></u-datetime-picker></view>