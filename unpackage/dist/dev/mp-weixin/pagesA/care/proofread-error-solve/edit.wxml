<ut-page class="page data-v-68c0200c" vue-id="a1773b0c-1" bind:__l="__l" vue-slots="{{['default']}}"><ut-top class="top-warp data-v-68c0200c" vue-id="{{('a1773b0c-2')+','+('a1773b0c-1')}}" bg-color="#fff" data-event-opts="{{[['^topHeight',[['getHeight']]]]}}" bind:topHeight="__e" bind:__l="__l" vue-slots="{{['default']}}"><f-navbar vue-id="{{('a1773b0c-3')+','+('a1773b0c-2')}}" fontColor="#fff" bgColor="{{colors}}" title="校对异常处理" navbarType="1" class="data-v-68c0200c" bind:__l="__l"></f-navbar></ut-top><view class="padding-lr-xs data-v-68c0200c"><view class="cu-card margin-lr-sm margin-tb-sm radius bg-white flex padding-sm data-v-68c0200c"><view class="flex-sub-0 margin-right-sm data-v-68c0200c"><block wx:if="{{form.imgHead}}"><u-lazy-load vue-id="{{('a1773b0c-4')+','+('a1773b0c-1')}}" image="{{$root.g0}}" width="120" height="160" border-radius="4" class="data-v-68c0200c" bind:__l="__l"></u-lazy-load></block></view><view class="flex-sub text-content data-v-68c0200c"><view class="flex justify-between align-center data-v-68c0200c"><view class="data-v-68c0200c"><view class="flex justify-start align-center data-v-68c0200c"><view class="text-df text-bold text-black data-v-68c0200c">{{form.name}}</view><block wx:if="{{form.sex==1}}"><view class="text-blue margin-left-lg data-v-68c0200c">男<text class="cuIcon-male data-v-68c0200c"></text></view></block><block wx:if="{{form.sex==2}}"><view class="text-pink margin-left-lg data-v-68c0200c">女<text class="cuIcon-female data-v-68c0200c"></text></view></block></view><view class="text-gray margin-top-xs data-v-68c0200c"><block wx:if="{{form.phone}}"><text class="cuIcon-phone margin-right-xs data-v-68c0200c"></text></block>{{''+form.phone+''}}</view></view><view class="text-right data-v-68c0200c"><view class="text-gray data-v-68c0200c">护理日期</view><view class="text-df text-bold data-v-68c0200c" style="{{'color:'+(colors)+';'}}">{{form.workDate}}</view></view></view><view class="text-xs text-gray text-cut margin-top-xs data-v-68c0200c">{{form.address}}</view><block wx:if="{{form.attendantName}}"><view class="margin-top-xs data-v-68c0200c"><text class="text-sm data-v-68c0200c" style="{{'color:'+(colors)+';'}}">{{"("+form.groupName+")"}}</text><text class="text-sm text-gray data-v-68c0200c">{{"护理员："+form.attendantName}}</text></view></block><block wx:if="{{form.idcard}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">证件号码：</text><text class="text-sm text-black data-v-68c0200c">{{form.idcard}}</text></view></block><block wx:if="{{form.schedulingDuration}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">排班时长：</text><text class="text-sm text-black data-v-68c0200c">{{form.schedulingDuration}}</text></view></block><block wx:if="{{form.totalDuration}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">实际时长：</text><text class="text-sm text-black data-v-68c0200c">{{form.totalDuration}}</text></view></block><block wx:if="{{form.checkInTime}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">签到时间：</text><text class="text-sm text-black data-v-68c0200c">{{form.checkInTime}}</text></view></block><block wx:if="{{form.checkOutTime}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">签退时间：</text><text class="text-sm text-black data-v-68c0200c">{{form.checkOutTime}}</text></view></block><block wx:if="{{form.lastManualUserName}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">校对人：</text><text class="text-sm text-black data-v-68c0200c">{{form.lastManualUserName}}</text></view></block><block wx:if="{{form.lastManualTime}}"><view class="flex align-center margin-top-xs data-v-68c0200c"><text class="text-sm text-gray margin-right-xs data-v-68c0200c">校对时间：</text><text class="text-sm text-black data-v-68c0200c">{{form.lastManualTime}}</text></view></block><block wx:if="{{form.proofreadErrorRemark}}"><view class="margin-top-xs data-v-68c0200c"><text class="text-sm text-gray data-v-68c0200c">校对异常信息：</text><text class="cu-tag sm bg-orange light radius data-v-68c0200c">{{''+form.proofreadErrorRemark+''}}</text></view></block></view></view><block wx:if="{{$root.g1}}"><view class="cu-card margin-lr-sm margin-tb-sm radius bg-white padding-sm data-v-68c0200c"><scroll-view class="scroll-box data-v-68c0200c" scroll-y="true"><block wx:for="{{datas}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="text-content padding-bottom-sm data-v-68c0200c"><view class="text-bold text-lg data-v-68c0200c">{{item.title}}</view><block wx:for="{{item.details}}" wx:for-item="detail" wx:for-index="dIndex" wx:key="dIndex"><view class="padding-left-lg data-v-68c0200c"><view class="margin-left-xs data-v-68c0200c"><text class="data-v-68c0200c">{{detail.title}}</text><block wx:if="{{detail.require}}"><text class="text-xs margin-left-xs data-v-68c0200c" style="{{'color:'+(colors)+';'}}">(必填)</text></block><block wx:else><text class="text-xs margin-left-xs data-v-68c0200c">(选填)</text></block></view><view class="margin-top-sm data-v-68c0200c"><ut-image-upload vue-id="{{('a1773b0c-5-'+index+'-'+dIndex)+','+('a1773b0c-1')}}" name="file" mediaType="image" colors="{{colors}}" max="{{20}}" headers="{{headers}}" action="{{uploadInfo.server+uploadInfo.single||''}}" preview-image-width="{{1200}}" width="{{200}}" height="{{160}}" border-radius="{{8}}" disabled="{{false}}" add="{{true}}" remove="{{true}}" data-ref="upload" value="{{detail.files}}" data-event-opts="{{[['^uploadSuccess',[['uploadFaceSuccess',['$event','$0'],[[['datas','',index],['details','',dIndex]]]]]],['^input',[['__set_model',['$0','files','$event',[]],[[['datas','',index],['details','',dIndex]]]]]]]}}" bind:uploadSuccess="__e" bind:input="__e" class="data-v-68c0200c vue-ref-in-for" bind:__l="__l"></ut-image-upload></view></view></block></view></block></scroll-view></view></block><view class="cu-card margin-lr-sm margin-tb-sm radius shadow bg-white padding-sm data-v-68c0200c"><view class="flex justify-between align-center margin-bottom-sm data-v-68c0200c"><u-button vue-id="{{('a1773b0c-6')+','+('a1773b0c-1')}}" type="error" text="重置" plain="{{true}}" size="small" custom-style="{{({marginRight:'20rpx'})}}" data-event-opts="{{[['^click',[['resetProjects']]]]}}" bind:click="__e" class="data-v-68c0200c" bind:__l="__l"></u-button><u-button vue-id="{{('a1773b0c-7')+','+('a1773b0c-1')}}" type="primary" color="{{colors}}" text="添加项目" plain="{{true}}" size="small" data-event-opts="{{[['^click',[['addProject']]]]}}" bind:click="__e" class="data-v-68c0200c" bind:__l="__l"></u-button></view><u-swipe-action vue-id="{{('a1773b0c-8')+','+('a1773b0c-1')}}" data-ref="swipeProjectList" class="data-v-68c0200c vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><u-swipe-action-item vue-id="{{('a1773b0c-9-'+__i0__)+','+('a1773b0c-8')}}" name="{{item.$orig.projectId}}" options="{{item.m0}}" data-event-opts="{{[['^click',[['projectActionClick']]]]}}" bind:click="__e" class="data-v-68c0200c" bind:__l="__l" vue-slots="{{['default']}}"><view class="padding-tb-sm solid-bottom data-v-68c0200c"><project-item vue-id="{{('a1773b0c-10-'+__i0__)+','+('a1773b0c-9-'+__i0__)}}" detail="{{item.$orig}}" colors="{{colors}}" class="data-v-68c0200c" bind:__l="__l"></project-item></view></u-swipe-action-item></block></u-swipe-action><block wx:if="{{$root.g2}}"><view class="text-center padding-xl data-v-68c0200c"><text class="text-gray data-v-68c0200c">无服务项目</text></view></block></view></view><ut-fixed vue-id="{{('a1773b0c-11')+','+('a1773b0c-1')}}" safe-area-inset="{{true}}" position="bottom" background="#fff" class="data-v-68c0200c" bind:__l="__l" vue-slots="{{['default']}}"><view class="padding-tb-sm padding-lr-lg data-v-68c0200c"><u-button vue-id="{{('a1773b0c-12')+','+('a1773b0c-11')}}" type="primary" color="{{colors}}" loading="{{loading}}" text="保存" shape="circle" data-event-opts="{{[['^click',[['save']]]]}}" bind:click="__e" class="data-v-68c0200c" bind:__l="__l"></u-button></view></ut-fixed><u-popup vue-id="{{('a1773b0c-13')+','+('a1773b0c-1')}}" show="{{showProjectPopup}}" mode="bottom" round="10" closeable="{{true}}" safe-area-inset-bottom="{{false}}" mask-close-able="{{true}}" close-icon-pos="top-left" z-index="{{1025}}" overlay-style="{{({zIndex:998})}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" class="data-v-68c0200c" bind:__l="__l" vue-slots="{{['default']}}"><view class="text-center padding-tb text-df text-bold text-black data-v-68c0200c">项目选择</view><project-selector vue-id="{{('a1773b0c-14')+','+('a1773b0c-13')}}" project-data="{{projectData}}" selected-projects="{{form.projects}}" colors="{{colors}}" data-event-opts="{{[['^selectProject',[['selectProject']]]]}}" bind:selectProject="__e" class="data-v-68c0200c" bind:__l="__l"></project-selector></u-popup></ut-page>