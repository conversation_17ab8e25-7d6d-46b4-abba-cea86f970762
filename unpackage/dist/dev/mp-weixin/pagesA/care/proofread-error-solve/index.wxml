<ut-page vue-id="2e784dc6-1" class="data-v-1e08c980" bind:__l="__l" vue-slots="{{['default']}}"><ut-top class="top-warp data-v-1e08c980" vue-id="{{('2e784dc6-2')+','+('2e784dc6-1')}}" bg-color="#fff" data-event-opts="{{[['^topHeight',[['getHeight']]]]}}" bind:topHeight="__e" bind:__l="__l" vue-slots="{{['default']}}"><f-navbar vue-id="{{('2e784dc6-3')+','+('2e784dc6-2')}}" fontColor="#fff" bgColor="{{colors}}" title="校对异常处理" navbarType="1" class="data-v-1e08c980" bind:__l="__l"></f-navbar><view class="padding-tb-sm padding-lr-sm flex align-center data-v-1e08c980" style="{{'background-color:'+('#fff')+';'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="month-selector-wrapper margin-right-sm data-v-1e08c980" bindtap="__e"><view class="u-border round padding-tb-sm padding-lr-lg flex align-center justify-between data-v-1e08c980" style="height:70rpx;"><view class="text-grey text-bold data-v-1e08c980">{{monthDisplayText}}</view><block wx:if="{{monthValue}}"><view data-event-opts="{{[['tap',[['clearMonth',['$event']]]]]}}" class="clear-btn data-v-1e08c980" catchtap="__e"><u-icon vue-id="{{('2e784dc6-4')+','+('2e784dc6-2')}}" name="close" size="14" color="#c0c4cc" class="data-v-1e08c980" bind:__l="__l"></u-icon></view></block></view></view><u-input bind:input="__e" vue-id="{{('2e784dc6-5')+','+('2e784dc6-2')}}" prefixIcon="search" placeholder="输入客户或护理员姓名" shape="circle" border="surround" clearable="{{true}}" value="{{search.key}}" data-event-opts="{{[['^input',[['__set_model',['$0','key','$event',[]],['search']]]]]}}" class="data-v-1e08c980" bind:__l="__l" vue-slots="{{['suffix']}}"><view slot="suffix" class="data-v-1e08c980"><u-button vue-id="{{('2e784dc6-6')+','+('2e784dc6-5')}}" text="搜索" type="success" size="mini" data-event-opts="{{[['^click',[['onSearch']]]]}}" bind:click="__e" class="data-v-1e08c980" bind:__l="__l"></u-button></view></u-input></view></ut-top><mescroll-body vue-id="{{('2e784dc6-7')+','+('2e784dc6-1')}}" top="{{topWrapHeight+'px'}}" top-margin="{{-topWrapHeight+'px'}}" bottom="20" up="{{upOption}}" safearea="{{true}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]],['^emptyclick',[['emptyClick']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" bind:emptyclick="__e" class="data-v-1e08c980 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToEdit',['$0'],[[['dataList','',index]]]]]]]}}" bindtap="__e" class="data-v-1e08c980"><view class="item-box data-v-1e08c980"><view class="head-image data-v-1e08c980"><block wx:if="{{item.$orig.imgHead}}"><u-lazy-load vue-id="{{('2e784dc6-8-'+index)+','+('2e784dc6-7')}}" image="{{item.g0}}" width="120" height="160" border-radius="4" class="data-v-1e08c980" bind:__l="__l"></u-lazy-load></block><block wx:else><text class="cuIcon-people data-v-1e08c980"></text></block></view><view class="content text-content data-v-1e08c980"><view class="flex justify-between align-center data-v-1e08c980"><view class="data-v-1e08c980"><view class="flex justify-start align-center flex-wrap data-v-1e08c980"><view class="text-df text-bold margin-right-xs data-v-1e08c980">{{item.$orig.name}}</view><view class="{{['data-v-1e08c980',(true)?'cu-tag sm radius light':'',(item.$orig.proofreadError)?'bg-orange':'',(!item.$orig.proofreadError&&item.$orig.isManual)?'bg-green':'',(!item.$orig.proofreadError&&!item.$orig.isManual)?'bg-blue':'']}}">{{''+item.m0+''}}</view></view><view class="text-sm text-gray margin-top-xs data-v-1e08c980">{{'手机号：'+item.$orig.phone+''}}</view><view class="text-sm text-gray margin-top-xs data-v-1e08c980">{{'护理日期：'+item.$orig.workDate+''}}</view></view></view><view class="text-sm text-gray margin-top-xs data-v-1e08c980">{{'护理员：'+item.$orig.attendantName+"（"+item.$orig.groupName+'）'}}</view><view class="text-sm text-gray margin-top-xs data-v-1e08c980">签到时间：<block wx:if="{{item.$orig.checkInTime}}"><text class="text-black data-v-1e08c980">{{item.$orig.checkInTime}}</text></block><block wx:else><text class="text-red data-v-1e08c980">未签到</text></block></view><view class="text-sm text-gray margin-top-xs data-v-1e08c980">签退时间：<block wx:if="{{item.$orig.checkOutTime}}"><text class="text-black data-v-1e08c980">{{item.$orig.checkOutTime}}</text></block><block wx:else><text class="text-red data-v-1e08c980">未签退</text></block></view><view class="text-sm text-gray margin-top-xs data-v-1e08c980"><text class="data-v-1e08c980">项目数量：</text><block wx:if="{{item.$orig.projectCount&&item.$orig.projectCount>0}}"><text class="text-black data-v-1e08c980">{{''+item.$orig.projectCount+''}}</text></block><block wx:else><text class="text-red data-v-1e08c980">无</text></block><text class="margin-left-xs data-v-1e08c980">文件数量：</text><block wx:if="{{item.$orig.dataCount&&item.$orig.dataCount>0}}"><text class="text-black data-v-1e08c980">{{''+item.$orig.dataCount+''}}</text></block><block wx:else><text class="text-red data-v-1e08c980">无</text></block></view><block wx:if="{{item.$orig.isManual}}"><view class="text-sm text-gray margin-top-xs data-v-1e08c980">{{'校对人：'+item.$orig.lastManualUserName+''}}</view><view class="text-sm text-gray margin-top-xs data-v-1e08c980">{{'校对时间：'+item.$orig.lastManualTime+''}}</view></block></view></view></view></block></mescroll-body><u-datetime-picker vue-id="{{('2e784dc6-9')+','+('2e784dc6-1')}}" show="{{showMonthPicker}}" mode="year-month" title="选择月份" closeOnClickOverlay="{{true}}" value="{{monthValue}}" data-event-opts="{{[['^confirm',[['onMonthConfirm']]],['^close',[['e1']]],['^cancel',[['e2']]],['^input',[['__set_model',['','monthValue','$event',[]]]]]]}}" bind:confirm="__e" bind:close="__e" bind:cancel="__e" bind:input="__e" class="data-v-1e08c980" bind:__l="__l"></u-datetime-picker><ut-login-modal vue-id="{{('2e784dc6-10')+','+('2e784dc6-1')}}" colors="{{colors}}" class="data-v-1e08c980" bind:__l="__l"></ut-login-modal></ut-page>