<template>
    <ut-page class="page">
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" title="校对异常处理" navbarType="1"></f-navbar>
        </ut-top>

        <view class="padding-lr-xs">
            <!-- 客户信息 -->
            <view class="cu-card margin-lr-sm margin-tb-sm radius bg-white flex padding-sm">
                <view class="flex-sub-0 margin-right-sm">
                    <u-lazy-load v-if="form.imgHead"
                        :image="$tools.showImg(form.imgHead)"
                        width="120"
                        height="160"
                        border-radius="4" />
                </view>
                <view class="flex-sub text-content">
                    <view class="flex justify-between align-center">
                        <view>
                            <view class="flex justify-start align-center">
                                <view class="text-df text-bold text-black">{{ form.name }}</view>
                                <view class="text-blue margin-left-lg" v-if="form.sex == 1">男
                                    <text class="cuIcon-male" />
                                </view>
                                <view class="text-pink margin-left-lg" v-if="form.sex == 2">女
                                    <text class="cuIcon-female" />
                                </view>
                            </view>
                            <view class="text-gray margin-top-xs">
                                <text v-if="form.phone" class="cuIcon-phone margin-right-xs" />
                                {{ form.phone }}
                            </view>
                        </view>
                        <view class="text-right">
                            <view class="text-gray">护理日期</view>
                            <view class="text-df text-bold" :style="{ color: colors }">{{ form.workDate }}</view>
                        </view>
                    </view>

                    <view class="text-xs text-gray text-cut margin-top-xs">{{ form.address }}</view>
                    <view v-if="form.attendantName" class="margin-top-xs">
                        <text class="text-sm" :style="{ color: colors }">({{ form.groupName }})</text>
                        <text class="text-sm text-gray">护理员：{{ form.attendantName }}</text>
                    </view>

                    <view v-if="form.idcard" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">证件号码：</text>
                        <text class="text-sm text-black">{{ form.idcard }}</text>
                    </view>

                    <!-- 额外信息 -->
                    <view v-if="form.schedulingDuration" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">排班时长：</text>
                        <text class="text-sm text-black">{{ form.schedulingDuration }}</text>
                    </view>

                    <view v-if="form.totalDuration" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">实际时长：</text>
                        <text class="text-sm text-black">{{ form.totalDuration }}</text>
                    </view>

                    <view v-if="form.checkInTime" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">签到时间：</text>
                        <text class="text-sm text-black">{{ form.checkInTime }}</text>
                    </view>

                    <view v-if="form.checkOutTime" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">签退时间：</text>
                        <text class="text-sm text-black">{{ form.checkOutTime }}</text>
                    </view>

                    <view v-if="form.lastManualUserName" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">校对人：</text>
                        <text class="text-sm text-black">{{ form.lastManualUserName }}</text>
                    </view>

                    <view v-if="form.lastManualTime" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">校对时间：</text>
                        <text class="text-sm text-black">{{ form.lastManualTime }}</text>
                    </view>

                    <view v-if="form.proofreadErrorRemark" class="margin-top-xs">
                        <text class="text-sm text-gray">校对异常信息：</text>
                        <text class="cu-tag sm bg-orange light radius">
                            {{ form.proofreadErrorRemark }}
                        </text>
                    </view>
                </view>
            </view>

            <!-- 服务资料 -->
            <view v-if="datas && datas.length > 0" class="cu-card margin-lr-sm margin-tb-sm radius bg-white padding-sm">
                <scroll-view scroll-y="true" class="scroll-box">
                    <view v-for="(item, index) in datas" :key="index" class="text-content padding-bottom-sm">
                        <view class="text-bold text-lg">{{ item.title }}</view>
                        <view v-for="(detail, dIndex) in item.details" :key="dIndex" class="padding-left-lg">
                            <view class="margin-left-xs">
                                <text>{{ detail.title }}</text>
                                <text v-if="detail.require"
                                    class="text-xs margin-left-xs"
                                    :style="{ color: colors }">(必填)
                                </text>
                                <text v-else class="text-xs margin-left-xs">(选填)</text>
                            </view>
                            <view class="margin-top-sm">
                                <ut-image-upload
                                    ref="upload"
                                    name="file"
                                    v-model="detail.files"
                                    mediaType="image"
                                    :colors="colors"
                                    :max="20"
                                    :headers="headers"
                                    :action="uploadInfo.server + uploadInfo.single || ''"
                                    :preview-image-width="1200"
                                    :width="200"
                                    :height="160"
                                    :border-radius="8"
                                    :disabled="false"
                                    :add="true"
                                    :remove="true"
                                    @uploadSuccess="uploadFaceSuccess($event, detail)">
                                </ut-image-upload>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>

            <!-- 服务项目 -->
            <view class="cu-card margin-lr-sm margin-tb-sm radius shadow bg-white padding-sm">
                <view class="flex justify-between align-center margin-bottom-sm">
                    <u-button
                        type="error"
                        text="重置"
                        :plain="true"
                        size="small"
                        @click="resetProjects"
                        :custom-style="{ marginRight: '20rpx' }"
                    />
                    <u-button
                        type="primary"
                        :color="colors"
                        text="添加项目"
                        :plain="true"
                        size="small"
                        @click="addProject"
                    />
                </view>
                <u-swipe-action ref="swipeProjectList">
                    <u-swipe-action-item v-for="item in orderProjects" :key="item.id"
                        :name="item.projectId"
                        :options="getProjectActionOptions(item)"
                        @click="projectActionClick">
                        <view class="padding-tb-sm solid-bottom">
                            <project-item :detail="item" :colors="colors"></project-item>
                        </view>
                    </u-swipe-action-item>
                </u-swipe-action>
                <view v-if="!form.projects || !form.projects.length" class="text-center padding-xl">
                    <text class="text-gray">无服务项目</text>
                </view>
            </view>

        </view>

        <!-- 底部保存按钮 -->
        <ut-fixed safe-area-inset position="bottom" background="#fff">
            <view class="padding-tb-sm padding-lr-lg">
                <u-button
                    type="primary"
                    :color="colors"
                    :loading="loading"
                    @click="save"
                    text="保存"
                    shape="circle"
                ></u-button>
            </view>
        </ut-fixed>

        <!-- 项目选择弹窗 -->
        <u-popup
            :show="showProjectPopup"
            mode="bottom"
            round="10"
            :closeable="true"
            :safe-area-inset-bottom="false"
            :mask-close-able="true"
            close-icon-pos="top-left"
            :z-index="1025"
            :overlay-style="{zIndex:998}"
            @close="showProjectPopup=false"
        >
            <view class="text-center padding-tb text-df text-bold text-black">项目选择</view>
            <project-selector
                :project-data="projectData"
                :selected-projects="form.projects"
                :colors="colors"
                @selectProject="selectProject"
            />
        </u-popup>
    </ut-page>
</template>

<script>
let app = getApp()
import { mapState } from 'vuex'
import ProjectItem from '@/pagesA/components/project-item.vue'
import ProjectSelector from '@/pagesA/components/proofread/project-selector.vue'

export default {
    components: {
        ProjectItem,
        ProjectSelector,
    },
    data() {
        return {
            colors: '',
            topWrapHeight: 0,
            workId: '',
            month: '',
            loading: false,
            form: {},
            datas: [],
            showProjectPopup: false,
            projectData: [],
            initialProjects: [],
        }
    },
    computed: {
        ...mapState({
            community: state => state.init.community,
            uploadInfo: state => state.init.oss,
        }),
        headers() {
            return {
                Token: this.uploadInfo.token,
            }
        },
        orderProjects() {
            return this.form.projects?.toSorted((a, b) => a.govCode - b.govCode) ?? []
        },
    },
    onLoad(options) {
        this.workId = options.workId || ''
        this.month = options.month || ''
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.getInfo()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        async getInfo() {
            if (!this.workId) return
            this.loading = true
            const params = {
                communityId: this.community.id,
                workId: this.workId,
            }
            if (this.month) params.month = this.month
            try {
                const { data } = await this.$ut.api('mang/care/proofread/task', params)
                this.form = {
                    ...this.form,
                    ...data,
                    projects: data.projects?.map((item) => ({
                        ...item,
                        id: item.projectId,
                        projectId: item.projectId,
                        projectName: item.projectName,
                        govCode: item.govCode,
                        requireMinDuration: item.requireMinDuration,
                        requireMaxDuration: item.requireMaxDuration,
                        _id: item.id,
                    })),
                }
                this.datas = data.datas || []
                // 保存初始项目状态用于重置功能
                if (data.projects && data.projects.length > 0) {
                    this.initialProjects = JSON.parse(JSON.stringify(this.form.projects))
                }
                this.datas.forEach(item => {
                    if (item.details && item.details.length > 0) {
                        item.details.forEach(detail => {
                            if (!detail.files) {
                                this.$set(detail, 'files', [])
                            }
                        })
                    }
                })
            } finally {
                this.loading = false
            }
        },
        async save() {
            this.loading = true
            try {
                const datas = this.datas.flatMap((item) =>
                    item.details ? item.details.map((detail) => ({
                        dataId: detail.id,
                        urls: detail.files.map((file) => file.url),
                    })) : [],
                )
                await this.$ut.api('mang/care/proofread/errorSave', {
                    communityId: this.community.id,
                    workId: this.workId,
                    projectIds: this.form.projects ? this.form.projects.map(p => p.projectId) : [],
                    datas,
                })
                this.$u.toast('保存成功')
                setTimeout(() => this.goBack(), 500)
            } finally {
                this.loading = false
            }
        },

        goBack() {
            uni.navigateBack()
        },
        async loadProjectData() {
            const { data } = await this.$ut.api('mang/care/work/project/allRuleListpg', {
                communityId: this.community.id,
                workId: this.workId,
            })
            this.projectData = data?.info || []
        },
        addProject() {
            this.loadProjectData()
            this.showProjectPopup = true
        },
        resetProjects() {
            uni.showModal({
                title: '确认重置',
                content: '确定要将服务项目重置为初始状态吗？',
                success: (res) => {
                    if (res.confirm) {
                        if (this.initialProjects?.length > 0) {
                            this.form.projects = JSON.parse(JSON.stringify(this.initialProjects))
                        } else {
                            this.form.projects = []
                        }
                    }
                },
            })
        },

        selectProject(projects) {
            this.showProjectPopup = false
            if (!this.form.projects) {
                this.$set(this.form, 'projects', [])
            }
            if (!projects || !projects.length) return
            projects.forEach(project => {
                const existingProject = this.form.projects.find(p => p.projectId === project.id)
                if (!existingProject) {
                    this.form.projects.push({
                        id: project.id,
                        projectId: project.id,
                        projectName: project.name,
                        projectCode: project.code,
                        govCode: project.govCode,
                        requireMinDuration: project.minDuration,
                        requireMaxDuration: project.maxDuration,
                        remark: project.remark || '',
                    })
                }
            })
        },
        getProjectActionOptions() {
            const btnDelete = {
                text: '删除',
                code: 'delete',
                style: {
                    backgroundColor: '#f56c6c',
                },
            }
            return [btnDelete]
        },
        projectActionClick(data) {
            if (data.code === 'delete') {
                this.removeProject(data.name)
            }
            this.$refs['swipeProjectList'].closeAll()
        },
        removeProject(projectId) {
            if (this.form.projects && this.form.projects.length) {
                const index = this.form.projects.findIndex(p => p.projectId === projectId)
                if (index >= 0) {
                    this.form.projects.splice(index, 1)
                }
            }
        },
        uploadFaceSuccess(res, detail) {
            res.forEach((img) => {
                const item = img.data
                detail.files.push({
                    name: item.source,
                    url: `${this.uploadInfo.preview}?file=${item.name}${item.ext}`,
                })
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.page {
  padding-bottom: 128rpx;
}

.pop-title {
  padding-top: 20rpx;
  text-align: center;
}
</style>
