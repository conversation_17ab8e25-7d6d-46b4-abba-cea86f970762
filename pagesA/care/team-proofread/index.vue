<template>
    <ut-page>
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <proofread-header
                :colors="colors"
                :title="title"
                :month="month"
                :search="search"
                @search="onSearch"
                @month-change="onMonthConfirm"
            />
        </ut-top>

        <mescroll-body
            ref="mescrollRef"
            :top="topWrapHeight+'px'"
            :top-margin="-topWrapHeight+'px'"
            bottom="20"
            :up="upOption"
            :safearea="true"
            @init="mescrollInit"
            @down="downCallback"
            @up="upCallback"
            @emptyclick="emptyClick"
        >
            <proofread-list
                :dataList="dataList"
                :checkPagePath="checkPagePath"
                @item-click="goToCheck"
            />
        </mescroll-body>

        <ut-login-modal :colors="colors"></ut-login-modal>
    </ut-page>
</template>

<script>
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import ProofreadHeader from '@/pagesA/components/proofread/proofread-header.vue'
import ProofreadList from '@/pagesA/components/proofread/proofread-list.vue'

const app = getApp()

export default {
    mixins: [MescrollMixin],
    components: {
        MescrollBody,
        ProofreadHeader,
        ProofreadList,
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            colors: '',
            apiPath: 'mang/care',
            checkPagePath: '/pagesA/care/team-proofread/check',
            title: '校对任务列表',
            mescroll: null,
            currentFilter: 0,
            search: {
                key: '',
                state: 0,
            },
            topWrapHeight: 0,
            filterTabs: [
                { name: '可校对' },
                { name: '未校对' },
                { name: '已校对' },
                { name: '无法校对' },
            ],
            upOption: {
                page: {
                    num: 0,
                    size: 10,
                },
                noMoreSize: 3,
                textNoMore: '没有更多数据了',
                empty: {
                    tip: '暂无校对任务',
                },
            },
            pageReq: {
                pageindex: 1,
                pagesize: 10,
            },
            dataList: [],
            firstLoad: true,
        }
    },
    computed: {
        ...mapState({
            community: state => state.init.community,
        }),
        month() {
            const date = new Date()
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        },
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.downCallback()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        mescrollInit(mescroll) {
            this.mescroll = mescroll
        },
        downCallback() {
            this.pageReq.pageindex = 1
            this.mescroll.resetUpScroll()
        },
        async upCallback(page) {
            const params = {
                communityId: this.community.id,
                pageindex: page.num,
                pagesize: page.size,
                ...this.search,
                month: this.month,
            }
            const { data } = await this.$ut.api(`${this.apiPath}/proofread/listpg`, params)
            setTimeout(() => {
                this.mescroll.endBySize(data.info.length, data.record)
            }, this.firstLoad ? 0 : 500)
            this.firstLoad = false
            if (page.num === 1) {
                this.dataList = []
            }
            this.dataList = this.dataList.concat(data.info)
        },
        emptyClick() {
            this.mescroll.resetUpScroll()
        },
        onSearch(searchData) {
            this.search = { ...searchData }
            this.downCallback()
        },
        onMonthConfirm({ value }) {
            this.downCallback()
        },
        goToCheck(item) {
            this.$tools.routerTo(this.checkPagePath, {
                workId: item.id,
                month: this.month,
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.filter-bar {
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
</style>
