<template>
    <view>
        <view v-for="(item, index) in dataList" :key="index" @click="onItemClick(item)">
            <view class="item-box">
                <view class="head-image">
                    <u-lazy-load v-if="item.imgHead"
                        :image="$tools.showImg(item.imgHead)"
                        width="120"
                        height="160"
                        border-radius="4" />
                    <text v-else class="cuIcon-people"></text>
                </view>
                <view class="content text-content">
                    <view class="flex justify-between align-center">
                        <view>
                            <view class="flex justify-start align-center flex-wrap">
                                <view class="text-df text-bold margin-right-xs">{{ item.name }}</view>
                                <view :class="{
                                    'cu-tag sm radius light': true,
                                    'bg-orange': item.proofreadError,
                                    'bg-green': !item.proofreadError && item.isManual,
                                    'bg-blue': !item.proofreadError && !item.isManual,
                                }">
                                    {{ getStatusText(item) }}
                                </view>
                            </view>

                            <view class="text-sm text-gray margin-top-xs">
                                手机号：{{ item.phone }}
                            </view>
                            <view class="text-sm text-gray margin-top-xs">
                                护理日期：{{ item.workDate }}
                            </view>
                        </view>
                    </view>
                    <view class="text-sm text-gray margin-top-xs">
                        护理员：{{ item.attendantName }}（{{ item.groupName }}）
                    </view>
                    <template v-if="item.isManual">
                        <view class="text-sm text-gray margin-top-xs">
                            校对人：{{ item.lastManualUserName }}
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            校对时间：{{ item.lastManualTime }}
                        </view>
                    </template>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'ProofreadList',
    props: {
        dataList: {
            type: Array,
            default: () => [],
        },
        checkPagePath: {
            type: String,
            required: true,
        },
    },
    options: {
        styleIsolation: 'shared',
    },
    methods: {
        getStatusText(item) {
            if (item.proofreadError) {
                return item.proofreadErrorRemark ?? '异常'
            }
            return item.isManual ? '已校对' : '待校对'
        },
        onItemClick(item) {
            this.$emit('item-click', item)
        },
    },
}
</script>

<style lang="scss" scoped>
.item-box {
  padding: 20rpx 30rpx;
  background-color: white;
  margin-bottom: 10rpx;
  display: flex;
  position: relative;
  overflow: hidden;

  .content {
    flex: 1;
    padding: 0 20rpx;
  }

  &:active {
    transform: scale(0.98);
    transition: all 0.3s ease;
  }
}

.cuIcon-people {
  font-size: 116rpx;
  color: gray;
  border: 1rpx solid #ccc;
  width: 116rpx;
  height: 156rpx;
  line-height: 156rpx;
  border-radius: 6rpx;
  display: block;
}
</style>
