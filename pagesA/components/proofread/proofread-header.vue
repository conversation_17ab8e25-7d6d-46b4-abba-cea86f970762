<template>
    <view>
        <f-navbar fontColor="#fff" :bgColor="colors" :title="title" navbarType="1"></f-navbar>

        <view :style="{backgroundColor:'#fff'}" class="padding-top-sm padding-lr-sm flex align-center">
            <view class="u-border margin-right-sm round padding-tb-sm padding-lr-lg"
                @click="showMonthPicker = true">
                <view class="text-grey text-bold">{{ month }}</view>
            </view>
            <u-input
                prefixIcon="search"
                placeholder="输入客户或护理员姓名"
                v-model="searchData.key"
                shape='circle'
                border="surround"
                clearable
            >
                <template slot="suffix">
                    <u-button
                        text="搜索"
                        type="success"
                        size="mini"
                        @click="onSearch"
                    ></u-button>
                </template>
            </u-input>
        </view>

        <u-datetime-picker
            :show="showMonthPicker"
            v-model="monthValue"
            mode="year-month"
            title="选择月份"
            :closeOnClickOverlay="true"
            @confirm="onMonthConfirm"
            @close="showMonthPicker = false"
            @cancel="showMonthPicker = false"
        ></u-datetime-picker>
    </view>
</template>

<script>
export default {
    name: 'ProofreadHeader',
    props: {
        colors: {
            type: String,
            default: '',
        },
        title: {
            type: String,
            default: '校对任务列表',
        },
        month: {
            type: String,
            default: '',
        },
        search: {
            type: Object,
            default: () => ({
                key: '',
                state: 0,
            }),
        },
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            showMonthPicker: false,
            monthValue: Date.now(),
            searchData: { ...this.search },
        }
    },
    watch: {
        search: {
            handler(newVal) {
                this.searchData = { ...newVal }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        onSearch() {
            this.$emit('search', this.searchData)
        },
        onMonthConfirm({ value }) {
            this.monthValue = value
            this.showMonthPicker = false
            this.$emit('month-change', { value })
        },
    },
}
</script>

<style lang="scss" scoped>
// 这里可以添加header特有的样式，目前主要使用项目已有的样式class
</style>
